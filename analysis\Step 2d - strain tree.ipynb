{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Plotting tree showing taxonomic relation of strains\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>species_HPLCname</th>\n", "      <th>species</th>\n", "      <th>new_species</th>\n", "      <th>species.1</th>\n", "      <th>species_short</th>\n", "      <th>new_genus</th>\n", "      <th>genus</th>\n", "      <th>new_family</th>\n", "      <th>family</th>\n", "      <th>new_order</th>\n", "      <th>order</th>\n", "      <th>new_class</th>\n", "      <th>class</th>\n", "      <th>new_phylum</th>\n", "      <th>phylum</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON>.uniformis</td>\n", "      <td>Bacteroides uniformis</td>\n", "      <td>Bacteroides uniformis</td>\n", "      <td>Bacteroides uniformis</td>\n", "      <td><PERSON>. uniformis</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON><PERSON>fragilis</td>\n", "      <td>Bacteroides fragilis</td>\n", "      <td>Bacteroides fragilis</td>\n", "      <td>Bacteroides fragilis</td>\n", "      <td><PERSON><PERSON> fragilis</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON><PERSON>ovatus</td>\n", "      <td>Bacteroides ovatus</td>\n", "      <td>Bacteroides ovatus</td>\n", "      <td>Bacteroides ovatus</td>\n", "      <td><PERSON><PERSON> ovatus</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON>.theta</td>\n", "      <td>Bacteroides thetaiotaomicron</td>\n", "      <td>Bacteroides thetaiotaomicron</td>\n", "      <td>Bacteroides thetaiotaomicron</td>\n", "      <td><PERSON>. theta</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Bacteroides <PERSON>ii</td>\n", "      <td>Bacteroides <PERSON>ii</td>\n", "      <td>Bacteroides <PERSON>ii</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td><PERSON><PERSON>vulgatus</td>\n", "      <td>Phocaeicola vulgatus</td>\n", "      <td>Phocaeicola vulgatus</td>\n", "      <td>Bacteroides vulgatus</td>\n", "      <td><PERSON><PERSON> vulgatus</td>\n", "      <td>Phocaeicola</td>\n", "      <td>Phocaeicola</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td><PERSON><PERSON>copri</td>\n", "      <td>Prevotella copri</td>\n", "      <td>Prevotella copri</td>\n", "      <td>Prevotella copri</td>\n", "      <td><PERSON><PERSON> copri</td>\n", "      <td>Prevotella</td>\n", "      <td>Prevotella</td>\n", "      <td>Prevotellaceae</td>\n", "      <td>Prevotellaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td><PERSON><PERSON>di<PERSON></td>\n", "      <td>Parabacteroides distasonis</td>\n", "      <td>Parabacteroides distasonis</td>\n", "      <td>Bacteroides distasonis</td>\n", "      <td><PERSON><PERSON> di<PERSON></td>\n", "      <td>Parabacteroides</td>\n", "      <td>Parabacteroides</td>\n", "      <td>Tannerellaceae</td>\n", "      <td>Tannerellaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td><PERSON><PERSON>intestinalis</td>\n", "      <td><PERSON><PERSON><PERSON> intestinalis</td>\n", "      <td><PERSON><PERSON><PERSON> intestinalis</td>\n", "      <td><PERSON><PERSON><PERSON> intestinalis</td>\n", "      <td><PERSON><PERSON> intestinalis</td>\n", "      <td>Roseburia</td>\n", "      <td>Roseburia</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Clostridia</td>\n", "      <td>Clostridia</td>\n", "      <td>Bacillota</td>\n", "      <td>Firmicutes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td><PERSON><PERSON>rectale</td>\n", "      <td>Eubacterium rectale</td>\n", "      <td>Agathobacter rectalis</td>\n", "      <td>Eubacterium rectale</td>\n", "      <td><PERSON><PERSON> rectale</td>\n", "      <td>Agathobacter</td>\n", "      <td>Lachnospiraceae_NA</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Clostridia</td>\n", "      <td>Clostridia</td>\n", "      <td>Bacillota</td>\n", "      <td>Firmicutes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td><PERSON><PERSON>eligens</td>\n", "      <td>Lachnospira eligens</td>\n", "      <td>Lachnospira eligens</td>\n", "      <td>Lachnospira eligens</td>\n", "      <td><PERSON><PERSON> eligens</td>\n", "      <td>Lachnospira</td>\n", "      <td>Lachnospira</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Clostridia</td>\n", "      <td>Clostridia</td>\n", "      <td>Bacillota</td>\n", "      <td>Firmicutes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td><PERSON><PERSON>long<PERSON>a</td>\n", "      <td><PERSON><PERSON> long<PERSON>a</td>\n", "      <td><PERSON><PERSON> long<PERSON>a</td>\n", "      <td><PERSON><PERSON> long<PERSON>a</td>\n", "      <td><PERSON><PERSON> long<PERSON>a</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Clostridia</td>\n", "      <td>Clostridia</td>\n", "      <td>Bacillota</td>\n", "      <td>Firmicutes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>F.saccharivorans</td>\n", "      <td>Fusicatenibacter saccharivorans</td>\n", "      <td>Fusicatenibacter saccharivorans</td>\n", "      <td>Fusicatenibacter saccharivorans</td>\n", "      <td>F. saccharivorans</td>\n", "      <td>Fusicatenibacter</td>\n", "      <td>Fusicatenibacter</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Clostridia</td>\n", "      <td>Clostridia</td>\n", "      <td>Bacillota</td>\n", "      <td>Firmicutes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td><PERSON><PERSON>we<PERSON></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Clostridia</td>\n", "      <td>Clostridia</td>\n", "      <td>Bacillota</td>\n", "      <td>Firmicutes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td><PERSON><PERSON>hydrogenotrophica</td>\n", "      <td><PERSON><PERSON><PERSON> hydrogenotrophica</td>\n", "      <td><PERSON><PERSON><PERSON> hydrogenotrophica</td>\n", "      <td><PERSON><PERSON><PERSON> hydrogenotrophica</td>\n", "      <td><PERSON><PERSON> hydrogenotrophica</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Clostridia</td>\n", "      <td>Clostridia</td>\n", "      <td>Bacillota</td>\n", "      <td>Firmicutes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Faecalibacterium <PERSON>ii</td>\n", "      <td>Faecalibacterium <PERSON>ii</td>\n", "      <td>Faecalibacterium <PERSON>ii</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Faecalibacterium</td>\n", "      <td>Faecalibacterium</td>\n", "      <td>Oscillospiraceae</td>\n", "      <td>Oscillospiraceae</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Clostridia</td>\n", "      <td>Clostridia</td>\n", "      <td>Bacillota</td>\n", "      <td>Firmicutes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td><PERSON><PERSON>sir<PERSON></td>\n", "      <td>Eubacterium siraeum</td>\n", "      <td>Eubacterium siraeum</td>\n", "      <td>Eubacterium siraeum</td>\n", "      <td><PERSON><PERSON> sir<PERSON></td>\n", "      <td>[Eubacterium]</td>\n", "      <td>Oscillospiraceae_NA</td>\n", "      <td>Oscillospiraceae</td>\n", "      <td>Oscillospiraceae</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Clostridia</td>\n", "      <td>Clostridia</td>\n", "      <td>Bacillota</td>\n", "      <td>Firmicutes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td><PERSON><PERSON>br<PERSON></td>\n", "      <td>Ruminococcus bromii</td>\n", "      <td>Ruminococcus bromii</td>\n", "      <td>Ruminococcus bromii</td>\n", "      <td><PERSON><PERSON> br<PERSON></td>\n", "      <td>Ruminococcus</td>\n", "      <td>Ruminococcus</td>\n", "      <td>Oscillospiraceae</td>\n", "      <td>Oscillospiraceae</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Clostridia</td>\n", "      <td>Clostridia</td>\n", "      <td>Bacillota</td>\n", "      <td>Firmicutes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td><PERSON><PERSON>longum</td>\n", "      <td>Bifidobacterium longum</td>\n", "      <td>Bifidobacterium longum</td>\n", "      <td>Bifidobacterium longum</td>\n", "      <td><PERSON><PERSON> longum</td>\n", "      <td>Bifidobacterium</td>\n", "      <td>Bifidobacterium</td>\n", "      <td>Bifidobacteriaceae</td>\n", "      <td>Bifidobacteriaceae</td>\n", "      <td>Bifidobacteriales</td>\n", "      <td>Bifidobacteriales</td>\n", "      <td>Actinomycetia</td>\n", "      <td>Actinomycetia</td>\n", "      <td>Actinomycetota</td>\n", "      <td>Actinobacteria</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td><PERSON><PERSON>adolescent<PERSON></td>\n", "      <td>Bifidobacterium adolescentis</td>\n", "      <td>Bifidobacterium adolescentis</td>\n", "      <td>Bifidobacterium adolescentis</td>\n", "      <td><PERSON><PERSON> adolescent<PERSON></td>\n", "      <td>Bifidobacterium</td>\n", "      <td>Bifidobacterium</td>\n", "      <td>Bifidobacteriaceae</td>\n", "      <td>Bifidobacteriaceae</td>\n", "      <td>Bifidobacteriales</td>\n", "      <td>Bifidobacteriales</td>\n", "      <td>Actinomycetia</td>\n", "      <td>Actinomycetia</td>\n", "      <td>Actinomycetota</td>\n", "      <td>Actinobacteria</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>C.aerofaciens</td>\n", "      <td>Collinsella aerofaciens</td>\n", "      <td>Collinsella aerofaciens</td>\n", "      <td>Collinsella aerofaciens</td>\n", "      <td>C. aerofaciens</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Coriobacteriaceae</td>\n", "      <td>Coriobacteriaceae</td>\n", "      <td>Coriobacteriales</td>\n", "      <td>Coriobacteriales</td>\n", "      <td>Coriobacteriia</td>\n", "      <td>Coriobacteriia</td>\n", "      <td>Actinomycetota</td>\n", "      <td>Actinobacteria</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td><PERSON><PERSON>coli</td>\n", "      <td>Escherichia coli</td>\n", "      <td>Escherichia coli</td>\n", "      <td>Escherichia coli</td>\n", "      <td><PERSON><PERSON> coli</td>\n", "      <td>Escherichia</td>\n", "      <td>Escherichia</td>\n", "      <td>Enterobacteriaceae</td>\n", "      <td>Enterobacteriaceae</td>\n", "      <td>Enterobacterales</td>\n", "      <td>Enterobacterales</td>\n", "      <td>Gammaproteobacteria</td>\n", "      <td>Gammaproteobacteria</td>\n", "      <td>Pseudomonadota</td>\n", "      <td>Proteobacteria</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       species_HPLCname                          species  \\\n", "0           B.uniformis            Bacteroides uniformis   \n", "1            B.fragilis             Bacteroides fragilis   \n", "2              B.ovatus               Bacteroides ovatus   \n", "3               B.theta     Bacteroides thetaiotaomicron   \n", "4          <PERSON><PERSON>finegoldii           Bacteroides finegoldii   \n", "5            B.vulgatus             Phocaeicola vulgatus   \n", "6               P.copri                 Prevotella copri   \n", "7         P.distastonis       Parabacteroides distasonis   \n", "8        R.intestinalis           Roseburia intestinalis   \n", "9             E.rectale              Eubacterium rectale   \n", "10            L.eligens              Lachnospira eligens   \n", "11        <PERSON>.longicatena                Dorea longicatena   \n", "12     F.saccharivorans  Fusicatenibacter saccharivorans   \n", "13            <PERSON><PERSON>we<PERSON><PERSON>i                 <PERSON> wexlerae   \n", "14  B.hydrogenotrophica        Blautia hydrogenotrophica   \n", "15        F.<PERSON>ii     Faecalibacterium pra<PERSON>ii   \n", "16            E.siraeum              Eubacterium siraeum   \n", "17             R.bromii              Ruminococcus bromii   \n", "18             B.longum           Bifidobacterium longum   \n", "19       B.adolescentis     Bifidobacterium adolescentis   \n", "20        C.aerofaciens          Collinsella aerofaciens   \n", "21               E.coli                 Escherichia coli   \n", "\n", "                        new_species                        species.1  \\\n", "0             Bacteroides uniformis            Bacteroides uniformis   \n", "1              Bacteroides fragilis             Bacteroides fragilis   \n", "2                Bacteroides ovatus               Bacteroides ovatus   \n", "3      Bacteroides thetaiotaomicron     Bacteroides thetaiotaomicron   \n", "4            Bacteroides finegoldii           Bacteroides finegoldii   \n", "5              Phocaeicola vulgatus             Bacteroides vulgatus   \n", "6                  Prevotella copri                 Prevotella copri   \n", "7        Parabacteroides distasonis           Bacteroides distasonis   \n", "8            Roseburia intestinalis           Roseburia intestinalis   \n", "9             Agathobacter rectalis              Eubacterium rectale   \n", "10              Lachnospira eligens              Lachnospira eligens   \n", "11                Dorea longicatena                Dorea longicatena   \n", "12  Fusicatenibacter saccharivorans  Fusicatenibacter saccharivorans   \n", "13                 <PERSON><PERSON><PERSON> wexlerae                 B<PERSON><PERSON> wexlerae   \n", "14        Blautia hydrogenotrophica        Blautia hydrogenotrophica   \n", "15     Faecalibacterium pra<PERSON>ii     Faecalibacterium pra<PERSON>ii   \n", "16              Eubacterium siraeum              Eubacterium siraeum   \n", "17              Ruminococcus bromii              Ruminococcus bromii   \n", "18           Bifidobacterium longum           Bifidobacterium longum   \n", "19     Bifidobacterium adolescentis     Bifidobacterium adolescentis   \n", "20          Collinsella aerofaciens          Collinsella aerofaciens   \n", "21                 Escherichia coli                 Escherichia coli   \n", "\n", "           species_short         new_genus                genus  \\\n", "0           B. uniformis       Bacteroides          Bacteroides   \n", "1            B. fragilis       Bacteroides          Bacteroides   \n", "2              B. ovatus       Bacteroides          Bacteroides   \n", "3               B. theta       Bacteroides          Bacteroides   \n", "4          <PERSON><PERSON>ii       Bacteroides          Bacteroides   \n", "5            <PERSON><PERSON> vulgatus       Phocaeicola          Phocaeicola   \n", "6               P. copri        Prevotella           Prevotella   \n", "7         P. distastonis   Parabacteroides      Parabacteroides   \n", "8        <PERSON>. intestinalis         <PERSON>buria            Roseburia   \n", "9             E. rectale      Agathobacter   Lachnospiraceae_NA   \n", "10            L. eligens       Lachnospira          Lachnospira   \n", "11        <PERSON>. longicatena             Dorea                Dorea   \n", "12     F. saccharivorans  Fusicatenibacter     Fusicatenibacter   \n", "13           <PERSON><PERSON> <PERSON><PERSON><PERSON>ae           <PERSON>lau<PERSON>   \n", "14  <PERSON>. hydrogenotrophica           <PERSON>   \n", "15        <PERSON><PERSON>ii  Faecalibacterium     Faecalibacterium   \n", "16            E. siraeum     [Eubacterium]  Oscillospiraceae_NA   \n", "17             <PERSON>. bromii      Ruminococcus         Ruminococcus   \n", "18             B. longum   Bifidobacterium      Bifidobacterium   \n", "19       B. adolescentis   Bifidobacterium      Bifidobacterium   \n", "20        C. aerofaciens       Collins<PERSON>   \n", "21               E. coli       Escherichia          Escherichia   \n", "\n", "            new_family              family          new_order  \\\n", "0       Bacteroidaceae      Bacteroidaceae      Bacteroidales   \n", "1       Bacteroidaceae      Bacteroidaceae      Bacteroidales   \n", "2       Bacteroidaceae      Bacteroidaceae      Bacteroidales   \n", "3       Bacteroidaceae      Bacteroidaceae      Bacteroidales   \n", "4       Bacteroidaceae      Bacteroidaceae      Bacteroidales   \n", "5       Bacteroidaceae      Bacteroidaceae      Bacteroidales   \n", "6       Prevotellaceae      Prevotellaceae      Bacteroidales   \n", "7       Tannerellaceae      Tannerellaceae      Bacteroidales   \n", "8      Lachnospiraceae     Lachnospiraceae      Eubacteriales   \n", "9      Lachnospiraceae     Lachnospiraceae      Eubacteriales   \n", "10     Lachnospiraceae     Lachnospiraceae      Eubacteriales   \n", "11     Lachnospiraceae     Lachnospiraceae      Eubacteriales   \n", "12     Lachnospiraceae     Lachnospiraceae      Eubacteriales   \n", "13     Lachnospiraceae     Lachnospiraceae      Eubacteriales   \n", "14     Lachnospiraceae     Lachnospiraceae      Eubacteriales   \n", "15    Oscillospiraceae    Oscillospiraceae      Eubacteriales   \n", "16    Oscillospiraceae    Oscillospiraceae      Eubacteriales   \n", "17    Oscillospiraceae    Oscillospiraceae      Eubacteriales   \n", "18  Bifidobacteriaceae  Bifidobacteriaceae  Bifidobacteriales   \n", "19  Bifidobacteriaceae  Bifidobacteriaceae  Bifidobacteriales   \n", "20   Coriobacteriaceae   Coriobacteriaceae   Coriobacteriales   \n", "21  Enterobacteriaceae  Enterobacteriaceae   Enterobacterales   \n", "\n", "                order            new_class                class  \\\n", "0       Bacteroidales          Bacteroidia          Bacteroidia   \n", "1       Bacteroidales          Bacteroidia          Bacteroidia   \n", "2       Bacteroidales          Bacteroidia          Bacteroidia   \n", "3       Bacteroidales          Bacteroidia          Bacteroidia   \n", "4       Bacteroidales          Bacteroidia          Bacteroidia   \n", "5       Bacteroidales          Bacteroidia          Bacteroidia   \n", "6       Bacteroidales          Bacteroidia          Bacteroidia   \n", "7       Bacteroidales          Bacteroidia          Bacteroidia   \n", "8       Eubacteriales           Clostridia           Clostridia   \n", "9       Eubacteriales           Clostridia           Clostridia   \n", "10      Eubacteriales           Clostridia           Clostridia   \n", "11      Eubacteriales           Clostridia           Clostridia   \n", "12      Eubacteriales           Clostridia           Clostridia   \n", "13      Eubacteriales           Clostridia           Clostridia   \n", "14      Eubacteriales           Clostridia           Clostridia   \n", "15      Eubacteriales           Clostridia           Clostridia   \n", "16      Eubacteriales           Clostridia           Clostridia   \n", "17      Eubacteriales           Clostridia           Clostridia   \n", "18  Bifidobacteriales        Actinomycetia        Actinomycetia   \n", "19  Bifidobacteriales        Actinomycetia        Actinomycetia   \n", "20   Coriobacteriales       Coriobacteriia       Coriobacteriia   \n", "21   Enterobacterales  Gammaproteobacteria  Gammaproteobacteria   \n", "\n", "        new_phylum          phylum  \n", "0     Bacteroidota   Bacteroidetes  \n", "1     Bacteroidota   Bacteroidetes  \n", "2     Bacteroidota   Bacteroidetes  \n", "3     Bacteroidota   Bacteroidetes  \n", "4     Bacteroidota   Bacteroidetes  \n", "5     Bacteroidota   Bacteroidetes  \n", "6     Bacteroidota   Bacteroidetes  \n", "7     Bacteroidota   Bacteroidetes  \n", "8        Bacillota      Firmicutes  \n", "9        Bacillota      Firmicutes  \n", "10       Bacillota      Firmicutes  \n", "11       Bacillota      Firmicutes  \n", "12       Bacillota      Firmicutes  \n", "13       Bacillota      Firmicutes  \n", "14       Bacillota      Firmicutes  \n", "15       Bacillota      Firmicutes  \n", "16       Bacillota      Firmicutes  \n", "17       Bacillota      Firmicutes  \n", "18  Actinomycetota  Actinobacteria  \n", "19  Actinomycetota  Actinobacteria  \n", "20  Actinomycetota  Actinobacteria  \n", "21  Pseudomonadota  Proteobacteria  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["import sys, os\n", "import glob \n", "import matplotlib as mpl\n", "mpl.rcParams['pdf.fonttype'] = 42\n", "import collections\n", "import builtins\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import scipy.integrate as spi\n", "from scipy.integrate import odeint #this is the module to solve ODEs\n", "\n", "%matplotlib inline\n", "import scipy.stats \n", "import json\n", "\n", "import csv\n", "from collections import defaultdict\n", "from pprint import pprint\n", "\n", "################################\n", "#load information of species to include\n", "#################################\n", "#load species information for all characterized species\n", "speciesinformation=pd.read_csv(\"data_hplc/species_properties.csv\",skiprows=1)\n", "#display(speciesinformation.head())\n", "display(speciesinformation)\n", "from ete3 import Tree, TreeStyle, TextFace, add_face_to_node\n", "import plot_eteTree \n", "#adjusted from https://gist.github.com/jolespin/5d90deff552138d73de7ed4bdd9ac57a\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Load general files and generate tree\n", "\n", "## Note: This uses the taxonomic abundance tables caliulatioed in subfolder microbiota_composition. To decide if all or only healthy samples should be considered run the script in the folder first and make your selection there.\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['average', 'AsnicarF_2017', 'AsnicarF_2021', 'BackhedF_2015',\n", "       '<PERSON><PERSON><PERSON>-PalmeJ_2015', 'ChuDM_2017', 'CosteaPI_2017', 'DavidLA_2015',\n", "       'DeFilippisF_2019', 'DhakanDB_2019', 'FengQ_2015', 'FerrettiP_2018',\n", "       'GuptaA_2019', 'HMP_2012', 'HMP_2019_ibdmdb', 'HMP_2019_t2d',\n", "       'HallAB_2017', 'HanniganGD_2017', 'HansenLBS_2018',\n", "       'Heitz-BuschartA_2016', 'IjazUZ_2017', 'JieZ_2017', 'KarlssonFH_2013',\n", "       'Ke<PERSON>aneDM_2020', 'LeChatelierE_2013', 'LiJ_2014', 'LiJ_2017',\n", "       'LiSS_2016', 'LifeLinesDeep_2016', 'LiuW_2016', 'LouisS_2016',\n", "       'MehtaRS_2018', 'NagySzakalD_2017', 'NielsenHB_2014',\n", "       'Obregon-TitoAJ_2015', 'QinJ_2012', 'QinN_2014', 'RampelliS_2015',\n", "       'RaymondF_2016', 'SankaranarayananK_2015', 'SchirmerM_2016',\n", "       'S<PERSON>Y_2019', 'ThomasAM_2018a', 'ThomasAM_2018b', 'ThomasAM_2019_c',\n", "       'VincentC_2016', 'VogtmannE_2016', 'WampachL_2018', 'WirbelJ_2018',\n", "       'XieH_2016', 'YachidaS_2019', 'YassourM_2016', 'YassourM_2018',\n", "       'YeZ_2018', '<PERSON>J_2015', 'ZeeviD_2015', 'ZellerG_2014', 'ZhuF_2020'],\n", "      dtype='object')"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["'HMP_2019_ibdmdb'"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["['Bacteroidetes', 'Firmicutes', 'Actinobacteria', 'Proteobacteria']\n", "['Bacteroidia', 'Clostridia', 'Actinomycetia', 'Coriobacteriia', 'Gammaproteobacteria']\n", "['Bacteroidales', 'Eubacteriales', 'Bifidobacteriales', 'Coriobacteriales', 'Enterobacterales']\n", "['Bacteroidaceae', 'Prevotellaceae', 'Tannerellaceae', 'Lachnospiraceae', 'Oscillospiraceae', 'Bifidobacteriaceae', 'Coriobacteriaceae', 'Enterobacteriaceae']\n", "['Bacteroides', 'Phocaeico<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Parabacteroides', 'Roseburia', 'Lachnospiraceae_NA', 'Lachnospira', 'Dorea', 'Fusicatenibacter', 'Blautia', 'Faecalibacterium', 'Oscillospiraceae_NA', 'Ruminococcus', 'Bifidobacterium', 'Collinsella', 'Escherichia']\n", "['Bacteroides uniformis', 'Bacteroides fragilis', 'Bacteroides ovatus', 'Bacteroides thetaiotaomicron', 'Bacteroides <PERSON>ii', 'Phocaeicola vulgatus', 'Prevotella copri', 'Parabacteroid<PERSON> distasonis', 'Rose<PERSON>ia intestinalis', 'Eubacterium rectale', 'Lachnospira eligens', 'Dorea longicatena', 'Fusicatenibacter saccharivorans', 'Blautia wexlerae', 'Blautia hydrogenotrophica', 'Faecalibacterium <PERSON>ii', 'Eubacterium siraeum', 'Ruminococcus bromii', 'Bifidobacterium longum', 'Bifidobacterium adolescentis', 'Collinsella aerofaciens', 'Escherichia coli']\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "curdata=pd.read_csv(\"data_curated_microbiome/avabundance_class_healthy_adult_western.csv\",skiprows=0,index_col=0,)\n", "studylist=curdata.columns\n", "display(studylist)\n", "\n", "studylist=[\"HMP_2019_ibdmdb\"]#\"HMP_2012\",\"AsnicarF_2021\",\"HMP_2019_ibdmdb\"]\n", "############################\n", "#plotting options for tree\n", "##########################\n", "phyla_colors=[\"r\",\"b\",\"m\",\"orange\",\"brown\"] \n", "phyla=['Bacteroidetes', 'Firmicutes', 'Actinobacteria', 'Proteobacteria']\n", "\n", "#define which study to use for abundance data\n", "#study=\"HMP_2019_ibdmdb\"\n", "#fileending_abundanceinfo=\"\"\n", "#study=\"average\"\n", "fileending_abundanceinfo=\"_healthy_adult_western\" #\"\"\n", "    \n", "display()\n", "\n", "for study in studylist:\n", "    display(study)\n", "    #use all samples from healthy adults (non-westernized marked as no)\n", "\n", "    \n", "    ################################\n", "    #load abundance information (from analysis of metagenomics data\"\n", "    # ! modify here if other studies should be included\n", "    ################################\n", "    abundanceinformation=[]\n", "    taxa_levellist=[]\n", "    taxa_levellistC=[]\n", "    for level in [\"phylum\",\"class\",\"order\",\"family\",\"genus\",\"species\"]:\n", "        taxa_levellist.append([])\n", "        taxa_levellistC.append([])\n", "        abundanceinformation.append(pd.read_csv(\"data_curated_microbiome/avabundance_\"+level+fileending_abundanceinfo+\".csv\",skiprows=0,index_col=0,))\n", "    all_species_list=[]\n", "    all_species_list_short=[]\n", "    \n", "    #################################\n", "    #generate Newick tree\n", "    ################################\n", "    branchdistance=\":2\" #distance between branches\n", "    branchdistance_species=\":0.3\" #distance of lead (last name)\n", "    \n", "    #start analysis\n", "    branchdistance_number=float(branchdistance[1:])\n", "    #phyla=speciesinformation[\"phylum\"].unique()\n", "    newick=\"(\"\n", "    cP=-1\n", "    \n", "    #go through each taxonomic level\n", "    for phylum in phyla:\n", "        taxa_levellist[0].append(phylum)  \n", "        cP=cP+1\n", "        taxa_levellistC[0].append(phyla_colors[cP])\n", "        select=speciesinformation.loc[speciesinformation[\"phylum\"]==phylum]\n", "        classes=select[\"class\"].unique()\n", "        \n", "        if cP==0:\n", "                newick=newick+\"(\"\n", "        else:\n", "                newick=newick+\",(\"\n", "        cI=-1    \n", "        for classc in classes:\n", "            taxa_levellist[1].append(classc)\n", "            taxa_levellistC[1].append(phyla_colors[cP])\n", "            cI=cI+1\n", "            select1=select.loc[select[\"class\"]==classc]\n", "            if cI==0:\n", "                newick=newick+\"(\"\n", "            else:\n", "                newick=newick+\",(\"\n", "            orders=select1[\"order\"].unique()\n", "            oI= -1\n", "            for order in orders:\n", "                taxa_levellist[2].append(order)\n", "                taxa_levellistC[2].append(phyla_colors[cP])\n", "                oI=oI+1\n", "                select2=select1.loc[select1[\"order\"]==order]\n", "                if oI==0:\n", "                    newick=newick+\"(\"\n", "                else:\n", "                    newick=newick+\",(\"\n", "                families=select2[\"family\"].unique()\n", "                fI=-1\n", "                for family in families:\n", "                    taxa_levellist[3].append(family)\n", "                    taxa_levellistC[3].append(phyla_colors[cP])\n", "                    fI=fI+1\n", "                    select3=select2.loc[select2[\"family\"]==family]\n", "                    genus=select3[\"genus\"].unique()\n", "                    if fI==0:\n", "                        newick=newick+\"(\"\n", "                    else:\n", "                        newick=newick+\",(\"\n", "    \n", "                    gI=-1\n", "                    for genus in genus:\n", "                        taxa_levellist[4].append(genus)\n", "                        taxa_levellistC[4].append(phyla_colors[cP])\n", "                        gI=gI+1\n", "                        select4=select3.loc[select3[\"genus\"]==genus]\n", "                        species=select4[\"species\"].unique()\n", "                        if gI==0:\n", "                            newick=newick+\"(\"\n", "                        else:\n", "                            newick=newick+\",(\"\n", "                        spc=-1\n", "                        for speciesc in species:\n", "                                taxa_levellist[5].append(speciesc)\n", "                                taxa_levellistC[5].append(phyla_colors[cP])\n", "                                spc=spc+1\n", "                                if spc>0:\n", "                                    newick=newick+\",\"\n", "                                select5=select4.loc[select4[\"species\"]==speciesc]\n", "                                all_species_list.append(speciesc)\n", "                                newick= newick+speciesc+branchdistance_species\n", "                        newick=newick+\")\"+genus+branchdistance\n", "                    newick=newick+\")\"+family+branchdistance\n", "                newick=newick+\")\"+order+branchdistance\n", "            newick=newick+\")\"+classc+branchdistance\n", "        newick=newick+\")\"+str(phylum)+branchdistance  \n", "    newick=newick+\");\"\n", "    \n", "    #save tree\n", "    text_file = open(\"newick.txt\", \"w\")\n", "    n = text_file.write(newick)\n", "    text_file.close()\n", "    \n", "    #print(all_species_list)\n", "    \n", "    \n", "    #####################\n", "    #for different nodes of tree get abundance lvele\n", "    ######################\n", "        \n", "    \n", "    lc=-1\n", "    abundancevalues=[]\n", "    for level in [\"phylum\",\"class\",\"order\",\"family\",\"genus\",\"species\"]:\n", "        abundancevalues.append([])\n", "        lc=lc+1\n", "        print(taxa_levellist[lc])\n", "        for taxidentifier in taxa_levellist[lc]:\n", "            #print(taxidentifier)\n", "            #print(study)\n", "            abundancevalues[-1].append(abundanceinformation[lc].at[taxidentifier,study])\n", "    \n", "    \n", "    #generate list with abundance for all taxonomic levels\n", "    alltaxonomicnames=[]\n", "    alltaxonomiccolors=[] #color (currently based on phyla)\n", "    alltaxonomiccolors_phylum=[] #color (phyla in color rest black)\n", "    taxonomiclevel=[]\n", "    \n", "    alltaxonomicabundances=[]\n", "    abundancevalues_sum=[]\n", "    lc=-1\n", "    for level in [\"phylum\",\"class\",\"order\",\"family\",\"genus\",\"species\"]:\n", "        lc=lc+1\n", "        #colors for taxonomic names\n", "        #option 1: only phylum in phylum color, others black\n", "        #if level==\"phylum\":\n", "        #    alltaxonomiccolors_phylum=alltaxonomiccolors_phylum+taxa_levellistC[lc]\n", "        #else:\n", "        #    alltaxonomiccolors_phylum=alltaxonomiccolors_phylum+[\"k\"]*len(taxa_levellist[lc])\n", "        #option 2: set all names according to phylum:\n", "        alltaxonomiccolors_phylum=alltaxonomiccolors_phylum+taxa_levellistC[lc]\n", "        taxonomiclevel=taxonomiclevel+[level]*len(taxa_levellist[lc])\n", "    \n", "       \n", "        alltaxonomicnames=alltaxonomicnames+taxa_levellist[lc]\n", "        alltaxonomiccolors=alltaxonomiccolors+taxa_levellistC[lc]\n", "        abundancevalues_sum.append(sum(abundancevalues[lc]))\n", "        alltaxonomicabundances=alltaxonomicabundances+abundancevalues[lc]\n", "    \n", "    #for easier handling, generate dataframe with all information\n", "    data_abundancec=pd.DataFrame(list(zip(alltaxonomicnames, alltaxonomicabundances, alltaxonomiccolors,alltaxonomiccolors_phylum,taxonomiclevel)))\n", "    data_abundancec.columns=[\"taxidentifier\",\"abundance\",\"color\",\"color_phylum\",\"tax_level\"]\n", "    data_abundancec.set_index(\"taxidentifier\",inplace=True)\n", "    #print(\"sum of abundance\")\n", "    #print(abundancevalues_sum)\n", "    \n", "    \n", "    \n", "    \n", "    #add short and alternamtive names according to \n", "    for iter, row in data_abundancec.iterrows():\n", "        levelc=row[\"tax_level\"]\n", "        taxind=iter\n", "        curinfo=speciesinformation.loc[speciesinformation[levelc]==iter]\n", "        if levelc==\"species\":\n", "            shortn=curinfo[level+\"_short\"]\n", "            data_abundancec.at[iter,\"short_name\"]=shortn.iloc[0]\n", "        shortn=curinfo[\"new_\"+levelc]\n", "        if (shortn.shape[0]>1) and (levelc==\"species\"):\n", "            error\n", "        data_abundancec.at[iter,\"new_name\"]=shortn.iloc[0]\n", "    \n", "    #display(data_abundancec.head(15))\n", "    #consistency check, taxonomic identifiers should be unique\n", "    if len(alltaxonomicnames) != len(set(alltaxonomicnames)):\n", "        print(\"Error: non unique used of taxonomic identifiers\")\n", "        print([item for item, count in collections.Counter(alltaxonomicnames).items() if count > 1])\n", "        error\n", "    \n", "    ############\n", "    #generate ete3 tree from Newick tree\n", "    ############\n", "    \n", "    t = Tree(newick, format=1)\n", "    ts = TreeStyle()\n", "    ts.show_leaf_name = False\n", "    \n", "    ############\n", "    #ad properties to tree branches\n", "    #############\n", "    \n", "    all_species_list_short=[]\n", "    for leaf in t.traverse():\n", "        nameleaf=leaf.name\n", "        if len(nameleaf)>0:\n", "            abundancec=str(round(data_abundancec.at[nameleaf,\"abundance\"],1))+\"%\"\n", "    \n", "            \n", "            color=data_abundancec.at[nameleaf,\"color\"] #color for lines\n", "            color_phylum=data_abundancec.at[nameleaf,\"color_phylum\"] #color for taxonomic names\n", "            new_name=data_abundancec.at[nameleaf,\"new_name\"]\n", "            short_name=data_abundancec.at[nameleaf,\"short_name\"]\n", "            if short_name != \"nan\":\n", "                all_species_list_short.append(short_name)\n", "            #print(nameleaf)\n", "            #print(abundancec)\n", "            leaf.add_features(abundance=abundancec)\n", "            leaf.add_features(new_name=new_name)\n", "            leaf.add_features(short_name=short_name)\n", "            leaf.add_features(color=color_phylum)\n", "            leaf.img_style['hz_line_color'] = color\n", "            leaf.img_style['vt_line_color'] = color\n", "        else:\n", "            leaf.add_features(abundance=\"\")\n", "            leaf.add_features(abundance=\"\")\n", "            leaf.add_features(new_name=\"\")\n", "            leaf.add_features(short_name=\"\")\n", "            leaf.add_features(color=\"k\")\n", "            leaf.img_style['hz_line_color'] = \"k\"\n", "            leaf.img_style['vt_line_color'] = \"k\"\n", "    \n", "            \n", "        leaf.img_style['hz_line_width']=2\n", "        leaf.img_style['vt_line_width']=2\n", "        leaf.img_style['fgcolor']='#000000'\n", "    \n", "    #print(all_species_list_short)\n", "    \n", "    \n", "    fig, ax = plt.subplots(1,1,figsize=(12,5))  #for barplots yields/excretion\n", "    plot_eteTree.plot_tree(t, align_names=False, name_offset=None, max_dist=None, font_size=9, axe=ax,font_size2=8,alternative_names=True,short_names=False)\n", "    lc=-1\n", "    for level in [\"phylum\",\"class\",\"order\",\"family\",\"genus\",\"species\"]:\n", "        lc=lc+1\n", "        lpos=0.5+lc*branchdistance_number\n", "        ax.text(lpos,-1.2,level,fontsize=14)\n", "        ax.text(lpos,-2.5,\"(\"+str(round(abundancevalues_sum[lc],1))+\"%)\",fontsize=14,color='k')\n", "\n", "    ax.set_title(study)\n", "    ax.text(6,-4,\"taxonomic rank\")\n", "    fig.tight_layout()\n", "    fig.savefig(\"plot_output_figure1/tree_matplotlib.pdf\")\n", "    plt.show()\n", "    \n", "    ###### old, using eTe3 plotting options\n", "    \n", "    #decide what to add to nodes\n", "    def my_layout(node):\n", "            F = TextFace(node.name[:5]) #tight_text=True\n", "            A = TextFace(node.abundance) #, tight_text=True \n", "            #print(node)\n", "            if node.is_leaf():\n", "                add_face_to_node(F, node, column=1, position=\"branch-top\")\n", "                #add_face_to_node(F, node, column=0,position='aligned')\n", "                add_face_to_node(A, node, column=0,position='aligned')\n", "            else:\n", "                add_face_to_node(F, node, column=0, position=\"branch-top\")\n", "                add_face_to_node(A, node, column=0, position=\"branch-bottom\")\n", "    \n", "                \n", "                #if node.is_leaf():\n", "            #        seq_face = SeqMotifFace(node.sequence, seqtype='aa', seq_format='seq')\n", "            #        add_face_to_node(seq_face, node, column=0, position='aligned')\n", "        \n", "    ts.layout_fn = my_layout\n", "    \n", "    #ts.show_branch_length = True\n", "    #ts.show_branch_support = True\n", "    #t.render(\"plot_output_Fig2/tree.pdf\", w=8, units=\"in\", tree_style=ts)\n", "    \n", "    #t.show(tree_style=ts)\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Plot tree with full species information"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 1200x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "fig, ax = plt.subplots(1,1,figsize=(12,5))  #for barplots yields/excretion\n", "plot_eteTree.plot_tree(t, align_names=False, name_offset=None, max_dist=None, font_size=9, axe=ax,font_size2=8,alternative_names=True,short_names=False)\n", "lc=-1\n", "for level in [\"phylum\",\"class\",\"order\",\"family\",\"genus\",\"species\"]:\n", "    lc=lc+1\n", "    lpos=0.5+lc*branchdistance_number\n", "    ax.text(lpos,-1.2,level,fontsize=14)\n", "    ax.text(lpos,-2.5,\"(\"+str(round(abundancevalues_sum[lc],1))+\"%)\",fontsize=14,color='k')\n", "\n", "ax.text(6,-4,\"taxonomic rank\")\n", "fig.tight_layout()\n", "fig.savefig(\"plot_output_figure1/tree_matplotlib.pdf\")\n", "plt.show()\n", "\n", "###### old, using eTe3 plotting options\n", "\n", "#decide what to add to nodes\n", "def my_layout(node):\n", "        F = TextFace(node.name[:5]) #tight_text=True\n", "        A = TextFace(node.abundance) #, tight_text=True \n", "        #print(node)\n", "        if node.is_leaf():\n", "            add_face_to_node(F, node, column=1, position=\"branch-top\")\n", "            #add_face_to_node(F, node, column=0,position='aligned')\n", "            add_face_to_node(A, node, column=0,position='aligned')\n", "        else:\n", "            add_face_to_node(F, node, column=0, position=\"branch-top\")\n", "            add_face_to_node(A, node, column=0, position=\"branch-bottom\")\n", "\n", "            \n", "            #if node.is_leaf():\n", "        #        seq_face = SeqMotifFace(node.sequence, seqtype='aa', seq_format='seq')\n", "        #        add_face_to_node(seq_face, node, column=0, position='aligned')\n", "    \n", "ts.layout_fn = my_layout\n", "\n", "#ts.show_branch_length = True\n", "#ts.show_branch_support = True\n", "#t.render(\"plot_output_Fig2/tree.pdf\", w=8, units=\"in\", tree_style=ts)\n", "\n", "#t.show(tree_style=ts)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Plot compact tree without full names"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 400x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(1,1,figsize=(4,5))  #for barplots yields/excretion\n", "plot_eteTree.plot_tree(t, align_names=False, name_offset=None, max_dist=None, font_size=9, axe=ax,font_size2=8,alternative_names=False,short_names=True,plot_abundancenumbers=True,abundancestyle=\"plain\")\n", "lc=-1\n", "for level in [\"phylum\",\"class\",\"order\",\"family\",\"genus\",\"species\"]:\n", "    lc=lc+1\n", "    lpos=0.5+lc*branchdistance_number\n", "    \n", "    #ax.text(lpos,-1.5,level,fontsize=10)\n", "    #ax.text(lpos,-2.5,str(round(abundancevalues_sum[lc],1)),fontsize=10,color='k')\n", "\n", "ax.text(6,-4,\"taxonomic rank\")\n", "fig.tight_layout()\n", "fig.savefig(\"plot_output_figure1/tree_matplotlib_compact.pdf\")\n", "plt.show()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Prepare plots for paper\n", "\n", "This script takes data from analysis_out.csv which is generated in the final_analysis notebook. \n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0.2</th>\n", "      <th>Unnamed: 0.1</th>\n", "      <th>Unnamed: 0</th>\n", "      <th>notes</th>\n", "      <th>strain</th>\n", "      <th>species</th>\n", "      <th>experiment</th>\n", "      <th>experiment_short</th>\n", "      <th>exp_number</th>\n", "      <th>medium</th>\n", "      <th>...</th>\n", "      <th>succinate_std</th>\n", "      <th>lactate_std</th>\n", "      <th>butyrate_std</th>\n", "      <th>formate_std</th>\n", "      <th>maltose_std</th>\n", "      <th>total_ferm</th>\n", "      <th>total_ferm_std</th>\n", "      <th>total_uptake</th>\n", "      <th>total_uptake_std</th>\n", "      <th>growth_rate_std</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>29.0</td>\n", "      <td>NaN</td>\n", "      <td>DSM20083</td>\n", "      <td><PERSON><PERSON>adolescent<PERSON></td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>33</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "      <td>45.0</td>\n", "      <td>NaN</td>\n", "      <td>DSM20083</td>\n", "      <td><PERSON><PERSON>adolescent<PERSON></td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>51</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>2.0</td>\n", "      <td>38.0</td>\n", "      <td>NaN</td>\n", "      <td>DSM20083</td>\n", "      <td><PERSON><PERSON>adolescent<PERSON></td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>42</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>3.0</td>\n", "      <td>143.0</td>\n", "      <td>NaN</td>\n", "      <td>DSM20083</td>\n", "      <td><PERSON><PERSON>adolescent<PERSON></td>\n", "      <td>NaN</td>\n", "      <td>stan_BHI_30Dec22</td>\n", "      <td>199</td>\n", "      <td>BHI</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>4.0</td>\n", "      <td>142.0</td>\n", "      <td>NaN</td>\n", "      <td>DSM20083</td>\n", "      <td><PERSON><PERSON>adolescent<PERSON></td>\n", "      <td>NaN</td>\n", "      <td>stan_BHI_30Dec22</td>\n", "      <td>198</td>\n", "      <td>BHI</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 65 columns</p>\n", "</div>"], "text/plain": ["   Unnamed: 0.2  Unnamed: 0.1  Unnamed: 0  notes    strain         species  \\\n", "0             0           0.0        29.0    NaN  DSM20083  <PERSON><PERSON><PERSON><PERSON>   \n", "1             1           1.0        45.0    NaN  DSM20083  <PERSON><PERSON><PERSON><PERSON>   \n", "2             2           2.0        38.0    NaN  DSM20083  <PERSON><PERSON><PERSON><PERSON>   \n", "3             3           3.0       143.0    NaN  DSM20083  <PERSON><PERSON><PERSON><PERSON>   \n", "4             4           4.0       142.0    NaN  DSM20083  <PERSON><PERSON><PERSON><PERSON>   \n", "\n", "                       experiment                experiment_short exp_number  \\\n", "0  analysis_Zur_Newruns_2023_June  analysis_Zur_Newruns_2023_June         33   \n", "1  analysis_Zur_Newruns_2023_June  analysis_Zur_Newruns_2023_June         51   \n", "2  analysis_Zur_Newruns_2023_June  analysis_Zur_Newruns_2023_June         42   \n", "3                             NaN                stan_BHI_30Dec22        199   \n", "4                             NaN                stan_BHI_30Dec22        198   \n", "\n", "  medium  ... succinate_std  lactate_std  butyrate_std formate_std  \\\n", "0    YCA  ...           NaN          NaN           NaN         NaN   \n", "1    YCA  ...           NaN          NaN           NaN         NaN   \n", "2    YCA  ...           NaN          NaN           NaN         NaN   \n", "3    BHI  ...           NaN          NaN           NaN         NaN   \n", "4    BHI  ...           NaN          NaN           NaN         NaN   \n", "\n", "  maltose_std total_ferm  total_ferm_std total_uptake  total_uptake_std  \\\n", "0         NaN        NaN             NaN          NaN               NaN   \n", "1         NaN        NaN             NaN          NaN               NaN   \n", "2         NaN        NaN             NaN          NaN               NaN   \n", "3         NaN        NaN             NaN          NaN               NaN   \n", "4         NaN        NaN             NaN          NaN               NaN   \n", "\n", "   growth_rate_std  \n", "0              NaN  \n", "1              NaN  \n", "2              NaN  \n", "3              NaN  \n", "4              NaN  \n", "\n", "[5 rows x 65 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>species_HPLCname</th>\n", "      <th>species</th>\n", "      <th>new_species</th>\n", "      <th>species.1</th>\n", "      <th>species_short</th>\n", "      <th>new_genus</th>\n", "      <th>genus</th>\n", "      <th>new_family</th>\n", "      <th>family</th>\n", "      <th>new_order</th>\n", "      <th>order</th>\n", "      <th>new_class</th>\n", "      <th>class</th>\n", "      <th>new_phylum</th>\n", "      <th>phylum</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON>.uniformis</td>\n", "      <td>Bacteroides uniformis</td>\n", "      <td>Bacteroides uniformis</td>\n", "      <td>Bacteroides uniformis</td>\n", "      <td><PERSON>. uniformis</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON><PERSON>fragilis</td>\n", "      <td>Bacteroides fragilis</td>\n", "      <td>Bacteroides fragilis</td>\n", "      <td>Bacteroides fragilis</td>\n", "      <td><PERSON><PERSON> fragilis</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON><PERSON>ovatus</td>\n", "      <td>Bacteroides ovatus</td>\n", "      <td>Bacteroides ovatus</td>\n", "      <td>Bacteroides ovatus</td>\n", "      <td><PERSON><PERSON> ovatus</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON>.theta</td>\n", "      <td>Bacteroides thetaiotaomicron</td>\n", "      <td>Bacteroides thetaiotaomicron</td>\n", "      <td>Bacteroides thetaiotaomicron</td>\n", "      <td><PERSON>. theta</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Bacteroides <PERSON>ii</td>\n", "      <td>Bacteroides <PERSON>ii</td>\n", "      <td>Bacteroides <PERSON>ii</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  species_HPLCname                       species  \\\n", "0      B.uniformis         Bacteroides uniformis   \n", "1       B.fragilis          Bacteroides fragilis   \n", "2         B.ovatus            Bacteroides ovatus   \n", "3          B.theta  Bacteroides thetaiotaomicron   \n", "4     <PERSON><PERSON>finegoldii        Bacteroides finegoldii   \n", "\n", "                    new_species                     species.1  species_short  \\\n", "0         Bacteroides uniformis         Bacteroides uniformis   B. uniformis   \n", "1          Bacteroides fragilis          Bacteroides fragilis    B. fragilis   \n", "2            Bacteroides ovatus            Bacteroides ovatus      B. ovatus   \n", "3  Bacteroides thetaiotaomicron  Bacteroides thetaiotaomicron       B. theta   \n", "4        Bacteroides finegoldii        Bacteroides finegoldii  B<PERSON> finegoldii   \n", "\n", "     new_genus        genus      new_family          family      new_order  \\\n", "0  Bacteroides  Bacteroides  Bacteroidaceae  Bacteroidaceae  Bacteroidales   \n", "1  Bacteroides  Bacteroides  Bacteroidaceae  Bacteroidaceae  Bacteroidales   \n", "2  Bacteroides  Bacteroides  Bacteroidaceae  Bacteroidaceae  Bacteroidales   \n", "3  Bacteroides  Bacteroides  Bacteroidaceae  Bacteroidaceae  Bacteroidales   \n", "4  Bacteroides  Bacteroides  Bacteroidaceae  Bacteroidaceae  Bacteroidales   \n", "\n", "           order    new_class        class    new_phylum         phylum  \n", "0  Bacteroidales  Bacteroidia  Bacteroidia  Bacteroidota  Bacteroidetes  \n", "1  Bacteroidales  Bacteroidia  Bacteroidia  Bacteroidota  Bacteroidetes  \n", "2  Bacteroidales  Bacteroidia  Bacteroidia  Bacteroidota  Bacteroidetes  \n", "3  Bacteroidales  Bacteroidia  Bacteroidia  Bacteroidota  Bacteroidetes  \n", "4  Bacteroidales  Bacteroidia  Bacteroidia  Bacteroidota  Bacteroidetes  "]}, "metadata": {}, "output_type": "display_data"}, {"ename": "ValueError", "evalue": "'B.hydrogenotrophica' is not in list", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[7], line 12\u001b[0m\n\u001b[1;32m     10\u001b[0m \u001b[38;5;66;03m#sort list manually \u001b[39;00m\n\u001b[1;32m     11\u001b[0m sorter\u001b[38;5;241m=\u001b[39m[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mB.vulgatus\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mB.fragilis\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mB.ovatus\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mB.theta\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mB.fine<PERSON>ii\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mB.uniformis\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mP.copri\u001b[39m\u001b[38;5;124m'\u001b[39m,\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m<PERSON>.distastonis\u001b[39m\u001b[38;5;124m'\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mE.rectale\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mR.intestinalis\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mF.prausnitzii\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mR.bromii\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mB.longum\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mB.adolescentis\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mC.aerofaciens\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mE.coli\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mE.coliI\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mE.coliII\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFecal\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFecalPP\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mECOR\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mE.halli\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[0;32m---> 12\u001b[0m \u001b[43mdataout_av\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msort_values\u001b[49m\u001b[43m(\u001b[49m\u001b[43mby\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mspecies\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkey\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mlambda\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mcolumn\u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mcolumn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmap\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43;01mlambda\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43me\u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43msorter\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mindex\u001b[49m\u001b[43m(\u001b[49m\u001b[43me\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minplace\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m     14\u001b[0m display(dataout_av)\n\u001b[1;32m     16\u001b[0m \u001b[38;5;66;03m#speciesall=speciesinformation[\"species_HPLCname\"]\u001b[39;00m\n", "File \u001b[0;32m~/miniforge3/envs/ete3/lib/python3.12/site-packages/pandas/core/frame.py:7200\u001b[0m, in \u001b[0;36mDataFrame.sort_values\u001b[0;34m(self, by, axis, ascending, inplace, kind, na_position, ignore_index, key)\u001b[0m\n\u001b[1;32m   7197\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(ascending, (\u001b[38;5;28mtuple\u001b[39m, \u001b[38;5;28mlist\u001b[39m)):\n\u001b[1;32m   7198\u001b[0m         ascending \u001b[38;5;241m=\u001b[39m ascending[\u001b[38;5;241m0\u001b[39m]\n\u001b[0;32m-> 7200\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m \u001b[43mnargsort\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   7201\u001b[0m \u001b[43m        \u001b[49m\u001b[43mk\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkind\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mkind\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mascending\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mascending\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mna_position\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mna_position\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkey\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mkey\u001b[49m\n\u001b[1;32m   7202\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   7203\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   7204\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m inplace:\n", "File \u001b[0;32m~/miniforge3/envs/ete3/lib/python3.12/site-packages/pandas/core/sorting.py:401\u001b[0m, in \u001b[0;36mnargsort\u001b[0;34m(items, kind, ascending, na_position, key, mask)\u001b[0m\n\u001b[1;32m    377\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    378\u001b[0m \u001b[38;5;124;03mIntended to be a drop-in replacement for np.argsort which handles NaNs.\u001b[39;00m\n\u001b[1;32m    379\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    396\u001b[0m \u001b[38;5;124;03mnp.ndarray[np.intp]\u001b[39;00m\n\u001b[1;32m    397\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    399\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m key \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[1;32m    400\u001b[0m     \u001b[38;5;66;03m# see TestDataFrameSortKey, TestRangeIndex::test_sort_values_key\u001b[39;00m\n\u001b[0;32m--> 401\u001b[0m     items \u001b[38;5;241m=\u001b[39m \u001b[43mensure_key_mapped\u001b[49m\u001b[43m(\u001b[49m\u001b[43mitems\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    402\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m nargsort(\n\u001b[1;32m    403\u001b[0m         items,\n\u001b[1;32m    404\u001b[0m         kind\u001b[38;5;241m=\u001b[39mkind,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    408\u001b[0m         mask\u001b[38;5;241m=\u001b[39mmask,\n\u001b[1;32m    409\u001b[0m     )\n\u001b[1;32m    411\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(items, ABCRangeIndex):\n", "File \u001b[0;32m~/miniforge3/envs/ete3/lib/python3.12/site-packages/pandas/core/sorting.py:569\u001b[0m, in \u001b[0;36mensure_key_mapped\u001b[0;34m(values, key, levels)\u001b[0m\n\u001b[1;32m    566\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(values, ABCMultiIndex):\n\u001b[1;32m    567\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m _ensure_key_mapped_multiindex(values, key, level\u001b[38;5;241m=\u001b[39mlevels)\n\u001b[0;32m--> 569\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[43mkey\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvalues\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcopy\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    570\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(result) \u001b[38;5;241m!=\u001b[39m \u001b[38;5;28mlen\u001b[39m(values):\n\u001b[1;32m    571\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mV<PERSON><PERSON><PERSON>rror\u001b[39;00m(\n\u001b[1;32m    572\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUser-provided `key` function must not change the shape of the array.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    573\u001b[0m     )\n", "Cell \u001b[0;32mIn[7], line 12\u001b[0m, in \u001b[0;36m<lambda>\u001b[0;34m(column)\u001b[0m\n\u001b[1;32m     10\u001b[0m \u001b[38;5;66;03m#sort list manually \u001b[39;00m\n\u001b[1;32m     11\u001b[0m sorter\u001b[38;5;241m=\u001b[39m[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mB.vulgatus\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mB.fragilis\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mB.ovatus\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mB.theta\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mB.finegoldii\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mB.uniformis\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mP.copri\u001b[39m\u001b[38;5;124m'\u001b[39m,\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mP.distastonis\u001b[39m\u001b[38;5;124m'\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mE.rectale\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mR.intestinalis\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mF.prausnitzii\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mR.bromii\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mB.longum\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mB.adolescentis\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mC.aerofaciens\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mE.coli\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mE.coliI\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mE.coliII\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFecal\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFecalPP\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mECOR\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mE.halli\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[0;32m---> 12\u001b[0m dataout_av\u001b[38;5;241m.\u001b[39msort_values(by\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mspecies\u001b[39m\u001b[38;5;124m\"\u001b[39m, key\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mlambda\u001b[39;00m column: \u001b[43mcolumn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmap\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43;01mlambda\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43me\u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43msorter\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mindex\u001b[49m\u001b[43m(\u001b[49m\u001b[43me\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m, inplace\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[1;32m     14\u001b[0m display(dataout_av)\n\u001b[1;32m     16\u001b[0m \u001b[38;5;66;03m#speciesall=speciesinformation[\"species_HPLCname\"]\u001b[39;00m\n", "File \u001b[0;32m~/miniforge3/envs/ete3/lib/python3.12/site-packages/pandas/core/series.py:4700\u001b[0m, in \u001b[0;36mSeries.map\u001b[0;34m(self, arg, na_action)\u001b[0m\n\u001b[1;32m   4620\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mmap\u001b[39m(\n\u001b[1;32m   4621\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m   4622\u001b[0m     arg: Callable \u001b[38;5;241m|\u001b[39m Mapping \u001b[38;5;241m|\u001b[39m Series,\n\u001b[1;32m   4623\u001b[0m     na_action: Literal[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mignore\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m   4624\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Series:\n\u001b[1;32m   4625\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m   4626\u001b[0m \u001b[38;5;124;03m    Map values of Series according to an input mapping or function.\u001b[39;00m\n\u001b[1;32m   4627\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   4698\u001b[0m \u001b[38;5;124;03m    dtype: object\u001b[39;00m\n\u001b[1;32m   4699\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m-> 4700\u001b[0m     new_values \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_map_values\u001b[49m\u001b[43m(\u001b[49m\u001b[43marg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mna_action\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mna_action\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   4701\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_constructor(new_values, index\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mindex, copy\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m)\u001b[38;5;241m.\u001b[39m__finalize__(\n\u001b[1;32m   4702\u001b[0m         \u001b[38;5;28mself\u001b[39m, method\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmap\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   4703\u001b[0m     )\n", "File \u001b[0;32m~/miniforge3/envs/ete3/lib/python3.12/site-packages/pandas/core/base.py:921\u001b[0m, in \u001b[0;36mIndexOpsMixin._map_values\u001b[0;34m(self, mapper, na_action, convert)\u001b[0m\n\u001b[1;32m    918\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(arr, ExtensionArray):\n\u001b[1;32m    919\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m arr\u001b[38;5;241m.\u001b[39mmap(mapper, na_action\u001b[38;5;241m=\u001b[39mna_action)\n\u001b[0;32m--> 921\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43malgorithms\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmap_array\u001b[49m\u001b[43m(\u001b[49m\u001b[43marr\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmapper\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mna_action\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mna_action\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconvert\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mconvert\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniforge3/envs/ete3/lib/python3.12/site-packages/pandas/core/algorithms.py:1743\u001b[0m, in \u001b[0;36mmap_array\u001b[0;34m(arr, mapper, na_action, convert)\u001b[0m\n\u001b[1;32m   1741\u001b[0m values \u001b[38;5;241m=\u001b[39m arr\u001b[38;5;241m.\u001b[39mastype(\u001b[38;5;28mobject\u001b[39m, copy\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m)\n\u001b[1;32m   1742\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m na_action \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m-> 1743\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mlib\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmap_infer\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvalues\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmapper\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconvert\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mconvert\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1744\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1745\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m lib\u001b[38;5;241m.\u001b[39mmap_infer_mask(\n\u001b[1;32m   1746\u001b[0m         values, mapper, mask\u001b[38;5;241m=\u001b[39misna(values)\u001b[38;5;241m.\u001b[39mview(np\u001b[38;5;241m.\u001b[39muint8), convert\u001b[38;5;241m=\u001b[39mconvert\n\u001b[1;32m   1747\u001b[0m     )\n", "File \u001b[0;32mlib.pyx:2972\u001b[0m, in \u001b[0;36mpandas._libs.lib.map_infer\u001b[0;34m()\u001b[0m\n", "Cell \u001b[0;32mIn[7], line 12\u001b[0m, in \u001b[0;36m<lambda>\u001b[0;34m(e)\u001b[0m\n\u001b[1;32m     10\u001b[0m \u001b[38;5;66;03m#sort list manually \u001b[39;00m\n\u001b[1;32m     11\u001b[0m sorter\u001b[38;5;241m=\u001b[39m[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mB.vulgatus\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mB.fragilis\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mB.ovatus\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mB.theta\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mB.finegoldii\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mB.uniformis\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mP.copri\u001b[39m\u001b[38;5;124m'\u001b[39m,\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mP.distastonis\u001b[39m\u001b[38;5;124m'\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mE.rectale\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mR.intestinalis\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mF.prausnitzii\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mR.bromii\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mB.longum\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mB.adolescentis\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mC.aerofaciens\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mE.coli\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mE.coliI\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mE.coliII\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFecal\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFecalPP\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mECOR\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mE.halli\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[0;32m---> 12\u001b[0m dataout_av\u001b[38;5;241m.\u001b[39msort_values(by\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mspecies\u001b[39m\u001b[38;5;124m\"\u001b[39m, key\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mlambda\u001b[39;00m column: column\u001b[38;5;241m.\u001b[39mmap(\u001b[38;5;28;01mlambda\u001b[39;00m e: \u001b[43msorter\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mindex\u001b[49m\u001b[43m(\u001b[49m\u001b[43me\u001b[49m\u001b[43m)\u001b[49m), inplace\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[1;32m     14\u001b[0m display(dataout_av)\n\u001b[1;32m     16\u001b[0m \u001b[38;5;66;03m#speciesall=speciesinformation[\"species_HPLCname\"]\u001b[39;00m\n", "\u001b[0;31mValueError\u001b[0m: 'B.hydrogenotrophica' is not in list"]}], "source": ["###################\n", "#decide what to plot\n", "####################\n", "\n", "#use a table with average values  (to generate this table, run Final_analysis_hplcdata.ipynb\n", "dataout_av=pd.read_csv(\"data_hplc/analysis_out_av.csv\")\n", "display(dataout_av.head())\n", "display(speciesinformation.head())\n", "\n", "#sort list manually \n", "sorter=[\"B.vulgatus\",\"<PERSON>.fragilis\",\"<PERSON>.ovatus\",\"B.theta\",\"<PERSON><PERSON>ii\",\"<PERSON>.uniformis\",'<PERSON>.copri','<PERSON><PERSON>distastonis',\"<PERSON>.rectale\",\"<PERSON>.intestinalis\",\"<PERSON><PERSON>ii\",\"<PERSON>.bromii\",\"<PERSON>.longum\",\"B.adolescentis\",\"C.aerofaciens\",\"E.coli\",\"E.coliI\",\"E.coliII\",\"Fecal\",\"FecalPP\",\"ECOR\",\"E.halli\"]\n", "dataout_av.sort_values(by=\"species\", key=lambda column: column.map(lambda e: sorter.index(e)), inplace=True)\n", "\n", "display(dataout_av)\n", "\n", "#speciesall=speciesinformation[\"species_HPLCname\"]\n", "speciesall=[\"<PERSON>.vulgatus\",\"<PERSON>.fragilis\",\"<PERSON>.ovatus\",\"B.theta\",\"<PERSON>.<PERSON>ii\",\"<PERSON>.uniformis\",'<PERSON>.copri','<PERSON><PERSON>distastonis',\"<PERSON>.rectale\",\"<PERSON>.intestinalis\",\"<PERSON><PERSON>ii\",\"<PERSON>.bromii\",\"<PERSON>.longum\",\"<PERSON>.adolescentis\",\"C.aerofaciens\",\"E.coli\"]\n", "\n", "samplenamelist=[[],[],[],[]]\n", "for species in speciesall:\n", "    mc=-1\n", "    for medium in [\"e\",\"BHI\",\"simple\",\"YCA\"]:\n", "            mc=mc+1\n", "            selectc=dataout_av.loc[(dataout_av[\"experiment_short\"]==\"av\") & (dataout_av[\"species\"]==species) & (dataout_av[\"medium\"]==medium)]\n", "            #print(selectc)\n", "            for il in range(0,selectc.shape[0]):\n", "                samplenamelist[mc].append(selectc[\"exp_number\"].iloc[il])\n", "print(\"list output\")\n", "for mc in range(0,len(samplenamelist)):\n", "    print(samplenamelist[mc])\n", "\n", "print(samplenamelist[3])\n", "\n", "sublistshort=['glu','mal','ace','but','for','lac','pro','suc']\n", "\n", "\n", "#colorlist=['b','b','#1b9e77','#66a61e','#a6761d','#e7298a','#d95f02','#7570b3']\n", "\n", "\n", "#colorlist = met_brewer.met_brew(name=\"Egypt\", n=8, brew_type=\"continuous\")\n", "#print(colorlist)\n", "\n", "colorlist=['#dd5129', '#1e8b99', '#2c7591', '#85635d', '#34a28d', '#fab255', '#acb269', '#5db27d']\n", "#['glucose', 'maltose', 'acetate', 'butyrate', 'formate', 'lactate', 'propionate', 'succinate']\n", "#sublist=['glucose','maltose','acetate','butyrate','formate','lactate','propionate','succinate'] #skipp ethanol here\n", "\n", "colorlist=['#dd5129', '#85635d', '#2c7591', '#34a28d', '#fab255','#5db27d', '#1e8b99','#acb269']\n", "\n", "sublist=['glucose','maltose','acetate','butyrate','formate','lactate','propionate','succinate'] #skipp ethanol here\n", "print(sublist)\n", "\n", "markerlist=['s','h','v','^','<','>','d','o']\n", "energycontent=np.array([0.68,1.36,0.21,0.52,0.,.33,0.37,0.36]) #kcal/mmol #the energy per mm for different fermentation products3\n", "cfactorlist=[6,12,2,4,1,3,3,4]\n", "markerlist=['s','v','^','<','>','d','o','h']\n", "   \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}