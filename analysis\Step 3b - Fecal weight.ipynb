{"cells": [{"cell_type": "markdown", "id": "13e1a71f-e2a4-4d98-8d13-f08c63aecc97", "metadata": {}, "source": ["# British diet - Fecal mass distribution"]}, {"cell_type": "code", "execution_count": 1, "id": "f74089e7-5aa4-4f70-be31-35445c265c47", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'energy': 2275.0,\n", " 'carbohydrates': 276.75,\n", " 'sugars': 59.0,\n", " 'proteins': 72.05000000000001,\n", " 'fat': 105.5,\n", " 'fiber': 19.9,\n", " 'fiber_low': 14.599999999999998,\n", " 'fiber_high': 25.2,\n", " 'carbLI_standard': 35.670500000000004,\n", " 'carbLI_higher': 44.6025,\n", " 'carbLI_lower': 25.755000000000003,\n", " 'carbLI_error': 9.423749999999998,\n", " 'bacwetweight': 117.72413793103448,\n", " 'fecalwetmass': 117.72413793103448,\n", " 'fecaldrymass': 29.58620689655172,\n", " 'fecaldrymassstd': 6.845624030794191,\n", " 'fecealfractionbac': 0.546888888888889,\n", " 'bacterialdrymass_feces': 16.180367816091955,\n", " 'energybacteria_fromfeces': 100.84956469130942,\n", " 'FP_fromfeces': 466.61884926595684,\n", " 'FP_fromfeces_g': [8.273028254513578,\n", "  2.679218579054624,\n", "  6.2112485745426635,\n", "  8.917506078661688,\n", "  1.5865312839653274,\n", "  5.089146712515624],\n", " 'FP_fromfeces_gsum': 32.75667948325351,\n", " 'energyfrac_fromfeces': 0.044329478985190955,\n", " 'energybacteria_fromfeces_error': 23.334461425213142,\n", " 'energyfrac_fromfeces_error': 0.01025690612097281,\n", " 'FP_fromfeces_error': 107.96575643932452,\n", " 'bacterialdrymass_feces_error': 3.743795719952113,\n", " 'energy_fromcarbs_standard': 97.849963476916,\n", " 'FPlist_fromcarbs_standard': [133.6668370726343,\n", "  29.5032313525979,\n", "  130.92560068770663,\n", "  96.05095261662028,\n", "  20.779742854909536,\n", "  41.81368935697409],\n", " 'FP_fromcarbs_standard': 452.74005394144274,\n", " 'FP_fromcarbs_g_standard': [8.026960899885836,\n", "  2.5995297144774008,\n", "  6.026505399655137,\n", "  8.652269811705155,\n", "  1.5393425709488433,\n", "  4.93777857616507],\n", " 'FP_fromcarbs_gsum_standard': 31.782386972837443,\n", " 'ferm_fromcarbs_cc_standard': 1034.0190440858526,\n", " 'drymass_fromcarbs_standard': 15.699109903883397,\n", " 'energyfrac_fromcarbs_standard': 0.043010972956886155,\n", " 'energy_fromcarbs_lower': 70.65013973305594,\n", " 'FPlist_fromcarbs_lower': [96.51082515820347,\n", "  21.302076603528377,\n", "  94.53158340118263,\n", "  69.35120855163386,\n", "  15.00349805099999,\n", "  30.190537541914683],\n", " 'FP_fromcarbs_lower': 326.889729307463,\n", " 'FP_fromcarbs_g_lower': [5.795668072400434,\n", "  1.8769259695368854,\n", "  4.351288783956436,\n", "  6.247156866331178,\n", "  1.1114441321200281,\n", "  3.565200578324705],\n", " 'FP_fromcarbs_gsum_lower': 22.947684402669665,\n", " 'ferm_fromcarbs_cc_lower': 746.5878101072634,\n", " 'drymass_fromcarbs_lower': 11.335153013681246,\n", " 'energy_fromcarbs_higher': 122.35188730124743,\n", " 'FPlist_fromcarbs_higher': [167.13741328358648,\n", "  36.89092881804987,\n", "  163.70976310041732,\n", "  120.10239873516788,\n", "  25.983052681798757,\n", "  52.28396236510385],\n", " 'FP_fromcarbs_higher': 566.1075189841242,\n", " 'FP_fromcarbs_g_higher': [10.036935942505936,\n", "  3.2504597381583746,\n", "  7.53556039551221,\n", "  10.818824078063923,\n", "  1.92479855961497,\n", "  6.174213115695114],\n", " 'FP_fromcarbs_gsum_higher': 39.74079182955053,\n", " 'ferm_fromcarbs_cc_higher': 1292.940508651105,\n", " 'drymass_fromcarbs_higher': 19.63021402806126,\n", " 'energyfrac_fromcarbs_lower': 0.031055006476068545,\n", " 'energyfrac_fromcarbs_higher': 0.05378104936318568,\n", " 'FP_fromcarbs_standard_error': 119.60889483833054,\n", " 'FP_fromcarbs_gsum_standard_error': 8.396553713440428,\n", " 'drymass_fromcarbs_standard_error': 4.147530507190006,\n", " 'energy_fromcarbs_standard_error': 25.850873784095736,\n", " 'energyfrac_fromcarbs_standard_error': 0.011363021443558566,\n", " 'FP_infeces': 9.041213793103447,\n", " 'FP_infeces_error': 9.41793103448276}"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#load required functions\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import matplotlib\n", "matplotlib.rcParams['pdf.fonttype'] = 42\n", "matplotlib.rcParams['ps.fonttype'] = 42\n", "from matplotlib.ticker import PercentFormatter\n", "import scipy\n", "\n", "import json\n", "import os\n", "\n", "capsize=6\n", "#set colorscheme\n", "colorav='k'\n", "colorrefdiet='purple'\n", "colorlistferm=['#1b9e77','#d95f02','#7570b3','#e7298a','#66a61e']\n", "\n", "labelenergybac=\"energy supply via bacteria (kcal)\"\n", "labelenergybacfrac=\"enery supply via bacteria (%)\"\n", "labelenergybacfracnounit=\"energy supply via bacteria\"\n", "\n", "results_for_figure2={}\n", "import FPcalc #basic calculations of \n", "\n", "\n", "#dict to save major characteristics of British reference diet\n", "#load file if already exists, otherwise start with empty dict\n", "try:\n", "    with open('data_analysisresults/BRD_characteristics.json', 'r') as fp:\n", "        BRD = json.load(fp)\n", "except:\n", "    BRD={}\n", "    \n", "display(BRD)\n", "import FPcalc #basic calculations of \n"]}, {"cell_type": "markdown", "id": "86c9f320-3941-4e38-bccc-aad9de20f6b6", "metadata": {"tags": []}, "source": ["# Analysis of fecal dry and wet weight data"]}, {"cell_type": "code", "execution_count": 2, "id": "032123d9-7a59-409f-953d-a9ecdc59a28f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/4b/8dffvbfs5qs93rh77f2zxs340000gn/T/ipykernel_40942/2054786765.py:29: FutureWarning: `rcond` parameter will change to the default of machine precision times ``max(M, N)`` where M and N are the input matrix dimensions.\n", "To use the future default and silence this warning we advise to pass `rcond=None`, to keep using the old, explicitly pass `rcond=-1`.\n", "  drymassfraction_fit, _, _, _ = np.linalg.lstsq(xcolvec, y)\n"]}, {"data": {"text/plain": ["'relation between wet and dry weight (slope and offset)'"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["array([0.24141097])"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "av and std wet weight (g/day)\n", "117.72413793103448\n", "38.07424376202291\n", "av and std dry weight (g/day)\n", "29.58620689655172\n", "6.845624030794191\n", "fraction of fecal wet weight ending up as dry weight*** to be used for Burit data\n", "0.25131810193321613\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x250 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x150 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 200x350 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "#load data on weight distribution of different studies\n", "BDdata=pd.read_csv('data/weightdistribution.csv',skiprows=1)\n", "\n", "########################################\n", "#plot relation between wet and dry weight for British reference diet\n", "#########################################\n", "\n", "fig, axs = plt.subplots(1,2, figsize=(6,2.5))\n", "#axs=[axs]\n", "\n", "#plot dry and wet weight data for each study\n", "axs[0].plot(BDdata['WET_WymanTable1'],BDdata['DRY_WymanTable1'],marker='o',ls='',c='b',label='<PERSON><PERSON> et al - female',fillstyle='none')\n", "axs[0].plot(BDdata['WET_WymanTable3'],BDdata['DRY_WymanTable3'],marker='s',ls='',c='r',label='<PERSON><PERSON> et al - male',fillstyle='none')\n", "axs[0].plot(BDdata['WET_<PERSON>'],BDdata['DRY_<PERSON>'],marker='v',ls='',c='m',label='<PERSON> et al ',fillstyle='none')\n", "#linear fits between dry weight and wet weight for each study\n", "fitc1=np.polyfit(BDdata['WET_WymanTable1'],BDdata['DRY_WymanTable1'],1)\n", "fitc2=np.polyfit(BDdata['WET_WymanTable3'],BDdata['DRY_WymanTable3'],1)\n", "fitc3=np.polyfit(BDdata['WET_<PERSON>'][:-1],BDdata['DRY_<PERSON>'][:-1],1)\n", "\n", "#consideration of all points together\n", "x=np.concatenate((BDdata['WET_WymanTable1'],BDdata['WET_WymanTable3'],BDdata['WET_Stephen'][:-1]),axis=0)\n", "BRD[\"bacwetweight\"]=x.mean()\n", "y=np.concatenate((BDdata['DRY_WymanTable1'],BDdata['DRY_WymanTable3'],BDdata['DRY_Stephen'][:-1]),axis=0)\n", "xlin=np.linspace(0,200,10)\n", "\n", "# Our model is y = a * x, so things are quite simple, in this case...\n", "# x needs to be a column vector instead of a 1D vector for this, however.\n", "xcolvec = x[:,np.newaxis]\n", "drymassfraction_fit, _, _, _ = np.linalg.lstsq(xcolvec, y)\n", "\n", "#plot ifts\n", "axs[0].set_xlabel('fecal wet weight (g/day)')\n", "axs[0].set_ylabel('fecal dry weight (g/day)')\n", "\n", "axs[1].plot(BDdata['WET_WymanTable1'],100*(BDdata['WET_WymanTable1']*drymassfraction_fit-BDdata['DRY_WymanTable1'])/BDdata['WET_WymanTable1'],marker='o',ls='',c='b',label='<PERSON>yman et al - female',fillstyle='none')\n", "axs[1].plot(BDdata['WET_WymanTable3'],100*(BDdata['WET_WymanTable3']*drymassfraction_fit-BDdata['DRY_WymanTable3'])/BDdata['WET_WymanTable3'],marker='s',ls='',c='r',label='<PERSON><PERSON> et al - male',fillstyle='none')\n", "axs[1].plot(BDdata['WET_<PERSON>'],100*(BDdata['WET_<PERSON>']*drymassfraction_fit-BDdata['DRY_<PERSON>'])/BDdata['WET_<PERSON>'],marker='v',ls='',c='m',label='<PERSON> et al ',fillstyle='none')\n", "\n", "axs[0].plot(xlin,xlin*drymassfraction_fit,ls='--',c='k',label='fit, fixed fraction')\n", "\n", "axs[1].set_xlabel('fecal wet weight (g/day)')\n", "axs[1].set_ylabel('relative error (%)')\n", "#plot relation\n", "display(\"relation between wet and dry weight (slope and offset)\")\n", "display(drymassfraction_fit)\n", "\n", "#plot averages into plot as well\n", "#axs[0].axvline(x.mean(),c='k',ls=\":\",label='mean all data')\n", "#axs[0].axhline(y.mean(),c='k',ls=':')\n", "#axs[0].axvline(BDdata['WET_<PERSON>'][:-1].mean(),ls=':',c='g',label='mean Stephen')\n", "#axs[0].axhline(BDdata['DRY_<PERSON>'][:-1].mean(),ls=':',c='g')\n", "#axs[0].legend()\n", "\n", "print()\n", "print('av and std wet weight (g/day)')\n", "print(x.mean())\n", "print(x.std())\n", "BRD[\"fecalwetmass\"]=x.mean()\n", "\n", "\n", "print(\"av and std dry weight (g/day)\")\n", "print(y.mean())\n", "print(y.std())\n", "\n", "BRD[\"fecaldrymass\"]=y.mean()\n", "BRD[\"fecaldrymassstd\"]=y.std()\n", "\n", "print('fraction of fecal wet weight ending up as dry weight*** to be used for Burit data')\n", "print(y.mean()/x.mean())\n", "axs[0].legend()\n", "fig.tight_layout()\n", "fig.savefig(\"plot_output_figure2/BRITISH_fecal_scatter.pdf\")\n", "\n", "\n", "############\n", "# plot histogram of all fecal mass data\n", "###########\n", "\n", "fig, axs = plt.subplots(1,2, figsize=(6,1.5))\n", "\n", "axs[0].hist(x,label=\"wet\",color='gray')\n", "axs[1].hist(y,label=\"dry\",color='gray')\n", "axs[0].set_xlabel(\"fecal wet mass (g/day)\")\n", "axs[1].set_xlabel(\"fecal dry mass (g/day)\")\n", "\n", "axs[0].set_ylabel(\"# samples\")\n", "axs[1].set_ylabel(\"# samples\")\n", "\n", "\n", "axs[0].axvline(BRD[\"bacwetweight\"],color='k',ls='--')\n", "axs[1].axvline(BRD[\"fecaldrymass\"],color='k',ls='--')\n", "\n", "\n", "axs[0].set_xlim(0,200)\n", "axs[1].set_xlim(0,50)\n", "            \n", "fig.tight_layout()\n", "fig.savefig(\"plot_output_figure2/BRITISH_fecal_hist.pdf\")\n", "\n", "############\n", "# plot box plots with distribution of wet and dry weight\n", "###########\n", "fig, axs = plt.subplots(1,1, figsize=(2,3.5))\n", "axs=[axs]\n", "\n", "databox=[x,y]\n", "bplot1=axs[0].boxplot(databox,labels=[\"wet\",\"dry\"],vert=True,showfliers=True, widths=[0.7]*2,patch_artist=True)#,\"<PERSON><PERSON> (autoclaved lab coy)\"])\n", "\n", "colors=[colorrefdiet]*2\n", "for patch, color in zip(bplot1['boxes'], colors):\n", "        patch.set_facecolor(color)\n", "\n", "\n", "axs[0].set_ylabel('fecal mass (g/day)')\n", "axs[0].set_ylim(0,200)\n", "fig.tight_layout()\n", "fig.savefig(\"plot_output_figure2/BRITISH_fecal_box.pdf\")\n"]}, {"cell_type": "markdown", "id": "81cfee6d-5f6d-4636-84f9-5f4756da0034", "metadata": {}, "source": ["# A more detailed analysis of water content in feces"]}, {"cell_type": "code", "execution_count": 3, "id": "5e3e5f34-efea-4a12-9693-e65934fdc460", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["funct parwater lin\n", "[4.63384822e-04 6.85305059e-01]\n", "funct parwater 3\n", "[ 0.49246012  0.03076236 -0.00070195]\n", "pearson coefficient - fecal drymass fraction and wet weight\n", "[[1.         0.69976807]\n", " [0.69976807 1.        ]]\n", "pearson coefficient - transit time and wet weight\n", "[[ 1.         -0.54258485]\n", " [-0.54258485  1.        ]]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/4b/8dffvbfs5qs93rh77f2zxs340000gn/T/ipykernel_40942/1642038686.py:115: RuntimeWarning: divide by zero encountered in divide\n", "  return a +b/(x)\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAt8AAAEJCAYAAABMhUtrAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/bCgiHAAAACXBIWXMAAA9hAAAPYQGoP6dpAAEAAElEQVR4nOydd3hUxdeA37ub7GaTTe89gVACBAhNqoBSFQQBQeCHYMEGCEoQsSAqTRBQ8bMhAoKAShNBugSQXqW3mNCSkIT0nt2d74+YazaNBFJA7vs894G9M3fmzN3NzLnnnjlHEkIIFBQUFBQUFBQUFBSqHFVNC6CgoKCgoKCgoKDwoKAo3woKCgoKCgoKCgrVhKJ8KygoKCgoKCgoKFQTivKtoKCgoKCgoKCgUE0oyreCgoKCgoKCgoJCNaEo3woKCgoKCgoKCgrVhKJ8KygoKCgoKCgoKFQTivKtoKCgoKCgoKCgUE0oyreCgoKCgoKCgoJCNaEo3woKCgoKCgoKCgrVhKJ83wVTpkxBkiSzw8PDQy4XQjBlyhS8vLzQ6XR06tSJM2fO1KDECgoKCgoKCgoKNYmifN8lDRs2JCYmRj5OnToll82aNYu5c+fyxRdfcPjwYTw8POjatStpaWk1KLGCgoKCgoKCgkJNoSjfd4mFhQUeHh7y4erqCuRbvT/99FPeeecd+vXrR6NGjViyZAmZmZksX768hqVWUFBQUFBQUFCoCSxqWoD7nUuXLuHl5YVWq+Whhx5i+vTp1KpVi8jISGJjY+nWrZtcV6vV0rFjR/bt28dLL71UYns5OTnk5OTIn00mE4mJiTg7OyNJUpWPR0FB4cFDCEFaWhpeXl6oVP89m4zJZCI6OhpbW1tlHlVQUKgSKjKPKsr3XfDQQw/xww8/ULduXW7evMnUqVNp27YtZ86cITY2FgB3d3eza9zd3bly5Uqpbc6YMYMPPvigSuVWUFBQKIlr167h4+NT02JUOtHR0fj6+ta0GAoKCg8A5ZlHFeX7LujZs6f8/5CQENq0aUPt2rVZsmQJrVu3BihmZRFClGl5mTRpEm+88Yb8OSUlBT8/P65du4adnV0lj0BBQUEBUlNT8fX1xdbWtqZFqRIKxlWRefTjjz9m+vTpDB8+nM8//7wqxVNQUPgPUJF5VFG+KxEbGxtCQkK4dOkSffv2BSA2NhZPT0+5TlxcXDFreGG0Wi1arbbY+bi4OBwdHbGxsal0uRUUFBSguLHgv0LBuOzs7MqtfDdr1owePXoQGhqqGD4UFBTKTXnm0f+ec18NkpOTw7lz5/D09CQwMBAPDw+2bdsml+fm5rJr1y7atm1b4bZ/+eUXrl69WpniKigoKCiUQv/+/dm0aRNjx46taVEUFBT+YyiW77sgLCyM3r174+fnR1xcHFOnTiU1NZXhw4cjSRLjxo1j+vTp1KlThzp16jB9+nSsra0ZMmRIhfsqiKKioKCgoKCgoKBw/6Io33fB9evXGTx4MAkJCbi6utK6dWsOHDiAv78/AG+++SZZWVm8+uqrJCUl8dBDD7F169Y78qt84YUXlFefCgoKCtVMbm4uGo2mpsVQUFD4DyEJIURFLjAajeTl5VWVPApFSE9Pp0WLFhw5cgS9Xl/T4igoKNyHWFpaolarSy1PTU3F3t6elJSU/+RD/p2MLy4ujqCgIDIzM8nNzf1PhmC8WxR9QOFBQ6PRlDoXVGSeKbflWwhBbGwsycnJFRJU4e4wmUx8/fXX3Lx5k/j4+JoWR0FB4T7FwcEBDw+P/+ymysrGwcFBzkacnJyMk5NTDUt076DoAwoPKiqVisDAwLt+G1Zu5bvgD83NzQ1ra2tlAq8mjEYjWVlZODk5YTQacXBwwMJC8RZSUFAoH0IIMjMziYuLAzCLvlQd7N69m9mzZ3P06FFiYmJYu3atHA0KYMSIESxZssTsmoceeogDBw7In3NycggLC2PFihVkZWXx6KOP8uWXX1ZpTHKNRoOdnR2pqancunVLUb4LoegDCg8iBcm6YmJi8PPzu6vffbm0OKPRKP+hOTs733FnChXHaDQC+U9bQggsLCywsrKqYakUFBTuJ3Q6HZDvSuHm5lamC0plk5GRQZMmTXj22Wfp379/iXV69OjBokWL5M9FrUrjxo3jt99+Y+XKlTg7OzN+/Hh69erF0aNHq3QsLi4upKamkpCQQJ06daqsn/sJRR9QeJBxdXUlOjoag8GApaXlHbdTLuW7wKfL2tr6jjtSuDtsbW1Rq9V39WUrKCg8uBTM33l5edWqfPfs2dMsIVlJaLVaPDw8SixLSUlh4cKFLF26lC5dugCwbNkyfH192b59O927d690mQtwcXHh77//JiEhocr6uN9Q9AGFB5kCw4DRaLwrfaxCO0iUV0s1R0HyHWXTj4KCwp1wL8/f4eHhuLm5UbduXUaOHCm7yAAcPXqUvLw8unXrJp/z8vKiUaNG7Nu3r8T2cnJySE1NNTvuBBcXFwAzeRTyuZd/TwoKVUVl/e4VTU5BQUFBocbo2bMnP/74I3/88Qdz5szh8OHDPPLII+Tk5AD5/sUajQZHR0ez69zd3YmNjS2xzRkzZmBvby8fvr6+dyRbgTW+tH4UFBQU7gRF+a4ioqKikCSJEydOVEp7Qgiys7NJT0+ngtEhK43Fixfj4OBQI31XJSNGjDDbAKagoFB9DBo0iMcff5xGjRrRu3dvNm3axMWLF9m4cWOZ1wkhSrVCTZo0iZSUFPm4du3aHcnWvHlzevbsSUBAwB1dr/DgcLt1ZMqUKTRt2rTa5KkswsPDkSRJiWxTyVSr8m0Sgptp2UQlZnIzLRtTFSuRcXFxvPTSS/j5+ck+hd27d2f//v1yHUmSWLduXZXKUVkkJiaSmpoqb8K8V9m5cyePPfYYzs7OWFtb06BBA8aPH8+NGzdqWrQS+eyzz1i8eHFNi2FGwcObm5ubHO6sgKZNmzJlypRyt3UvTJ5nzpyhf//+BAQEIEkSn376abE6aWlpjBs3Dn9/f3Q6HW3btuXw4cNmddLT0xk9ejQ+Pj7odDqCg4P56quvzOrk5OQwZswYXFxcsLGx4YknnuD69etVOTyFSsTT0xN/f38uXboE5Fufc3NzSUpKMqsXFxeHu7t7iW1otVrs7OzMjjvh1Vdf5ffff2fo0KF3dL1C6RiNRsLDw1mxYgXh4eFVvq6NGDECSZLkw9nZmR49enDy5Mkq7beAsLAwduzYYSZPUWW9Mo12nTp1Yty4cXfdjkLVUG3K97XkLDacjeWPywnsv5LIH5cT2HA2lmvJWVXWZ//+/fnrr79YsmQJFy9eZP369XTq1InExMQq67OqkCQJKyurez7SyTfffEOXLl3w8PBg9erVnD17lq+//pqUlBTmzJlT0+KViL29/T1r0U9LS+OTTz6paTFk7jShRmZmJrVq1WLmzJmlbqx74YUX2LZtG0uXLuXUqVN069aNLl26mD20vf7662zevJlly5Zx7tw5Xn/9dcaMGcOvv/4q1xk3bhxr165l5cqV/Pnnn6Snp9OrV697/qFVIZ9bt25x7do1OSRi8+bNsbS0ZNu2bXKdmJgYTp8+Tdu2bWtKTIW7YM2aNQQFBdG5c2eGDBlC586dCQoKYs2aNVXab48ePYiJiSEmJoYdO3ZgYWFBr1697qpNo9GIyWS6bT29Xq9Eh1H4F1EOsrKyxNmzZ0VWVlZ5qhfjalKmWH7smth1OV7Ep2eLXINRxKdni12X48XyY9fE1aTMO2q3LJKSkgQgwsPDS63j7+8vAPnw9/eXy9avXy+aNWsmtFqtCAwMFFOmTBF5eXlyOSC+/PJL0aNHD2FlZSUCAgLEzz//LJdHRkYKQKxevVp06tRJ6HQ60bhxY7Fv3z4zGfbu3Ss6dOggrKyshI+PjxgzZoxIT083k/HVV18VI0aMEHq9Xvj6+opvvvmmzLFv2rRJtGvXTtjb2wsnJyfx+OOPi8uXL1dYtkWLFglfX1+h0+lE3759xSeffCLs7e1L7ffatWtCo9GIcePGlVielJQkhBDi/fffF02aNDErmzdvntn9Hz58uOjTp4+YNm2acHNzE/b29vJ3EBYWJhwdHYW3t7dYuHBhsXH99NNPon379sLKykq0aNFCXLhwQRw6dEg0b95c2NjYiO7du4u4uLhifRXQsWNHMWbMGDFhwgTh6Ogo3N3dxfvvv28m77lz50S7du2EVqsVwcHBYtu2bQIQa9euFUIIkZOTI0aNGiU8PDyEVqsV/v7+Yvr06aXeu6IUjGXChAlCr9eLmzdvymVNmjQxk2fp0qWiefPmQq/XC3d3dzF48GC5fkE7hY/hw4cLIfJ/W/PmzTPrt2jbgPjqq6/EE088IaytrcXkyZOFELf/+yiLkvrNzMwUarVabNiwoZg877zzjvy5YcOG4sMPPzSr06xZM/Huu+8KIYRITk4WlpaWYuXKlXL5jRs3hEqlEps3by6XfP9VyprHU1JSBCBSUlIqvd+0tDRx/Phxcfz4cQGIuXPniuPHj4srV66ItLQ0MX78eLFv3z4RGRkpdu7cKdq0aSO8vb1Famqq3MbLL78sfHx8xPbt28WxY8fEI488Ipo0aSIMBkO5ZLjb8d3p2vdf5G71gdWrVwtJkkTv3r3F/v37RVpamti/f7/o3bu3kCRJrF69upIlzqfoPC+EELt37xaAvB7s3LlTAPJaJYSQf7eRkZFCiPx10d7eXvz2228iODhYqNVq8ffffxdr/8iRI8LV1VVMnTpVCGG+7r3//vvF5uWCvgsfHTt2LHU8Z86cET179hQ2NjbCzc1N/O9//xPx8fHyWIu2VSB/UcpaP0q7Jw8ylTWPVrnybTSZxK+no8Wuy/HCZDKZlZlMJrHrcrz49XS0MBYpu1vy8vKEXq8X48aNE9nZ2SXWiYuLE4BYtGiRiImJkf8AN2/eLOzs7MTixYtFRESE2Lp1qwgICBBTpkyRrwWEs7OzWLBggbhw4YJ49913hVqtFmfPnhVC/Kv01K9fX2zYsEFcuHBBDBgwQPj7+8tKysmTJ4Verxfz5s0TFy9eFHv37hWhoaFixIgRcj/+/v7C3t5ezJ8/X1y6dEnMmDFDqFQqce7cuVLHvmrVKrF69Wpx8eJFcfz4cdG7d28REhIijEZjuWU7cOCAkCRJzJgxQ1y4cEF89tlnwsHBoUzle+7cuQIQ0dHRZX435VW+bW1txahRo8T58+fFwoULBSC6d+8upk2bJi5evCg++ugjYWlpKa5evVpsXJs3bxZnz54VrVu3Fs2aNROdOnUSf/75pzh27JgICgoSL7/8sllfRZVvOzs7MWXKFHHx4kWxZMkSIUmS2Lp1qxBCCKPRKOrVqye6du0qTpw4Ifbs2SNatWplpnzPnj1b+Pr6it27d4uoqCixZ88esXz58jLvS2EKxnLs2DHRtGlTMWrUKLmsqIK8cOFC8fvvv4uIiAixf/9+0bp1a9GzZ08hhBAGg0GsXr1aAOLChQsiJiZGJCcnCyHKr3y7ubmJhQsXioiICBEVFVWuv4+yKKnf1NRUAYjt27ebnW/durXZAvTSSy+JFi1aiOvXrwuTyST++OMPodfrxZ49e4QQQuzYsUMAIjEx0aydxo0byw8ODyo1pXyXpFQUPARmZmaKbt26CVdXV2FpaSn8/PzE8OHD5b/pwrKPHj1aODk5CZ1OJ3r16lWsTlnc6fiuXbsm7OzshJWVVbH1qzIxGAxi586dYvny5WLnzp3lfqioCe5GHzAYDCIgIED07t1bXo8KMBqNonfv3iIwMLBKxl90nk9LSxMvvfSSCAoKkmUpr/JtaWkp2rZtK/bu3SvOnz8v0tPTzdrfuXOnsLe3F19++aXcTuF1Ly0tTQwcOFD06NFDxMTEiJiYGJGTkyMOHTokz4MxMTHi1q1bJY4lOjpauLi4iEmTJolz586JY8eOia5du4rOnTsLIfKNEG3atBEjR46U2y/tnpa1fpR2Tx5k7hvlOzY1Syw/dk3Ep5esAMenZ4vlx66J2NTKtyysWrVKODo6CisrK9G2bVsxadIk8ddff5nVKawwFdChQ4diVsqlS5cKT09Ps+sKK3BCCPHQQw+JV155RQjxr/L03XffyeVnzpwRgKw4Dxs2TLz44otmbezZs0eoVCr5Xvv7+4uePXvKfzgmk0m4ubmJr776qtz3oeAh49SpU+WWbfDgwaJHjx5m7QwaNKhM5fuVV14RdnZ2t5WnvMq3v7+/2QRdr1490aFDB/mzwWAQNjY2YsWKFaWOa8WKFQIQO3bskM/NmDFD1KtXz6yvosp3+/btzeRr2bKlmDhxohAi/82ChYWFiImJkcuLWr7HjBkjHnnkkTtesAvGcvz4cbF582ZhaWkpv70oqiAXpWACT0tLE0KUPnmWV/ku+iajPH8fZVFSv0II0aZNG9GxY0dx48YNYTAYxNKlS4UkSaJu3bpynZycHPHMM88IQFhYWAiNRiN++OEHufzHH38UGo2mWNtdu3Yt9rf2oFFTyve9wJ2OLzMzU35YqCrlY/Xq1SIgIMDswSQgIKDKLMB3y93oAwVz0f79+0ss37dvn2wFrmyGDx8u1Gq1sLGxETY2NgIQnp6e4ujRo8Xku53yDYgTJ04Ua79Pnz5i3bp1wtbWtpixpei6V5IlvvC8Xxbvvfee6Natm9m5a9euyUYWIfLXsbFjx5bZTkmUd/14UKmsebTKfb6z8vJ9oeytSg5GXnC+oF5l0r9/f6Kjo1m/fj3du3cnPDycZs2a3XZz3dGjR/nwww/R6/XyMXLkSGJiYsjMzJTrtWnTxuy6Nm3acO7cObNzjRs3lv9f4MNYEDP26NGjLF682Kyf7t27YzKZiIyMlK8ryKxmNBqJj4/HxcWFmzdvlip/REQEQ4YMoVatWtjZ2REYGAjA1atXyy3buXPnShxfWYgyog/cCQ0bNjSLa+7u7k5ISIj8Wa1W4+zsXCwGb+FxFWzIKnydu7v7beP2Fm4D8u9PwTUXLlzA19fXzHe5VatWZvVHjBjBiRMnqFevHq+99hpbt24ts7+y6N69O+3bt+e9994rsfz48eP06dMHf39/bG1t6dSpE1D8+75TWrRoYfa5vH8fFWXp0qUIIfD29kar1fL5558zZMgQs4Qwn3/+OQcOHGD9+vUcPXqUOXPm8Oqrr7J9+/Yy267s36bCg4FOp8Pe3h7I9zOvbNasWcOAAQMICQlh//79pKWlsX//fkJCQhgwYECV+0BXNwX3sFGjRiWWF5yvinsN0LlzZ06cOMGJEyc4ePAg3bp1o2fPnly5cqVC7Wg0mmJrBMDBgwfp378/S5YsYfDgwZUldjGOHj3Kzp07zebg+vXrA/nrf0Wo6vVDoWTKleHybtBZ5itPKdl5uNhoi5WnZOeZ1atsrKys6Nq1K127dmXy5Mm88MILvP/++4wYMaLUa0wmEx988AH9+vUrsb2yKLrAF86AVFBWsDnDZDLx0ksv8dprrxVrx8/PT/5/gfKhUqkwGAwAZW4e6927N76+vixYsAAvLy9MJhONGjUiNze33LKJO4hEU7duXVJSUoiJiZGV+ZJQqVTF2i9pI1/R7FGSJJV4ruhml5LGVfTc7TbIlNVPeRS5Zs2aERkZyaZNm9i+fTsDBw6kS5curFq1qszrSmPmzJm0adOGCRMmmJ3PyMigW7dudOvWjWXLluHq6srVq1fp3r17se+7KOX9HmxsbMw+383fR1nUrl2bXbt2kZGRQWpqKp6engwaNEh+eMzKyuLtt99m7dq1PP7440D+Q9KJEyf45JNP5I2+BZExCseFjouLUzbnKdwRnp6e8rwWHBxcae0ajUbGjx9Pr169WLdunWxoaN26NevWraNv376EhYXRp0+fas1IWpUUrAunT5+mdevWxcpPnz5tVq+ysbGxISgoSP7cvHlz7O3tWbBgAVOnTpW/g8LzYklzok6nK3ENqF27Ns7Oznz//fc8/vjjcjbEysZkMtG7d28+/vjjYmUVuXd3s34o3B1Vbvl21Wux0ag5G5tWbKEXQnA2Ng0bjRpXfXHFvCpo0KABGRkZ8mdLS8tiimyzZs24cOECQUFBxY7CltgDBw6YXXfgwAH56bM8NGvWjDNnzpTYT0l/tAXhkSwtLUtV/m7dusW5c+d49913efTRRwkODi4Woqs8NGjQoMTxlcWAAQPQaDTMmjWrxPKCUHeurq7Exsaa/R4qKx56dVC/fn2uXr1q9vahaEg8ADs7OwYNGsSCBQv46aefWL169R1H2mnVqhX9+vXjrbfeMjt//vx5EhISmDlzJh06dKB+/frFrPqF0+EWxtXV1czClJqaavbGpTTK+/dxp9jY2ODp6UlSUhJbtmyhT58+QP4imJeXV6wPtVotPxgpkTEUKpsCZaayrbF79uwhKiqKt99+u9hvWqVSMWnSJCIjI9mzZ0+l9luTdOjQgYCAAKZPn17MAGIymZgxYwaBgYF06NChWuSRJAmVSkVWVn7UNVdXV8D8u67I2uTi4sIff/xBREQEgwYNKjM6lEajKTYnlzZXF6VAdwgICCg2BxcYS0pqvyjlWT8UqoYqt3yrJIlQbwf+jLzFnr9v0cDDFnsrS1Ky8zgbm8aN1GzaBzqjquRXwrdu3eKpp57iueeeo3Hjxtja2nLkyBFmzZolL+YAAQEB7Nixg3bt2qHVanF0dGTy5Mn06tULX19fnnrqKVQqFSdPnuTUqVNMnTpVvvaXX36hRYsWtG/fnh9//JFDhw6xcOHCcss4ceJEWrduzahRoxg5ciQ2NjacO3eObdu2MX/+/BKv0WrzH1JKU74dHR1xdnbm22+/xdPTk6tXrxZT2MrDa6+9Rtu2bZk1axZ9+/Zl69atbN68ucxrfH19mTdvHqNHjyY1NZVnnnmGgIAArl+/zg8//IBer2fOnDl06tSJ+Ph4Zs2axYABA9i8eTObNm2641i81U3Xrl2pXbs2w4cPZ9asWaSlpfHOO+8A/34v8+bNw9PTk6ZNm6JSqfjll1/w8PC4q5CG06ZNo2HDhlhY/Ptn6+fnh0ajYf78+bz88sucPn2ajz76yOw6f39/JEliw4YNPPbYY+h0OvR6PY888giLFy+md+/eODo68t5775XLwlbev4/C5ObmcvbsWfn/N27c4MSJE+j1etkStWXLFoQQ1KtXj8uXLzNhwgTq1avHs88+C+Q/zHTs2JEJEyag0+nw9/dn165d/PDDD8ydOxfIDxv5/PPPM378eJydnXFyciIsLIyQkBC6dOlS8Zuu8MBTVcp3Tbtg1ARqtZo5c+YwYMAA+vbty6RJk2jUqBGnT59mxowZbNiwgVWrVlWZpT8nJ0fOVpqUlMQXX3xBeno6vXv3BiAoKAhfX1+mTJnC1KlTuXTpUoVD5Lq5ufHHH3/QuXNnBg8ezMqVK83m7AICAgLYsmULFy5cwNnZGXt7e9zc3NDpdGzevBkfHx+srKxkt6fCjBo1igULFjB48GAmTJiAi4sLly9fZuXKlSxYsAC1Wk1AQAAHDx4kKioKvV6Pk5NTsYe88qwfClXE3TqYl5erSZni19PRYvmxa/Lx6+noKgkzKIQQ2dnZ4q233hLNmjUT9vb2wtraWtSrV0+8++67IjPz3z7Xr18vgoKChIWFhdmGv82bN4u2bdsKnU4n7OzsRKtWrcS3334rlwPi//7v/0TXrl3lUHIFG/+EKHnjREH4w8KbSQ4dOiS6du0q9Hq9sLGxEY0bNxbTpk2Ty/39/cXrr79utlP5dhvutm3bJoKDg4VWqxWNGzcW4eHhZpsByyvbwoULhY+Pj9DpdKJ37963DTVYuP/u3bvLm13r168vwsLCzKKgfPXVV8LX11fY2NiIZ555RkybNq3EUIOFKWkDSeHNeyWNq6TNIgWhokrrq6R++vTpI4foE+LfUIMajUbUr19f/PbbbwKQw9l9++23omnTpsLGxkbY2dmJRx99VBw7dsysz7LCSJW28ebFF18UgNn3v3z5chEQECC0Wq1o06aNWL9+fbFrP/zwQ+Hh4SEkSZLHkZKSIgYOHCjs7OyEr6+vWLx4cYkbLotuSBbi9n8fpY2n6FH4Hvz000+iVq1aQqPRCA8PDzFq1Cg5MksBMTExYsSIEcLLy0tYWVmJevXqiTlz5phtbL3byBj/VZQNl3c2vvHjxwtAvPHGG5UqU01uPrwbKkMfKGmTaWBgYJVuMi0afs/W1la0bNlSrFq1yqzen3/+KUJCQoSVlZXo0KGD+OWXX0oMNVhS+4XXkejoaFG3bl0xcOBAYTAYim24jIuLk9f+wt/zggULhK+vr1CpVGWuERcvXhRPPvmkcHBwEDqdTtSvX1+MGzdOngsvXLggWrduLXQ6XZmhBm+3figbLs2prHlUEuL2zr3Z2dlERkYSGBh4Vz6dJiGIT88hK8+EzlKFq15b6RbvsjAYDCQnJ5Oenk5OTg4mkwkLCwusra2xt7dHr9eXuy1Jkli7dm2VpyU3Go0cP36c0NBQ1Go1RqOR7OxshBAVklehatm7dy/t27fn8uXL1K5d+7b1O3XqRKdOnSqUqVJB4W4oax5PTU3F3t6elJSU++YNVEW4m/H9+OOPLF++nD59+vDiiy9WmkxGo5GgoCBCQkLMfL4h3wWjb9++nD59mkuXLt1TPt+VpQ8YjUb27Nkj7xHq0KHDPTVOBYWSqKx5tMrdTgqjkiTcbas/Q2Nubi7R0dEkJiZiaWmJtbU11tbW8gbGtLQ0bt68iUajwcvLCycnp2qXsbyYTCZSUlKQJAkbGxslgkMNsXbtWvR6PXXq1OHy5cuMHTuWdu3alUvxTktLIyIigg0bNlSDpAoKCnfD0KFDqyS9fE27YNQ0arVajqyhoPCgUa3Kd01x9uxZnJ2dCQ4ORqfTlVjHZDKRlJTEzZs3yc3NLTUFdk2jVqvRarXFonEoVC9paWm8+eabXLt2DRcXF7p06VJu30BbW1uuXbtWxRIqKCjc6/Tr149Vq1Yxfvx4sw3BgYGBrFq1qsSIQgoKCvc/D4Ty3bBhw9sqqyqVCmdnZ5ydncvcoVxAObx1qoQCORVqlmeeeYZnnnmmpsVQUFCoJlJTU6vEJadfv3706dNHccFQUHiAeCCU74paiRWrsoKCgoIC5EfI8PDwIDk5meTk5BKjT9wtiguGgsKDxQOhfJeE0WgkOjqatLQ0APR6PZ6enveN4i2EwGQyKdYRBQUFhSpEq9XKoeL+/vtvs0RiioVaQUHhTnhgle+oqChUKhXe3t4IIYiPjycyMpK6devWtGi3JTs7m8TERDQaDS4uLjUtjoKCgsJ/Gj8/PxISEujRo4dZEpKAgADmzJmj+GYrKChUiCrPcHmvcPPmTTM/7czMTAICArC3t8fBwQEfHx+zzJf3MgUhqW6XvUpBQUFB4e4psHy7ubmxf/9+0tLS2L9/PyEhIQwYMIA1a9bUsIQKCgr3Ew+M8p2dnc358+fJzMwE8rPlXbp0ibi4OG7evElUVNR9E9/W0tISNzc33NzcaloUBQUFhf80RqORc+fOAdCzZ09at26NXq+ndevWrFu3jl69ehEWFqYYQxQUFMrNA6N8+/v74+vrS1RUFNeuXcPb2xt7e3tSU1NJS0vD0dGRwMDAmhazXEiShIWFhRLjW0FBQaGK2bNnj7w3qGiIUJVKxaRJk4iMjGTPnj01IZ6CgsJ9yAOjfEP+psrg4GDUajXnz59Hq9USFBREUFAQHh4eZhnG7hcKNl7eLZ06dWLcuHF3L1AVIUkS69atq2kxFBQUHjBiYmLk/1+5cqVYeaNGjYrVU1CoDkaMGFGhLNvh4eFIkkRycnKVyaRQPqpF27yRnMXpGymlHjeSs6qk3xEjRiBJEpIkYWlpSa1atZgwYQL29vYEBQVx8+ZNIiIiyhXX+3bExMRw5MgRrl69Kp8TQhAdHc1ff/3F0aNHuXDhAllZlTfWrKws4uLiSE9Pr7Q2S8NoNDJjxgzq16+PTqfDycmJ1q1bs2jRokrtZ8qUKTRt2rRS26wIVfkQ8u2339KpUyfs7OxKnQCPHTtG165dcXBwwNnZmRdffNHs+128eLH8my56FN4I9vPPP9O0aVOsra3x9/dn9uzZVTImBYX/Op6engC0bduWzp07Fys/ffq0WT2Fe5Pdu3fTu3dvvLy8bmvM6dSpE19//XX1CafwwFHl0U5uJGfRZe4usvJK94fTWarZ/kZHvB1Kzj55N/To0YNFixaRmprKr7/+ynvvvcf169dZvHgx9erVIyEhgfPnz+Pu7o6jo+MdhRrMyMggPj6+WPbM2NhYYmNjCQwMxMrKiujoaC5evEijRo0qLTyV0WgkKysLW1vbKnVDmTJlCt9++y1ffPEFLVq0IDU1lSNHjpCUlFRlff7XyMzMpEePHvTo0YNJkyYVK4+OjqZLly4MGjSIL774gtTUVMaNG8eIESNYtWoVAIMGDaJHjx5m140YMYLs7Gx5D8CmTZsYOnQo8+fPp1u3bpw7d44XXngBnU7H6NGjq36gCgr/ITp06EBAQADOzs589NFHZmUmk4kZM2YQGBhIhw4dakhChfKQkZFBkyZNePbZZ+nfv3+p9RITE9m3bx8//vhjNUqn8MAhykFWVpY4e/asyMrKKk91M05dTxb+b20Qa49dF6euJxc71h67Lvzf2iBOXU+ucNu3Y/jw4aJPnz5CCCHOnDkjrl69KkaMGCHc3NzE+fPnxfvvvy+aNGkivv32W+Hr6yskSRKLFy8WTk5OIjs726ytfv36iWHDhgkhhHzdDz/8IPz9/YVerxf9+vUTR44cEVeuXBFCCNGxY0cxePBg8corrwhHR0fh7u4uJk+eLI4dOybi4uKEEEIkJyeLkSNHCldXV2Frays6d+4sTpw4YdavwWAQhw8fFgaDQQghxJtvvinq1KkjdDqdCAwMFBMnTjSTtahsdnZ2YtCgQSI1NVWuk56eLoYNGyZsbGyEh4eH+OSTT0THjh3F2LFjS72XTZo0EVOmTCnzfmdnZ4sxY8YIV1dXodVqRbt27cShQ4fk8kWLFgl7e3uza9auXSsKfoaLFi0SgNmxaNEiIYQQgFiwYIHo27ev0Ol0IigoSPz6669mbf36668iKChIWFlZiU6dOonFixcLQCQlJQkhhEhISBBPP/208Pb2FjqdTjRq1EgsX75cvn748OHF+o+MjBRC5P9+evbsKWxsbISbm5v43//+J+Lj48u8H6Wxc+dOM7kK+Oabb4Sbm5swGo3yuePHjwtAXLp0qcS24uLihKWlpfjhhx/kc4MHDxYDBgwwqzdv3jzh4+MjTCbTHcmscP9T1jyekpIiAJGSklIDklU9dzu+1atXC0mSRO/evcW+fftEamqq2Ldvn+jdu7eQJEmsXr26kiW+d7kbfeBeARBr164tseyHH34QLVq0EEII8cEHHwhPT0+RkJAgl/fu3Vt06NDBbJ4uTIHeMW3aNOHm5ibs7e3FlClTRF5enggLCxOOjo7C29tbLFy40Oy6kydPis6dOwsrKyvh5OQkRo4cKdLS0uRyg8EgXn/9dWFvby+cnJzEhAkTxDPPPCPrOEIIYTKZxMcffywCAwOFlZWVaNy4sfjll1/k8tLWHoXyU1nzaLU5OQe56WnkbV/sCHLTV0v/2dnZuLq6Ymtri9FoJCcnB4DLly+zevVqfv31V/bs2cPAgQMxGo2sX79evjYhIYENGzbw7LPPyuciIiJYt24dX3/9NYsWLWLfvn0sWLBALjeZTKxfvx4nJycOHjzIrFmz+Oijj/jrr79IT09HCMHjjz9ObGwsv//+O0ePHqVZs2Y8+uijxMfHYzQa5aMwtra2LF68mLNnz/LZZ5+xePFiPvvsM7M6BbJt2LCBDRs2sGvXLmbOnCmXT5gwgZ07d7J27Vq2bt1KeHg4R48eLfP+eXh48McffxAfH19qnTfffJPVq1ezZMkSjh07RlBQEN27dycxMbHMtgsYNGgQ48ePp2HDhsTExBATE8OgQYPk8g8++ICBAwdy8uRJHnvsMYYOHSq3HRUVxYABA+jbty8nTpzgpZde4p133jFrPzs7m+bNm7NhwwZOnz7Niy++yLBhwzh48CAAn332GW3atGHkyJFy/76+vsTExNCxY0eaNm3KkSNH2Lx5Mzdv3mTgwIHlGld5ycnJQaPRmO09KHib8ueff5Z4zQ8//IC1tTUDBgwwa8fKysqsnk6n4/r16yX6rCooKJRNv379WLVqFX/99Rdt27bFzs6Otm3bcvr0aVatWqXE+SbfslzakZ2dXe66RV0zS6tXVaxfv54+ffoA8M477xAQEMALL7wAwNdff83u3btZunRpmXvE/vjjD6Kjo9m9ezdz585lypQp9OrVC0dHRw4ePMjLL7/Myy+/LG/gLXgr6ujoyOHDh/nll1/Yvn272ZvKOXPm8P3337Nw4UL+/PNPEhMTWbt2rVm/7777LosWLeKrr77izJkzvP766/zvf/9j165dlX2bFO6Wu9X0b0eB5bs0y/btyu+Gwpbvixcvip9//lk4OTmJxx57TERERIj3339fWFpaypboAl555RXRs2dP+fOnn34qatWqJVsN33//fWFtbS2ioqLE6dOnhdFoFBMmTBBNmjSRLd/t27cXTZs2FTk5OXI7LVu2FC+//LK4cOGC2LFjh7CzsytmYQ8ICBCTJk0Shw8fNjsKLN9FmTVrlmjevLkQIv+pt0C2wpbuCRMmiIceekgIIURaWprQaDRi5cqVcvmtW7eETqcr0/J95swZERwcLFQqlQgJCREvvfSS+P333+Xy9PR0YWlpKX788Uf5XG5urvDy8hKzZs0SQtze8l1wb5s0aVKsf0C8++67Zv1JkiQ2bdokhBBi4sSJolGjRmbXvPPOO7d9yn/sscfE+PHj5c8lvQF47733RLdu3czOXbt2TQDiwoULpbZdGqVZH06fPi0sLCzErFmzRE5OjkhMTBT9+vUTgJg+fXqJbTVo0EC88sorZue++eYbYW1tLbZv3y6MRqO4cOGCqF+/vgDEvn37Kiyvwn8DxfJ9d+Pr06ePAMRbb70lli9fLnbu3FnqvPxfprTfEUXeGhY+HnvsMbO61tbWpdbt2LGjWV0XF5cS690NlGL5zs7OFra2tuLkyZPyuYiICGFraysmTpworK2txbJly8pse/jw4cLf39/MMl6vXj3RoUMH+bPBYBA2NjZixYoVQgghvv32W+Ho6CjS09PlOhs3bhQqlUrExsYKIYTw9PQUM2fOlMvz8vKEj4+PrOOkp6cLKyurYnP8888/LwYPHiyEUCzflUFlzaP/+QyXGzZsQK/XYzAYyMvL49FHH2XatGn4+/sD+SEIXV1dza4ZOXIkLVu25MaNG3h7e7No0SJ582YB/v7+JCUlUbduXVQqFZ6enty6dcusnaCgILPPhescPXqU9PR0nJ2dzepkZWWRl5dHaGgokO/TffLkSbl81apVfPrpp1y+fJn09HQMBgN2dnbcunVLTiIUEBCAra2tWb8Fm/EiIiLIzc2lTZs2crmTkxP16tUr8z42aNCA06dPc/ToUf78809588qIESP47rvv5I2r7dq1k6+xtLSkVatWcozcu6Vx48by/21sbLC1tZXHdeHCBVq2bGlWv1WrVmafjUYjM2fO5KeffuLGjRvk5OSQk5ODjY1Nmf0ePXqUnTt3otcXf0sTERFRaVlRGzZsyJIlS3jjjTeYNGkSarWa1157DXd39xL3COzfv5+zZ8/yww8/mJ0fOXIkERER9OrVi7y8POzs7Bg7dixTpkxRUmErKNwhBXO1lZUVgwcPrmFpFKqCP/74A2dnZ0JCQuRztWrV4pNPPuGll15i0KBBDB069LbtNGzY0Mwy7u7uLkfFAVCr1Tg7O8vr17lz52jSpInZWtSuXTtMJhMXLlzAysqKmJgYs3XbwsKCFi1ayOv+2bNnyc7OpmvXrmay5ObmyvqEwr3Df1757ty5M1999RWWlpZ4eXkV21BZkuIVGhpKkyZN+OGHH+jevTunTp3it99+M6ujVqsxGAycPXsWyI//ajQaiYuLIy4uTo7FbTAY0Gg0QH64PIPBgKWlJSaTCU9PT8LDw4v17+DgUKKSdODAAZ5++mk++OADunfvjr29PStXrmTOnDmyG43JZCo2RkmS5HCEolCWz4qiUqlo2bIlLVu25PXXX2fZsmUMGzaMd955R2636KZPIYR8TqVSFeu/IpFmbjeukvouzJw5c5g3bx6ffvopISEh2NjYMG7cOHJzc8vs12Qy0bt3bz7++ONiZZUd4WDIkCEMGTKEmzdvYmNjgyRJzJ07t8QY9N999x1NmzalefPmZuclSeLjjz9m+vTpxMbG4urqyo4dO4D8BzMFBYWKU2BMiYiIqGFJ7k3KirpVdD0rHJmpKEXdOaKiou5KropQ2OWkMLt370atVhMVFYXBYJAznpZGSWtVRdevwvXKQ0FbGzduxNvb26xMq9WWqw2F6uM/r3zb2Njg6+tboR9fbm4uL7zwAvPmzePGjRt06dIFX19fszpqtZqGDRvKnz09PZEkCScnJzw9PVGpVKhUKlJTU7G2tgby/8AMBgN6vZ5mzZoRGxuLhYVFuRWivXv34u/vb+bLXODD6+DgUMxfuCSCgoKwtLTkwIED+Pn5AZCUlMTFixfp2LFjueQooEGDBkC+T15QUBAajYY///yTIUOGAPmK9ZEjR+TQfa6urqSlpZGRkSE/9Jw4ccKsTY1Gc0eZ4urXr8/vv/9udu7IkSNmn/fs2UOfPn343//+B+RPVpcuXSI4OLjM/ps1a8bq1asJCAi47aRbWbi7uwPw/fffY2VlVcyakZ6ezs8//8yMGTNKbUOtVsuT8IoVK2jTpo2SFVVB4Q6pU6cOAJcuXaphSe5NbvcGsTrq3g1CCH777bdibxJ/+ukn1qxZQ3h4OIMGDeKjjz7igw8+qNS+GzRowJIlS8zWxr1796JSqahbty729vZ4enpy4MABHn74YQAMBoO8V6ygDa1Wy9WrVyu8litUP9WmfF+OK/mpuLTzlcm5c+dwcHDA1dW11D9kg8FAUlIScXFxuLi4MHToUMLCwliwYEGxP8YCCocWLHiqtbCwkM9bW1sTExODVqvFysqKzMxMOUZ2ly5daNOmDX379uXjjz+mXr16REdH8/vvv9O3b19atGhRrL+goCCuXr3KypUradmyJRs3bpQ3XBQo+LdDr9fz/PPPM2HCBJydnXF3d+edd965rdI+YMAA2rVrR9u2bfHw8CAyMpJJkyZRt25d6tevj4WFBa+88goTJkzAyckJPz8/Zs2aRWZmJs8//zwADz30ENbW1rz99tuMGTOGQ4cOsXjxYrN+AgICiIyM5MSJE/j4+GBra1uuB6eXXnqJuXPnMnHiRJ5//nlOnDght11gOQgKCmL16tXs27cPR0dH5s6dS2xsrJnyHRAQwMGDB4mKikKv1+Pk5MSoUaNYsGABgwcPZsKECbi4uHD58mVWrlzJggULyu3KURB68vLlywCcOnUKW1tb/Pz8cHJyAuCLL76gbdu26PV6tm3bxoQJE5g5cyYODg5mbf30008YDIYSX4EmJCSwatUqOnXqRHZ2NosWLeKXX35RNt0oKNwFBZbvgr9fhfuL9PR0s++uYJ0pWK+OHj1KRkaGrNwCXL9+nVdeeYWPP/6Y9u3bs3jxYh5//HF69uxJ69atK022oUOH8v777zN8+HCmTJlCfHw8Y8aMYdiwYbIhZuzYscycOZM6deoQHBzM3LlzzXJF2NraEhYWxuuvv47JZKJ9+/akpqayb98+9Ho9w4cPrzR5Fe6eKo924mijQWepZtzPJ+j1xZ/FjnE/n0BnqcbRRlNlMhTE1b548SInTpzg0qVLREVFkZKSQm5uLmfPnuWvv/7i1q1b+Pj44O7ujp2dHf3790ev11cog1RhrK2tcXd35+rVq5w9exYhBPb29qjVaiRJ4vfff+fhhx/mueeeo27dujz99NNERUXJf2xF6dOnD6+//jqjR4+madOm7Nu3j/fee6/Ccs2ePZuHH36YJ554gi5dutC+fftirgtF6d69O7/99hu9e/embt26DB8+nPr167N161bZGjxz5kz69+/PsGHDaNasGZcvX2bLli04OjoC+b7ly5Yt4/fffyckJIQVK1YwZcoUs3769+9Pjx496Ny5M66urqxYsaJcYwoMDGTVqlWsWbOGxo0b89VXX8lvCAqU9/fee49mzZrRvXt3OnXqhIeHR7HvNiwsDLVaTYMGDXB1deXq1at4eXmxd+9ejEYj3bt3p1GjRowdOxZ7e3v5oaUg+U1ZfP3114SGhjJy5EgAHn74YUJDQ80i6xw6dIiuXbsSEhLCt99+yzfffMNrr71WrK2FCxfSr18/+d4WZcmSJbRo0YJ27dpx5swZwsPDi/nAKygolJ8C5TshIUHJEHgfcuTIEUJDQ2X/5zfeeIPQ0FAmT54MwK+//srjjz8ur2dCCEaMGEGrVq3kqCNdu3Zl9OjR/O9//6vU5HbW1tZs2bKFxMREWrZsyYABA3j00Uf54osv5Drjx4/nmWeeYcSIEbRp0wZbW1uefPJJs3Y++ugjJk+ezIwZMwgODpbX7ZLcFhVqFkmUwwk4OzubyMhIOVlMRbmRnEVSRul+tY42mipJsFMUk8lESkoK6enp5OTkYDKZsLCwwNraGnt7+2JJcrp27UpwcDCff/55lctWGkajkePHjxMaGnpbC6sQQnbrcHV1rTYXiXuVadOm8fXXX8vhnKqSKVOmEB4eXqIPv4LCvUBZ83hqair29vakpKRgZ2dXQxJWHZU1Pk9PT2JjYzl8+HCJbycfBO5WH7hXady4Me+++26lh5BV+G9RWfNotWhn3g66alGub4dKpcLR0bFUa2EBiYmJbN26lT/++MPsyfN+IC8vDyGEnPXyQeLLL7+kZcuWODs7s3fvXmbPnl1tGR23bNlSLN66gsL9TF5eHrGxsWRmZuLq6iq7Rj3IDB48mOzs7BIjHyncv+Tm5tK/f3969uxZ06IoPCA82KbRUmjWrBlJSUmyL/b9giRJ2NvbYzAY/lMWifJy6dIlpk6dSmJiIn5+fowfP77ENO5Vwf79+6ulHwWFqiQ9PZ0VK1awYsUKDh06JEdRAvDx8aFbt268+OKLxcJ6PijMnTu3WvoxGo3s2bOHmJgYPD096dChgxImtArRaDS8//77NS2GwgOEonyXQHWGNqpsLCwsHlh3k3nz5jFv3ryaFkNB4b4lJCSEwMBAnnjiCd566y28vb3R6XQkJiZy+vRp9uzZQ9euXWndujXz58+XI4AoVB5r1qxh/PjxZutQQEAAc+bMUTJpKij8R6i29PIK1Y/JZCqW1ldBQUGhNDZs2MCRI0eYPHkyPXr0ICQkhKCgIFq1asVzzz3HokWLuHnzJk888cQDGz0nLS2NixcvVknba9asYcCAAYSEhLB//37S0tLYv38/ISEhDBgwgDVr1lRJvwoKCtXLg2kifQAwmUzEx8djNBpxcXGRE/0oKCgolEbh3AWlodVqefXVV6tBmnuPkydP0qRJE1xdXctMFHMnGI1Gxo8fT69evVi3bp0cSal169asW7eOvn37EhYWRp8+fRQXFAWF+xzF8v0fRaVSyUl37iarZQEiNxdTZmaph7hNlkgFBQWF+51atWoBEB8fT2JiYqW2vWfPHqKionj77beL5V1QqVRMmjSJyMhI9uzZU6n9KigoVD8PpOU7OzubtLS0ElObe3l51YBEVYO9vT1QPF1vRREmE3mXI6CsVPCWlljWr4d0l30pKCjUPIcPH+att94iPj6eoKAgmjZtKh8FmXEfRPR6PX5+fly9epUzZ87QoUOHSms7JiYGyM9LURIF5wvqKSgo3L88cJpSfHw8p0+fJjo6mqSkJJKTk82O/xIFKe7vGklCslCDpSVqP99iB5aW+eW3STKjoKBwfzBs2DDUajUvv/wytWrVYteuXTz77LMEBATg7Oxc0+LVKAWuOWfOnKnUdj09PQE4ffp0ieUF5wvqKSgo3L88cJbvmJgYvL29H7gJLCcnh4yMDBwdHW+bibEwIi8TcpKQLAQiKw/j1ZIT1ghhAiEUBVxB4T/AtWvX2LhxI7Vr1zY7f+XKFU6cOFEzQt0jNGzYkE2bNlW68t2hQwcCAgKYPn26mc835O/hmTFjBoGBgZVqbVdQUKgZHjjLt8FguKeTRYSHhyNJUqVa4U0mE4mJiWRnZ5ORkVHu64TJCNlJIEyonXVgoUKyssKiTpB8SFZWoJKQNBqQJKZMmULTpk3lNkaMGGGWwr1Tp06MGzfujscSFRWFJEnVogAUHUtNUhW/CwWF0mjXrl2JmWH9/f3p06dPDUh071Dg/lHZyrdarWbOnDls2LCBvn37mkU76du3Lxs2bOCTTz65dzdbpqRATEz1HSkpNT3iu0KSJNatW1fTYtQYixcvxsHBoabFqDFq3PIthMCEEbVUNaLExsYybdo0Nm7cyI0bN3B2dqZRo0ZMnDiRRx99tEr6vBvatm1LTEyM7K9dGahUKhwcHMjJycHa2rpc1wghICcJJEDnTudOHWlSvz6zR78ORiMqW1tMaWks+eknwmbPJjH6BpIkERYWxpgxYypNdsifpNauXUvfvn3x9fUlJiYGFxeXSu2jJCpjLJ06daJp06Z8+umnlSNUBThz5gyTJ0/m6NGjXLlyhXnz5hV78ElLS+O9995j7dq1xMXFERoaymeffWaWRKW0NyWzZs1iwoQJAERERBAWFsaff/5JTk4OPXr0YP78+bi7u1fZ+BQqn2nTptGqVStefvllPvzwQ0JCQh54N5OiVJXbCUC/fv1YtWoV48ePp23btvL5wMBAVq1ade/G+U5JgY8+goSE6uvTxQXeew/KuVbW5FxcEjExMXK27aioKAIDAzl+/PgdG3wqo40HgZ07d/Lhhx/y119/kZ2djbe3N23btmXhwoVYWFgQHh5O586dSUpKqtKHgxpTvoUQxGdHcTF5L1mGdNp7DkFnYVepfURFRdGuXTscHByYNWsWjRs3Jjo6mt9++40XX3yRvXv3FlMsalpZ0Gg0eHh4VHq7Op0OnU5X/gsMmfmHlQuSWgMqC7BUobLVYoy9iaTXY4y9CSoJJJD+Sbes1+urNPWyWq2+6/uTl5eHpaXlbetV9ViqmszMTGrVqsVTTz3F66+/XmKdF154gdOnT7N06VK8vLxYtmwZXbp04ezZs3h7ewPFN3ht2rSJ559/nv79+wOQkZFBt27daNKkCX/88QcA7733Hr179+bAgQOVs+9AoVo4ePAg33//Pbdu3QKgXr169OnThzZt2hAaGkpISMgDH7a0bt26PPbYYzg5ObFjxw46depUqdbofv360adPn/srw2VmZr7irdNBOQ08ldJfZma5le/yIITAaDRWS6K6qljnHzTCw8MZMWJEuRMjnjlzhp49e/Laa68xf/58dDodly5dYtWqVZhMpqoVtgjVvioKIYjLimRv7I8cvLmKHGMW2cY0coxZld7Xq6++iiRJHDp0iAEDBlC3bl2cnJwYPnw4S5YsIS4ujmPHjuHh4cGuXbvkuK3JyclIkkR4eDjw7yv/LVu2EBoaik6n45FHHiEuLo5NmzYRHByMnZ0dgwcPJjMzU+6/U6dOjBkzhnHjxuHo6Ii7uzvffvstGRkZPPvss9ja2lK7dm02bdokX1PUvWDJkiV07tyZLVu2EBwcjF6vp0ePHmYKkcFg4LXXXsPBwQFnZ2cmTpzI8OHDzdw9Vq1aRUhICDqdDmdnZx555BHS0tJKvG/57ibJYGGNZPnPRCqpARUqJytEThbGmBhEZiaYBKhU8kNMVbtqFHY7MZlM+Pj48PXXX5vVOXbsGJIk8ffff+eLLkl8/fXX9OnTBxsbG6ZOnYrRaOT5558nMDAQnU5HvXr1+Oyzz8zaKc9Yzp49y2OPPYZer8fd3Z1hw4aR8I/1Z8SIEezatYvPPvsMSZKQJKnUSWLZsmW0aNECW1tbPDw8GDJkyF3HEW7ZsiWzZ8/m6aefRqvVFivPyspi9erVzJo1i4cffpigoCCmTJlCYGAgX331lVzPw8PD7Pj111/p3LmzHHZt7969REVFsXjxYkJCQggJCWHRokUcPnxYVsYV7g/Wr19PfHw8V69eZf369YwbN46UlBRmzpxJq1at0Ov1NG7cuKbFrDHWrFlDkyZN+P333+UH1aCgoEpPfqNWq+nUqRODBw+udOW+SrG2Blvbqj8qqOCXNhcXXttbtGiBVqtlz549RERE0KdPH9zd3dHr9bRs2ZLt27ebtVngn//cc89ha2uLn58f3377rVyem5vL6NGj8fT0xMrKioCAAGbMmCGXF3Y7CQwMBCA0NBRJkujUqVOJ40hKSmLo0KG4urqi0+moU6cOixYtKrMNk8nEhx9+iI+PD1qtlqZNm7J582a5zYI19eeff6ZDhw7odDpatmzJxYsXOXz4MC1atJD1jvj4ePm6w4cP07VrV1xcXLC3t6djx44cO3bMTN7k5GRefPFF3N3dsbKyolGjRmzYsMGsTlm6TWWzbds2PD09mTVrFo0aNaJ27dr06NGD7777rtqNCtWmfBdVugEech9Ac9feVdJfYmIimzdvZtSoUdjY2MjnGzduTOPGjWnfvj2NGzcmODgYyLdmhISElNnmlClT+OKLL9i3bx/Xrl1j4MCBfPrppyxfvpyNGzeybds25s+fb3bNkiVLcHFx4dChQ4wZM4ZXXnmFp556irZt23Ls2DG6d+/OsGHDzJT2omRnZzN37lyWLl3K7t27uXr1KmFhYXL5xx9/zI8//siiRYvYu3cvqampZr5kMTExDB48mOeee45z586xceNGunTpQlJSUrEY4GbuJlpHc0EsrZEkCbWLNab4hHylW1tzVjCVSsXTTz/Njz/+aHZ++fLltGnTRlYOAd5//3369OnDqVOneO6552TF/eeff+bs2bNMnjyZt99+m59//rnc/cfExNCxY0eaNm3KkSNH2Lx5Mzdv3mTgwIEAfPbZZ7Rp04aRI0cSExNDTEwMvr6+JbaVm5vLRx99xF9//cW6deuIjIxkxIgRFb8pFcBgMGA0GrGysjI7r9Pp+PPPP0u85ubNm2zcuJHnn39ePpeTk4MkSWYKvpWVFSqVqtR2FO5tfHx86NWrF++++y6rVq3i8uXLpKSksGPHDl566aWaFq9GULJP3r/cbi5+8803mTFjBufOnaNx48akp6fz2GOPsX37do4fP0737t3p3bs3V69eNWt3zpw5tGjRguPHj/Pqq6/yyiuvcP78eQA+//xz1q9fz88//8yFCxdYtmwZAQEBJcp36NAhALZv305MTEypv6X33nuPs2fPsmnTJs6dO8dXX30lu2CW1sZnn33GnDlz+OSTTzh58iTdu3fniSee4NKlS2Ztv//++7z77rscO3YMCwsLBg8ezJtvvslnn30mP5BMnjxZrp+Wlsbw4cPZs2cPBw4coE6dOjz22GOyUc9kMtGzZ0/27dvHsmXLOHv2LDNnzjR7kMzMzOSTTz4pVbepbDw8PIiJiWH37t1V1kd5qRa3k/isKC4k/0lSTgyOWk8ech+Aq1VAvoU352aV9Hn58mWEENSvX7/UOkKICiWgmTp1Ku3atQPg+eefZ9KkSURERMhK3oABA9i5cycTJ06Ur2nSpAnvvvsuAJMmTWLmzJm4uLgwcuRIACZPnsxXX33FyZMnad26dYn9GgwGvvzyS+rWrQvA6NGj+fDDD+Xy+fPnM2nSJJ588kkAvvjiC37//Xe5PCYmBoPBQL9+/fD398fLyws/Pz9sbW2Ld2bKy3c30dgjqcytLV999RULF35XcPNAgMFkKqa8VSdDhw5l7ty5XLlyBX9/f0wmEytXruTtt982qzdkyBCee+45s3MffPCB/P/AwED27dvHzz//LCvPt+Orr76iWbNmTJ8+XT73/fff4+vry8WLF6lbty4ajQZra+vbvmIsLFutWrX4/PPPadWqFenp6VXm+mJra0ubNm346KOPCA4Oxt3dnRUrVnDw4EHq1KlT4jVLlizB1tbWzPe0devW2NjYMHHiRKZPn44QgokTJ2IymZSYxP8h9Ho9HTp0kKNt3LhxQ3ZN+q9TNPukyWTi7NmzCCGU7JP3Afb29mXOxR9++CFdu3aVPzs7O9OkSRP589SpU1m7di3r169n9OjR8vnHHntMzvY6ceJE5s2bR3h4OPXr1+fq1avUqVOH9u3bI0kS/v7+pcrn6uoq91vWWnH16lVCQ0Np0aIFgJkyX1obn3zyCRMnTuTpp58G8o11O3fu5NNPP+X//u//5HphYWF0794dgLFjxzJ48GB27NhhpvMsXrxYrv/II4+YyfbNN9/g6OjIrl276NWrF9u3b+fQoUOcO3dO1l0KG8Qg3wX066+/lqMqFdVtKpunnnqKLVu20LFjRzw8PGjdujWPPvoozzzzDHZ2lev2fDuqxfJ9NjGcpJwYatk1p53HUNx0gRUKd3cnFCjVJfWTkJDAmTNnOHbsmBw7tTxRJAq/bnV3d8fa2trsx+Tu7l7MVaDwNWq1GmdnZzMLe4GPeVkuBlZWVmYhvzw9PeX6KSkp3Lx5k1atWpn107x5c/lzkyZNePTRRwkJCeGpp55iyZIlaDQabG1ti98flSVYWENeer77SSGGDhnC8T+3cGzHRg6t/JnD638zU2BrgtDQUOrXr8+KFSsAZPehogp0wWRVmK+//poWLVrg6uqKXq9nwYIFxSwbZXH06FF27twp+4br9Xr5YS8iIqJC4zh+/Dh9+vTB398fW1tb+ZVhReS5E5YuXYoQAm9vb7RaLZ9//jlDhgwpVYH4/vvvGTp0qNkDl6urK7/88gu//fYber0ee3t7UlJSaNasmaKI3GeMGTNGtqCVREpKCgsWLKBRo0YPlKW3aPbJAveTsWPHKtkn/wMUXR8yMjJ48803adCgAQ4ODuj1es6fP19sPi68vkuShIeHh7w2jxgxghMnTlCvXj1ee+01tm7detdyvvLKK6xcuZKmTZvy5ptvsm/fvjLrp6amEh0dLSvQBbRr145z586VOpYCvaSorlJYT4mLi+Pll1+mbt262NvbY29vT3p6unyPTpw4gY+Pj6x4l4S1tXWpuk1pFF5ve/bsydWrV4udKw21Ws2iRYu4fv06s2bNwsvLi2nTptGwYcNqNxRVi+W7gVMnLiT/yd+pR0nKiaauQzvZ8l1V1KlTB0mSOHfunJnvc2xsLNHR0bi5ueHt7S37+URHR3Pz5k3c3d1LzHwJmG3SkySp2KY9SZKKOe2XVKdoO0CZzv5FN39IklTMYl/0XhYuV6vVbNu2jX379rF161bmz5/PO++8w8GDBwkMDEQIgcFgwNLSMr9trSNkxOa7n+j+jSpip9cSFBSIELYYHWNRe3rifr3kuN/VydChQ1m+fDlvvfUWy5cvp3v37sWioRR2PQL4+eefef3115kzZw5t2rTB1taW2bNnc/DgwXL3azKZ6N27Nx9//HGxsorEkS/YsNitWzeWLVuGq6srV69epXv37uTm5pa7nTuhdu3a7Nq1i4yMDFJTU/H09GTQoEGy/2Bh9uzZw4ULF/jpp5+KlXXr1o2IiAgSEhKwsLDAwcEBDw+PEttRuHexs7OjR48eWFpa0qJFC7y8vLCysiIpKYmzZ89y5swZWrRowezZs8tc5P5rFM0+2axZMwBOnjxJXl6ekn3yPqfo+jBhwgS2bNnCJ598QlBQEDqdjgEDBhSbj8vSAZo1a0ZkZCSbNm1i+/btDBw4kC5durBq1ao7lrNnz55cuXKFjRs3sn37dh599FFGjRrFJ598UuZ1JekHRc+VpJcUPVdYTxkxYgTx8fF8+umn+Pv7o9VqadOmjXyPyhPgoaT7dztvhMJhhg8ePMjEiRPl/Xnl7dfb25thw4YxbNgwpk6dSt26dfn666+r1ZhYLZZvV10A7TyG8pD7AAAO3lzF3tgficuKBMrv9lERnJyc6N69O//3f/9nFts6Li4OPz8/9Ho9Dg4OBAUFAflf+s2b+S4w91MSCXt7e9zd3c2sVUajkePHj5vVkySJdu3a8cEHH3D8+HE0Gg1r165FCEFSUhIJCQnyQ4ekUoOVAxgy85PsAAgTmAygdURla4dl3bqoSnJbqQGGDBnCqVOnOHr0KKtWrWLo0KG3vWbPnj20bduWV199ldDQUIKCgipsrW7WrBlnzpwhICCAoKAgs6NgMtdoNBiNxjLbOX/+PAkJCcycOZMOHTpQv379u95sWVFsbGzw9PQkKSmJLVu2lBjLeeHChTRv3tzsdWxRXFxccHBw4I8//iAuLo4nnniiKsVWqGSmTZtGdHQ0X331FXXr1iUhIUH2DR06dChHjx5l7969D5TiDcWzT9auXRt7e3tycnI4d+6ckn3yPqA8c3EBe/bsYcSIETz55JOEhITg4eFR7ogahbGzs2PQoEEsWLCAn376idWrV5OYmFiibEC55HN1dWXEiBEsW7aMTz/9VN7kWVIbdnZ2eHl5Fdt7s2/fPnm/252yZ88eXnvtNR577DEaNmyIVquVgw1AviX9+vXrXLx48a76KUrhddbb2xsLC4ti5yqCo6Mjnp6eFcqBUhlUW6hBSZJw0wXiahUghxg8eHMV1hYOVdbnl19+Sdu2bWnVqhUffvghjRs35uLFi2zbto3vvvuOc+fOodPpaN26Nf/3f//H2LFjSU5Oln207xfGjBnDjBkzCAoKon79+syfP5+kpCT56fXgwYPs2LGDbt264ebmxsGDB4mPjyc4ODg/zvo/T7NGo/HfJ1ELa7DIgpwkhMoi3xdcZfFv9JN7iMDAQNq2bcvzzz+PwWAoVxKQoKAgfvjhB7Zs2UJgYCBLly7l8OHDFbLUjho1igULFjB48GAmTJiAi4sLly9fZuXKlSxYsAC1Wk1AQAAHDx4kKioKvV6Pk5NTsdB7fn5+aDQa5s+fz8svv8zp06f56KOPKnwfipKbm8vZs2fl/9+4cYMTJ06g1+vlh84tW7YghKBevXpcvnyZCRMmUK9ePZ599lmztlJTU/nll1+YM2dOiX0tWrSI4OBgXF1d2b9/P2PHjuX111+nXr16dz0OherFysqKfv363bsxpWuAkrJPhoaGEh4ezuHDh/n111+V7JP3OCXNxaVREMGmd+/eSJLEe++9V+FQdPPmzcPT05OmTZuiUqn45Zdf8PDwKDF2tJubGzqdjs2bN+Pj44OVlVWJuT4mT55M8+bNadiwITk5OWzYsEFWoktrY8KECbz//vvUrl2bpk2bsmjRIk6cOFEsUEFFCQoKYunSpbRo0YLU1FQmTJhgZnXu2LEjDz/8MP3792fu3LkEBQVx/vx5JEmiR48ed9X3nfLNN99w4sQJnnzySWrXrk12djY//PADZ86cKRYso6qp9lCDBUp4gSVcq9ZhpbZFq658pS4wMJBjx47RuXNnxo8fT6NGjRg9ejRbt241C6X2/fffk5WVxTPPPMPYsWOZOnVqpctSlUycOJHBgwfzzDPP0KZNG/R6Pd27d5f9cu3s7Ni9ezePPfYYdevW5d1332XOnDn07NkTlUrFyZMn8fLyIjY2Vm5TkqT8aCcCyPxnU6y6YhsrRW4uwmBAGI2YMjMxZWaCyYTIy8OUmYnIzWXKlCml7gCHf91xbhd3dejQofz111/069evXK+dXn75Zfr168egQYN46KGHuHXrlrxxprx4eXmxd+9ejEYj3bt3p1GjRowdOxZ7e3tZwQ4LC0OtVtOgQQPZnaQorq6uLF68mF9++YUGDRowc+bM275GhPzvqPAGmKJER0cTGhpKaGgoMTExfPLJJ4SGhvLCCy/IdVJSUhg1ahT169fnmWeeoX379mzdurXY68CVK1cihGDw4MEl9nXhwgX69u1LcHAwH374Ie+88065xqCgcD9QUvbJAleTqVOn3vvZJ6uLzExIS6v6o4zoYKVRnrm4gHnz5uHo6Ejbtm3p3bs33bt3l12Nyoter+fjjz+mRYsWtGzZkqioKH7//fcS8x5YWFjw+eef88033+Dl5VWqAUmj0TBp0iQaN27Mww8/jFqtZuXKlWW28dprrzF+/HjGjx9PSEgImzdvZv369aVuqi8v33//PUlJSYSGhjJs2DBee+013NzczOqsXr2ali1bMnjwYBo0aMCbb75Z7rcPVUFBEIOXX36Zhg0b0rFjRw4cOMC6devo2LFjtcoiiXKE+8jOziYyMpLAwMBKj2xR1Rkui5KUlERERAR2dnZyFIn09HTS0tKoVauWnHHqXqHAhSQ0NLTcE7vJZCI4OJiBAweWy4K6ePFipk2bxtmzZ7G0tMRkMiGEQK1W57ud5CSD1qFCVm9hMpF3/gKU4j8PgKUlL348E8pQImNjY/H09JTjjVYnkyZNYs+ePfdkuLyoqCjq1KnD2bNn73oSVXgwKGseT01NlTfKVveu/+qgssa3Zs0axo8fb+aCoNVqWb58+QPzpqDE39F9kOFSQaEyqKx5tMbTy0uShLoaxXB0dCQ4OJibN2/KEU6srKwIDg4ud+r1e40rV66wdetWOnbsSE5ODl988QWRkZEMGTKkXNdv3ryZ6dOnY2lpidFo5NatW0iShJOTE2pLa7iN0l1g4S6KpFYjAAsfbyhiuTZcvYqktmDX7t0lxtwUQnDlyhU++eQT3N3dZStTdSCE4O+//2bHjh2EhoZWW78VYfPmzbz44ouK4q2gUI0Uzj555MgRJkyYgEqlKper238ae/t8RfgOLNJ3jLW1ongr3LfUuPJdE9jY2BSLN3k/o1KpWLx4MWFhYQghaNSoEdu3by/3hoqC11aQbzUvcPUoTwx0YTKRdzmiTAu34do1LIODkf553WZKS4OcXNS1vImMjCzxmpSUFOrVq0dwcDArV66s1ljiKSkpNGjQgJYtWxaLF36v8PLLL9e0CAoK5WL37t3Mnj2bo0ePEhMTw9q1a80iUAkh+OCDD/j2229JSkrioYce4v/+7/9o2LChXCcnJ4ewsDBWrFhBVlYWjz76KF9++SU+Pj7VPp6C7JMdOnQgKyuL1q1bVyhfxH8We3tFGVZQKCcPhPJtNBpll43b+Rvdjz57vr6+7N27t1LasrS0xNnZGbi9nzUAkoRkaYlQSVj4+RUrNkRE5Keg/2fzpxACY+xNJGtrpDKSxzg4OJCTk3Nng7hLarJvBYX/GhkZGTRp0oRnn32W/v37FyufNWsWc+fOZfHixdStW5epU6fStWtXLly4ICcCGzduHL/99hsrV67E2dlZTnhz9OjRGpuz1Wo17733Xo30raCgcH/zQCjfx48fp0mTJlhaWhYLwVeU6vYrLi85OTnV5hZTdLNdQdzOglBGhZEkCbWHO4a/I8FoNAs/aEpLy1e8EYj0dCRbW0R6OiIzE4taVZ9oSUFB4c7Zs2cP33zzDREREaxatQpvb2+WLl1KYGAg7du3L3c7PXv2LDU0oRCCTz/9lHfeeUf2mV6yZAnu7u4sX76cl156iZSUFBYuXMjSpUvp0qULAMuWLcPX15ft27fLWfkUFBQU7heqPdpJTVCvXj3ZiluvXr0yj3uVggQXN27cIDMzs9pecxoMBhITE7l161apCV8kvR7J2hpj7E1Zrn8t3DqzsvJYvcuDyMtEpEf/G4dcQUGh0li9ejXdu3dHp9Nx/Phx+U1QWloa06dPr7R+IiMjiY2NpVu3bvI5rVZLx44d5ex9R48eJS8vz6yOl5cXjRo1KjXDX05ODqmpqWZHVZCVlcWvv/7KjBkzqqT9exnF1UbhQaSyfvcPhPJdOI26RqNBr9dja2trduj1+hItu/cSWVlZxMTEcPbsWU6fPs21a9dIS0ur0klQpVJhYWEhHyVRYP0WmZmI9HQA2cKt9vCQy4wxMf+cc78rq7cwGSE7CYQxPw656V9XIkUpV1C4e6ZOncrXX3/NggULzN6EtW3blmPHjlVaPwXhTQvSWRfg7u4ul8XGxqLRaIpFoipcpygzZsyQU17b29vj6+tbaTIXJicnh759+/L2229XKDGW0WgkPDycFStWEB4eXqPh1ypKwe8hszo3Vyoo3CMUGCHv1t3tgXA7KcypU6dkF5TCGI1GTp06VSG3k7i4OOLj42WrkE6nw8vLSw6OL4QgJiaG+Ph4DAYDer0ePz+/csWhLkpISAhpaWkkJyeTkpJCTk4ON2/e5ObNm3I6b0dHR2xtbUuMI3qnqFQqnJ2dEUKYtVs0PW1h67ek1xezcEvW1pjiE+7a6i2EyE97LwE6d8iKz/+sc/lXKceUr5SrtfnZOhUUFCrEhQsXePjhh4udt7Ozk6NEVSblSX9dlLLqTJo0iTfeeEP+nJqaWiUKuIODAw0bNuTMmTPs27fPbCNpUYxGI3v27OHXX39lxYoVckZlyE8AM2fOnPsiXKFarcbBwUF+2LC2tlZcCBUeCEwmE/Hx8VhbW5dvT1wZPHDKd2kYjcYKK60ajQYfHx+0Wi0At27d4vLlyzRo0ACdTkdsbCyxsbFyPMjo6GguXrxIo0aNKvzUZGFhgYuLCy4uLhiNRlJTU0lKSiIlJQWDwUBCQgIJCQmoVCrZ0lNZlnxJkswm18zMTLKzs3FwcJDvWWHf7wILd4FftxAC4eYGsbH5/94Nhsz8w8oFSa1BaB0hOwGRlwGGrBKV8pK40/jlCgoPAp6enly+fLlYAqw///yzUiNFeXh4AP/G8y8gLi5OtoZ7eHiQm5tLUlKSmfU7Li6Otm3bltiuVquV5+Wqpm3btpw5c4a9e/eWqnyXFB/c09OT2bNnU7t2baZPn86AAQNYtWrVfaGAF3xvFbH2Kyj8F1CpVPj5+d31A+cDo3xfu3ZN/n90dHQxK25GRkaFNzQWTRPr7e1NXFwc6enpWFlZERcXh6enp7xgBAYG8tdff5GYmIirq+sdj0WtVuPo6IijoyMmk4n09HSSkpJITk4mLy+PlJQUszTpGRkZaDSaYtb+O8FkMpGSkoIQgqysLGxsbOQyyUqFhb8DxoQU2cKdazCRl5OOlSqNTB8PciUr1Nl5WFtaoLGo2MNOvmU7GSys/1WYLXRgYf2vxbuYUp5ZTLlWLOQKCmXz0ksvMXbsWL7//nskSSI6Opr9+/cTFhbG5MmTK62fwMBAPDw82LZtmxxTPzc3l127dvHxxx8D0Lx5cywtLdm2bRsDBw4EICYmhtOnTzNr1qxKk+VOad++PQsWLCgxXwHkK94DBgzg8ccfJzs7m9DQUN59911mzpzJsGHDWLVqFevWraNv376EhYXRp0+fez7qliRJeHp64ubmRl5ZidQUFP5jaDSaSvEueGCU78L+aVlZWeYuE5KEtbV1Mb/DiiCEICkpCZPJhF6vJzc3l7y8PNkFBfKfmGxtbUlPTy9V+f7jjz949NFHSUhIwMHB4ba+gCqVCjs7O+zs7PDz8yMjI4Ps7GyzH0dkZCTZ2dnUqVPHTJ47ocANJTMz0+xhpUAx/uDjefy6YSvH9u0nzygYMWI4GclxrFvxHXpTGp169aZhSBM+mvUJtnkCC1H6+CQLC6R/rPdm7ibaf61fkiQhNHb51nBJXVwpL6JcF7QT2Kg1Y8eOZdyLg0u0kHfq1ImmTZvy6aef3tX9UlC4H3nzzTdJSUmhc+fOZGdn8/DDD6PVagkLC2P06NEVais9PZ3Lly/LnyMjIzlx4gROTk74+fkxbtw4pk+fTp06dahTpw7Tp0/H2tpaThJmb2/P888/z/jx43F2dsbJyYmwsDBCQkLk6Cc1SefOnQE4cuQIKSkpZnOs0WiUwyKOGzeORx99lHfeeYfc3FwGDRrErVu3ZIV70qRJtG3blj179tCpU6caGk3FUKvV9/yDgoLCvUi1bLjMNebW2M7o2NhYxowZQ8+ePWncuDFPPPEEYWFhXL9+XY5wUrduXfz9/e8okUtmZibHjh3j6NGjXLlyhaCgIHQ6nWwNKOoXZGFhUaaloFatWmzatIm///6b48ePc/LkyXLLIkkSer0eF5d/FckCdxpJksys1Ldu3SI6OpqsrKxyfTedOnVi3LhxQP6Tn4ODA9I/aeEdHBzISrsFQNhb77F9w09IFrlk5hmwEHlISGDtDhJIwoClWkIjgfg7AsOly6UeeZcjECYTkiSxbu2qfAXbUm9mpRZCQG4qIIEwIoy58r1A6wiCfOW6gAK3FUmFpLLIr2PILLZBc82aNXz00UflvveVwdixY2nevDlarZamTZuWWOfnn3+madOmWFtb4+/vz+zZs83KR4wYIbsJFT4KJywBSE5OZtSoUXh6esoZXn///feqGprCfci0adNISEjg0KFDHDhwgPj4+Dv6mzhy5AihoaGyZfuNN94gNDRUtqC/+eabjBs3jldffZUWLVpw48YNtm7dKsf4Bpg3bx59+/Zl4MCBtGvXDmtra3777bd7QvHz9fWlTp06mEymYtbvPXv2EBUVxdtvvy37eA8dOpTOnTvzv//9j3379hEZGcm0adPkLL4xMTHVPgYFBYXqpcot37EZsTz/+/9ws3HnxSav0tqrbbVtzoiKiqJdu3Y4ODgwa9YsGjduTFZWFlu3bmXUqFGcP39ermswGPL9lis4mVtZWdGgQQOMRiNJSUlERkbeVchCPz8/s41BRqOxQgp4UdRqNQ0aNCAvL8/sQSAuLo6MjAyio6PRarU4Ojri4OCAjY1Nxb8fIUjOMJCt0eDk4oTe2gqyE9BKoMIIKsv8Q+sIwgTCgJXGAoOFBWqVCgv/EpLz/JN+viA5D5JFviU7Lx1RWAE35eUr00j57ijqf/3cJZUaodFDbkq+Ui6pZbeVfBM6pVrInZycKnYPKgEhBM899xwHDx4s8TvftGkTQ4cOZf78+XTr1o1z587xwgsvoNPpZGvkZ599xsyZM+VrDAYDTZo04amnnpLP5ebm0rVrV9zc3Fi1ahU+Pj5cu3bNTNlRUID8zXR3m/ugU6dOZT7gS5LElClTmDJlSql1rKysmD9/PvPnz78rWaqKRx99lEuXLnHw4EF69+4tny9QpBs1asTWrVuBfGV95cqVNGrUiIMHD9KlSxfef/992S2wsO+7goLCf5Mqt3wnZydyM/MmSdlJjNn+Ms9t+h/7b+ytFkv4q6++iiRJHDp0iAEDBlC3bl10Oh3Dhw/nwIEDQL6CLkkSu3fv5u+//86XOTkZSZIIDw8HIDw8HEmS2LJlC6Ghoeh0Oh555BHi4uLkc56enkyYMAHIV2wtLS1lv8lx48bh6OiIu7s7y5YtIy8vj2effRZbW1tq167Npk2bZJl3796NhYUFaWlpqNVqli1bRufOndmyZQvBwcHo9Xp69OhhZh0xGAy89tprODg44OzszMSJExk+fLjZ5p9ff/2VkJAQdDodzs7OvPzyy1haWiJJEjk5OcTGxnL+/HlOnjzJlStXSElJkdPMl4X4p44EWFnbMGXKFEJbtsWkssJKZCIkFQaMpOXFI9QakFRgzEGYjOQ6uUBuLsJgRGVtLR8Yjfnp5wuFJCxsyb7x91kGDRqEo6MjLm6e9H16JFFXroLGDsi3/vbt25fZs2fh5V8Xl4CmjBozjrz0uBLdVtA6smjpTzg4OrJt2zbA3NIP+dEIpk+fznPPPYetrS1+fn58++23cnlubi6jR4+WLckBAQEVjv37+eefM2rUqFI3tC1dupS+ffvy8ssvU6tWLR5//HEmTpzIxx9/LP892dvb4+HhIR9HjhwhKSmJZ599Vm7n+++/JzExkXXr1tGuXTv8/f1p3749TZo0qZC8Cv9tsrOzOXToEBs2bGD9+vVmh4I5YWFhXLp0qdibgQJF+q+//mLRokXodDocHBxo1aoVer1edt1r3bo1U6dOJSAggA4dOlS7/AoKCtVLtcX5ntFxNvO7fA1QLUp4YmIimzdvZtSoUWbuFhkZGdja2hbbLGltbU1GRkaZbU6ZMoUvvviCffv2ce3aNQYOHMinn37K8uXL2bhxI9u2bWPZsmWYTCY0Gg2SJLFs2TJcXFw4dOgQo0eP5v3332fs2LFyvNzu3bszbNiwMmOmZmdnM3fuXJYuXcru3bu5evUqYWFhcvnHH3/Mjz/+yKJFi9i7dy+pqamsW7dOLo+JiWHw4ME899xznDt3jvDwcAYNGkTt2rVp2rQptWrVwsnJCbVaTV5eHvHx8Vy6dIm//vqLv//+m6SkpBK/JyEEGDJAAlc3F7MQikaTCSFACBMgMAkTJgRCsgABIjsJk0aLUaMlLzqa3PQMTJmZmDIzMUbHIFlZIRWJViCp1GSaNDzSow821lbs3r2bPTu3obfR0bPfcHLTE+S6O3fuJOLiOf7Y+DOLFy1iyZIlLF7yQzG3FYA5c+cx4d2pbF7zA10e6Vjq9zBnzhxatGjB8ePHefXVV3nllVfktyeff/4569ev5+eff+bChQssW7asWKSIuyUnJ6eYa5ROp+P69etcuXKlxGsWLlxIly5d8Pf3l8+tX7+eNm3aMGrUKNzd3WnUqBHTp0+/r2INK1Qtmzdvxs/Pj9atW/PEE0/Qt29f+XjyySdrWrx7jtq1axMUFFTsrWGHDh0ICAjgzTffJCoqismTJ7Nx40b69u3L3r17+eijj/Dy8kKlUpGZmcmzzz57T7jSKCgoVC3VpnxLSLTxbsf3PZcVU8IPRu+v9P4uX76MEIL69eubnRdClKpI3s7SO3XqVNq1a0doaCjPP/88u3btYvbs2TRo0IDmzZvTs2dP9u7di7OzM5IkodFoCAoKYtSoUfj4+PD000+j1Wrx9PRk5MiR1KlTh8mTJ3Pr1q0yXUsMBgNffvklLVq0oFmzZowePZodO3bI5fPnz2fSpEk8+eST1K9fny+++MLs4SImJgaDwUC/fv0ICAggJCSEV199Fb1ej1qtxsnJiVq1atGkSRPq1KmDq6srlpaWGI1GEhMTiYiIID09nS+//BK9Xi8ftra2vPJavrXfwiLf3cMoDJiEgeQMI/GZahCglvLdXQxGEwYBJskCDdnY3LyGOjcHVU4ORPzr/y2ysxHZ2bLPd2FWrlqPSq3mu8+n0ahBPYJrufP9gi+5ej2a8J1/yL7bjo4OfDF7MvUbt6T3E314/PHH+WP3gXy3lUJJeSZNmsTcuXPZufFnWrdtb+a2UpTHHnuMV199laCgICZOnIiLi4v8duTq1avUqVOH9u3by5bkwYMHl/l7qijdu3dnzZo17NixA5PJxMWLF+UNoSX5icbExLBp0yZeeOEFs/N///03q1atwmg08vvvv/Puu+8yZ84cpk2bVqnyKty/jB49mqeeeoqYmBhMJpPZoTykmVNWwhy1Ws2cOXPYvz9/jWvZsiVLly7l8OHDtG/fnk2bNhEdHc2NGzcAqFOnTo2MQUFBoXqp9mgnkpSvhLf2asu8I7NZfnYpnx2Zw/InVlVqPwUKdlFLhI2NDQkJCfj5mfsZJyUl3TZ+bePGjeX/u7u7y5be06dPo1arsbOzIzMzEzu7fPcHjUZD3bp1uXr1KgaDARsbG1xcXIq1A2XHS7WysqJ27dryZ09PT7l+SkoKN2/epFWrVnK5Wq2mefPm8sNEkyZNePTRRwkJCaF79+5069aNAQMGFMsYVxAj3N7eXo6cUhDCUJIkhg4dyjvvvAPkb9gUQvDHto3MmvM5ecYsso2Z5BmzEQJMQgIEQlJh/Ed/zjaYUCFQCQO5WCFJOaDRIKlUGIUg190TXVx+xjphMiFZFPL5/odjx45xOSISO8/CfvUS2dnZRFyJyd9cKUw0rBeEWmsrRz/x9PTk1KmTZhsw58yZQ0ZGBof/3EotP08zd5SSKPy9SZKEh4eH/D2MGDGCrl27Uq9ePXr06EGvXr3M0mFXBiNHjiQiIoJevXqRl5eHnZ0dY8eOZcqUKSVaywo2wxaNPWwymXBzc+Pbb7+VfyvR0dHMnj27UsPIKdy/xMXF8cYbb9xVBKgHgZLid+v1epYsWSLH6+7Xrx9Tpkzh/fffN4vO4unpyaBBg+jTpw8WFhZ06NBB8fdWUHhAqPb08kII9t/Yy3Ob/sfys0sJcW3M2BbjK72fOnXqIEkS586dMzvv7e1NfHw858+fJzo6mvj4eCBf+fb29gYoNRpJ4TjZBZbtxo0b07x5c5o2bYqrq2sxZd/BwYEmTZrQvHlz6tevj0qlKtYOUKbVvWjElILENUXPFaZwuVqtZtu2bWzatIkGDRowf/586tWrR2RkZKl9FkRO8fX1pVGjRtjY2GBvb09QUBC1a9dGp9PlZ9Z0+mdxzk7EYMpFrbJErbLA1c4CRysTKkmiQBKj0Yj0T2hBycoRCw8PyM1F5WCPKicHVWoKIjsblYM95Jr7fBeMx2Qy0bx5c44fPsDxvVs5fvgAJ06c4OLFiwx55vl85dqQlX+Pi/h2m0wCrBz+2aAp6NChA0ajkZ9/WQVax9vG+i4aJz2/zfzvrVmzZkRGRvLRRx+RlZXFwIEDGTBgQJntVRRJkvj4449JT0/nypUrxMbGyg9dRV1chBB8//33DBs2rFiyJU9PT+rWrWumsAcHBxMbGyunzlV4sBkwYID8VkehZArid4eEhLB//362b98O5LsJ9u/fnzVr1sh133nnHfz9/WnTpg3Lli1j586dXLt2jXnz5vHwww8za9YsAgMDFX9vBYUHhGqzfAvyle5v//qSU/EnCXFtzPwuX1dZ9BMnJye6d+/O//3f//Haa6/Jft96vZ7g4GAuXryIwWCQFRAbGxs52sOJEycqXZ6qwt7eHnd3dw4dOiRP3EajkePHj5uFq5MkiXbt2tGuXTsmT56Mv78/a9euNUvBXBpFM1wKIXBzcyMlJQUrXX7kEAuTkZSEDG7FJ5Gbm0NWRho2dk5IwohaMmEyCHJSU/OVVbUWjaUlJiGQrKwwJaeAVoMmOQk0lhiTkkCrQQhBXkYKAFk5yZiEkWbNmvHTTz/h7u0vv2EoTL7biQQqy5KVaQtrsMgCYaJli2aMfv5pejw5FAtrZ3nD7J1iZ2fHoEGDGDRoEAMGDKBHjx4kJiZWeuQUtVotPyiuWLGCNm3a4FYkc+iuXbu4fPkyzz//fLHr27Vrx/LlyzGZTHI8+IsXL+Lp6VlpWVEV7m+++OILnnrqKfbs2UNISEixB8/XXnuthiS7Nygcv3vdunWoVCqMRiMuLi4kJCTQpk0bs4Q5arWauXPnMmDAAFxcXJg0aRKZmZmcPn2aGTNmsGHDBlatWqX4eysoPCBUm/I9adcErqddq3KluzBffvklbdu2pVWrVnz44Yc0btwYg8HAtm3b+Oqrr2SreOvWrZk7dy5169YlISGBd999t0rlqmzGjBnDjBkzCAoKon79+syfP5+kpCT5/h48eJAdO3bQrVs33NzcOHjwIPHx8QQHB99RfyqVSo6mUeDLiIU1GamJmIwmDAYj568mYWGZSnp2Hsa8XEw5BfZvCUltiTCZMFyOgKJvGXLzP0s2lkjGRFSp+Z81KZmYTEaGDh3K7Nmz6dOnDx9++CE+Pj5cvXqVNWvWMGHCBHx8fMDSGlQlW3AlScrPfgmQm0abh5rz+8aN9HzscSwsLHj99dfv6J7MmzcPT09PmjZtikql4pdffsHDw6PYxt6yuHz5Munp6cTGxpKVlSU/BDZo0ACNRkNCQgKrVq2iU6dOZGdns2jRIn755Rd27dpVrK2FCxfy0EMPybGDC/PKK68wf/58xo4dy5gxY7h06RLTp09/4BUqhX9Zvnw5W7ZsQafTydGeCpAk6YH/rRTE716xYoX8AKtWq+nZsydLly6lVq1a7N+/3yxhTr9+/Vi1ahXjx4+nbdu2cluBgYH3TVp5BQWFyqHKlW9HKyfcbTxwtHJk4kPvVGuc78DAQI4dO8a0adMYP348MTExuLq60rx5c7766itMJhNCCBYsWMALL7xAixYtqFevHrNmzap0f92qZOLEicTGxvLMM8+gVqt58cUX6d69u2xFsbOzY/fu3Xz66aekpqbi7+/PnDlz6NmzJ5AfSrFz585ERkbecYQOoXWgnl8WDnotFmoVKpWEIc9Ido6BjKw8rl+JwcFOj4l/XEgkCTSWfPDVl/y4Zg0XNm9GqNSoVBJCJbBwsc5XlG3zLW4aBx1gwtramt27dzNx4kT69etHWloa3t7ePProoyVawktCUqnzQx5KKtA60r7Dw2zcuJHHHnsMtVp9R4qFXq/n448/5tKlS6jValq2bMnvv/8uL8wjRowgKiqqzFf5L7zwgpkiXZCUpPD3smTJEsLCwhBC0KZNG8LDw838/SF/H8Dq1av57LPPSuzH19eXrVu38vrrr9O4cWO8vb0ZO3YsEydOrPC4Ff6bvPvuu3z44Ye89dZblZJK+b9G4fjdhenVqxdLly6VjRJFN0L369ePPn36sGfPHmJiYvD09KRDhw6KxVtB4QFDEuWI9ZednU1kZCSBgYF3lAUy15iLpcqy2pTusjAajdy4cYPExEQMBkOx8rtNKFHZFLiQhIaGlnuCNplMBAcHM3DgwHJlpFu8eDHTpk3j7NmzxV4vl4c8Yx5RKX9jp7bERWVJtoUao0pDXqYgJTmVlOQUjMZ/fdollQo7OztsdTpee/111MCSd/5926DytUelAkmlITbuBl51WnJw52+0aNUKlbVbCRLc+3Tq1IlOnTqVmEhECIFB5JJtSEdgRG/pjEpSFmOFyqWseTw1NRV7e3tSUlKws7PDycmJw4cPm230vp8pOr67pcBgsX//flq3bi2fT0tLw9XVlZycHCA/5On9kipeQUHh7qjIPFMtJg2NWnNPKN4A169fJzU1FT8/P1QqFQEBAXh5eaHRaAgMDKxp8e6IK1eusGDBAi5evMipU6d45ZVXiIyMZMiQIeW6fvPmzUyfPv2OFG8AgzCQZzKQYszlQk4qsTkZqCQtTg4u1AqsTZMmTQmqWwt7Fz2WmnyXk5TkZDKyszlw4ACTx4zBpLNEIJBsNagtJUxZRqKu/s3U6Z/j7uZK3ZBgJGN2sTTwd0quwURWrqHUI9dw+wRD5SUtLY2IiAiz2OyQr3TnmXJIz0skIy8JgSk/HrqovL4VFO6E4cOH89NPP9W0GPcsBfG7p0+fbrZZ3tbWlh49egD5m+2VDZQKCgolUe2hBmualJQUAgMDsbW1JSoqCr1ej5WVFVqtlsTERJydnWtaxAqjUqlYvHix7I7QqFEjtm/fXm6f7pUrV1aKHF56L7IM6aTkpHE19SpWFla4WbthY6lHr7cFbS56P2dysnJJTEoiIyOD8yf+wuLaNbCz51pWHCJP4JCUh4os6jd/hPoBgSz//kvsNBqEWoNUJA38nZBrMHHxZhqmMl76qCSJuu62aCzu/vnU1taWa9euyZ8LW7qNIg+1ZImNpSMSKtLzbt11fwoKd4vRaGTWrFls2bKFxo0bF3swnzt3bg1Jdm9QEL97wIAB9O3bl0mTJtGoUSNOnz7N1atXARg2bJjiTqKgoFAiD5zybTAY5IgOarVaToig1+tLzRJ4r+Pr68vevXtrWgzUKktcdJ44aJ1JzrllpoS76FzkejY2NnL0GSEEhlu3IC2DdCFwstaTq1Zjr9WQfv4o2bkW4OUBpAHSv3G6C7VXUYwmEyYh8HWyxqoE5TrbYOJaYiZGk4nKfjmUZ8oppnRbSPlvhgymkkNcKihUN6dOnZL3HJw+fdqs7F55i1nTlLaBMiAggNWrVysbKBUUFErlgVO+tVotubm5aLVadDodiYmJ2NjYkJycrFgpKgFJkrBUa2UlPDYzmrScTG5mxuJqbV9ifbWHO8brV6jv50hiai5qCy26nARUWiPR2RIJFyOwsFDhqNfi4OCAXmSC8UZ+bO5/kujcCVYWKnSakv4Eiu8FqCyyDWkYhQGt2horta2iyCjck+zcubOmRbgvUDZQKigo3AkPnPLt4uJCZmYmtra2eHh4cPnyZeLi4hBC4OvrW9Pi/ScQQpCRl0F8VhxZeVlYWVhhq9WhklSopOKWZEmvBwsrLHJy8bDKAlMSqDQYsqxAlYuTU/7GhcSUFOKSorFQq3DQa3GwzcbO1Q+Vuhy+6iYT5CSD1qFyB1tBrCxsyTakk2PMxGDKw8pCL1u+FRQU7j/UanWJmypTUlJYu3at4n6ioKBQjAdO+S6cLtnOzo6GDRuSmZmJVqvF2vrOragK+aTnpstKt85Sh5+dPzaWNnJ5SUpmgfWblMugBnTOoFKjttHgrXP5J4W7wMFKIilTwmA0kZCSRUJKFqroZOztHXBwcMDe3r5YNlCEgOxkSL0OxhxQa1FZe5HfUfVjqdJiYamRfb4z8pJQS5ZYWeiRqj/hrIKCGRkZGdjZ2d02+daD7vN9O0wmEw0bNuTGjRt4eXndV6FrFRQUqp4HSvk2mUxcunQJf39/OdSWVqtFq9XWsGT/DW6kXSfXmGumdMvp4XNzISMDodVCTg7Y2CAVyqaosrWFLC3CQgsWarC0hdw01JmxuEmJ5Ao1Ors8/FztSZfsScnIIT09nYyMDJKSkuSkQra2tvj4+GBtbY3ISoTU60jGXIwaO4StL+qsOLRpkdSSdEgGf9Doq/0+SZKEpVRcCVfCCyrUNHn/JL06fvx4qXWUtzS3R6VS0adPH7788kuWLFmiKN8KCgpmPFDKt0qlIisrq6bF+M9hIVlgqbJErVLjZ2OudAOIvAzITgKTBWQa863RmRkIyxJiv6ss8lPA56RATjqSIRu1hQ6dMIFRQpWTgo2lIEOtx97eHi9XO9JSU0nONJCdnUNqaioqlQphMkLKNbLzjKC1J1vniwkVausANJZpaNKuYZF2BawbVvPd+peSlHCVJEp0zVFQqA4KMrIuWbIEHx+fYgl2hBBmkXsUSmfEiBF8+eWXrF69ms8///y+jKSloKBQNTxwq7yzszMJCQk1LcZ/Cku1JbUdgwiwC0Sv0Zsr3iZjvuKNAEsDYAKNBvIMxVPLAxKA1hGyUsCYh9A5gM4OVCpQWSAca4ExFxtVNlqtFjsnD7xd7WhY25N69erh5+eHVqtFZMYDghtpas5cTSE3KRo7rQVqSSIVG9KwwQQlxvjOrsQY3+VBkiQsVVr0lk7YWroqFnCFGicwMLDEeTIxMfG+zYdQ3bRo0YLQ0FBycnJYtGhRtfRpNBoJDw9nxYoVhIeHy9G8FBQU7i0eOOVbCEF8fDxnz57lypUrXLt2zey4HeHh4UiSRHJyctULew8RFRWFJEmcOHGixHKVpCpmxRZC5IcFFAJM1oAAi7z8zY+WFvnW76KxtnNSkHLTQaUGC22+/7chFww5IKmQdE6orWyxU2Xj5OSEVBB+MC+TzIwMDAYDuZkpSCL3HxnyFWm9FixM2ejVuVhlRZOclkVypoFLcenFjmuJmagkCXU1p9WWJEl5pa9wT1Ba4uP09PQ7ynL8ICJJEq+88goAX3/9tVkynqpgzZo1BAUF0blzZ4YMGULnzp0JCgpizZo1VdqvgoJCxfnPKt9ff/01tra2Zink09PTqV27NiNHjkStVpOdnU1mZia7d+/Gz8+PM2fOVLifxYsXy69qqxKj0VjiUXRCL1o+fPhw+vTpU666t2u3rGtKavfGlQiGPPM89Zt1Qu3kytgJH2KSDGDKAct863dOejpTpkyhdu3aWPk1o8kjA9n4yxJMhlxMWCBlxBPYuAMqr+aoPBrnK6iOAUjuDXl15LMYY09hSo1h+Atj8PD0xNvbGyu9Iyo7f1o//gz+LlY0CvLCSqfHlHULU1YiiSmZJKXnEnUrD1NCFNY5ibhrjQQ666jtYk1tF2uCXK1RSwKj0WimiJhMpjLvW0FdIQQ5eQaycvPIyTOUWbci7d6PdYUQ5f6tKXUrXresv8uS6mZmZpKRkVHsAHj77bd54403kCSJyZMn88Ybb8jH2LFjGTRoEE2bNkWhfAwZMgQ7OzsiIiLYunVrlfWzZs0aBgwYQEhICPv37yctLY39+/cTEhLCgAEDFAVcQeEeo9p9vk03rmM6cBBV64dQeftUWT+dO3cmPT2dI0eO0LhxY3Q6HXv27MHDw4PTp0/j6+srRzf5+eef8fLyktMC1wRGoxFJkor5WBZw8uTJEs/b29tTp04d+fNff/1lttjeunWL9PR0jh8/jq2tLfXq1ZPLTp06ZfZwUhhra2saNGggf7548SIA58+fL2YVs7KyolGjRvLnc+fOkZ2dTXR0NEKtY8iwZ1i+fDnxqemciUoiJNAZ8nLA0oJRY8fy24YNvPPOO/j7+3PgwAH6j3yThQsX0jC4Hk29tRzeuwuDhTURly6QnpVLREQEo0ePpmnLNhyPznddScoy0qNzGxbMm4bayprLcdnk5hq4kWmFZZ6JpKSb5OTkyDI6WUukZAkMBgPJSYkkJyWWeB8AQkND5VBhV65c4dat0rNQNmnSBCGpycwzEBd9g9Sk0uuGhITIm31v3LjBzZs3S63bsGFDdDodALGxsURHR5daNzg4WE5iFBcXx/Xr10utW69ePWxtbQFISEiQs/OVRFBQkPygmZiYSFRUVKl1a9WqhZOTEwBJSUn8/fffpdYNCAjAxSU/aVJKSgqXL18uta6fnx9ubm5A/sP0hQsXSq3r4+ODh4cHkK9wnjt3rtS6Xl5eeHl5AZCdnV3mg7i7u7scljQ3N5dTp06VWtfV1RV/f38gP8HXX3/9VWpdZ2dn2aXDZDKVuenR0dGR2rVry5/Lqlt0jmjXrh3nz58vse7JkydRq9UIITh16pSckAxAo9HQpEkTwsLCSu1LwRwbGxueffZZvvjiC/76668qWWOMRiPjx4+nV69erFu3Tl5DWrduzbp16+jbty9hYWH06dNHCXmooHCPUK2WbyEEpiNHIPEWpiNHSn21WRnUq1cPLy8vwsPDOXv2LAaDgfDwcNq3b0/t2rXZt2+fXDc8PJzOnTsDsGzZMlq0aCHHAR8yZMg/oe6KEx4ezrPPPktKSorsMjBlyhQgf1F+88038fb2xsbGhoceeojw8HD52gKL+YYNG2jQoAFarZYrV64QHh5Oq1atsLGxwcHBgQ4dOhATE1PqOGNjYxk0aBCOjo44OzvzxhtvyIrZt99+y8aNG9m1axctW7akfv36ZjIUZt++fbzwwgt07tyZLl268OqrrxIREVGBO14cLy8vwsLCePzxx9Hr/4kqIqlAkoAssNTw24YNjBgxgnbt2uHj48OAAQNo3bo1y5Yty69v54urXx08vbxxdfPAxcWFP//8Ex8fH5o1a1aoNwmNhQ5vN3s87K1wcXHHwcEBtVqNSqUye8hQqVT4OlvTpJYTdWr53TbaTWkPKCWRZzSRlmtALUlo1P/ZF0sK/1E2bNjAzp07GT58OJs2bWLnzp3ysWXLFr755hszRV7h9rz11ltcvnyZiRMnVkn7e/bsISoqirfffruY8UalUjFp0iQiIyPZs2dPlfSvoKBQcarV8i2uX4e4OKRGIYjTpxDXryNVYWKbTp06sXPnTnr06EFOTg47d+6kf//+2Nvbs3PnTrp06UJubi779+9n/vz5QL7S/NFHH1GvXj3i4uJ4/fXXGTFiBL///nux9tu2bcunn37K5MmTZQtcgZL57LPPEhUVxcqVK/Hy8mLt2rX06NGDU6dOyYtXZmYmM2bM4LvvvsPZ2RknJydCQ0MZOXIkK1asIDc3lwMHDiBJEo0bNy5mtcjMzKR58+Z06NCB3bt3Y2FhwUcffcSbb77J8ePHmT17NsnJyaSmprJw4UIkSZItjJBveS0gMjKSd999l0aNGpGRkcGUKVN48sknOXHiBCqVirp16wJQv3792752Dq4XBJk3waRBGC1BktBbWeHm5ETDoCAQWUAG5GZhNBgI8vGRU1kDeHh4cGD/fkJ8bSHpMmTagY07Qa4W5Grz2LZlE+NeGkGzeh4gWYAw4mhrw/rdu3Bv3BUHOz0d2j7MB2+/g6eXF3mJ0bi7AyZAbUGS2oN4gwl7SY2dZR4NG9RHoCIjI4OUlBSSk5PNrOSnTp2idu3aODo64u/vj5+bTX7McJ0rZMWDWgtW+ZEM0nKNaFQSeq0FNn6++Pr++3YnPceAUQjsrfKTAhVeKL29vWXLa0kUruvh4fFvvHoh8iPDaO3/eagxr+vm5oarq2u52nVxcSkzIkPhuk5OTjg6OparrqOjo9n3W5TCfu729vblrqvX68td19rautx1raysyl1Xo9GUu66FhUW566pUqnLXBSpUd+/evcUeOFNTU81+f9W1QfB+x2g03ja7ZcHbl6qiwDhT+O1jYQrOl2XEUVBQqF6qTfkWQmA6dhTc3FC1bo0x7iamY0eRfHyqbJNZp06deP3119Hr9Rw7dozjx48zdepUJElixYoVPP300xw5coSsrCy8vLw4deoUzz33nHx9rVq1+Pzzz2nVqhXp6en/Wm//QaPRYG9vjyRJZhNsREQEK1as4Pr16/KCFhYWxubNm1m0aBHTp08H8mPqfvnllzRp0gTIf5WfkpJCr1695FfKdevW5fjx46jV6mKT+i+//IJKpeK7776T72GBRX3Pnj1069YNa2trcnNz8fb2LnZ/Crf31FNPmZV9//33uLm5cfbsWRo1aiTXLUmOoqgstGBpA4YsMKrBJOVHMTEYUGWmgyYHTGowCLp37sxnX35Jpx49CAoKYseOHaxfvx6j0Yjape6/CXISL6FWa1m/9yzJqek8O7gPKkkCjCAJHuvSnoFPPIK/jxeRf//N5Nnf0LVfP7b/eQB3nRaVcAZDDqmW7uRmZgMmLKzsIC8RKTcFoXHEysoqP064mxtZtxJIzs0jKTWVrNxc2Y1DMmaRkpJClkmDkwa0OmfITgBTDgaVFUYBNhp1iZsnrbUSqTkGTEhYqotbqMqLSqUqMXkQdj5g5SAr4XfUbhXUlSSp3K+7lboVrwtUqK61tXWxTZNF920o3J41a9Ywfvx4M/ergIAA5syZQ79+/Uq85uzZs9ja2lZqNmVPT08ATp8+TevWrYuVnz592qxeSZTnIUJBQaHyqLb34gVWb1Wz5vm+zc2aQ1xc/vkqonPnzmRkZBAfH8/NmzepXbs2Tk5OdOvWjTNnzmBjY8O5c+fw9vamWbNmuLm5cfz4cfr06YO/vz+2trZy2uCyfGGLcuzYMYQQ1K1bF71eLx+7du0yc+XQaDQ0btxY/uzk5MSIESPo3r07vXv35rPPPivTWnH06FEuX76Mra2t3IeTkxPZ2dkVdhmJiIhgyJAh1KpVCzs7O9n3tCLjLkCSJLByzFcCdRLY2YFaDZaWYCXyz1s5gp0dn86fT5169QgODkaj0TB69GieffbZ/IlfkkDnCK4NwbE2uU7BfPfDT/To3A4vd1dMQkKQHwhlUM+29Hq4GY1qedK768P8vnoxERGXWbtuLWpyQAKTWseVFBMJeRpShA7JQpMfUcWQSWZaEgkJCSQnJUFaPDqdDk9/fxr4+RISEIClpeU/YROTiU/JJTo2Pj/ijYUOLKwR2YmYTPkKjFpV8sNkwXnT3bpb5WXCrYuQFAEWVuBUJ38cSRH55/My7659BQWFMrmTDY6zZ8+mUaNGfPDBB5UqS4cOHQgICGD69OnFNteaTCZmzJhBYGAgHTp0KHUsSpQUBYXqpVqU78JWb8kn/zW85OMDbm6Yjh2tMt/voKAgfHx82LlzJ4cOHeKRRx7B2dmZkJAQAgMDuXz5MocPH6Zr1664u7uj1+vp1q0ber2eZcuWcfjwYdauXQvku6OUF5PJhFqt5ujRo5w4cUI+zp07x2effSbX0+l0xayjixYtYv/+/bRt25affvqJ4ODgUjd0mUwmmjdvbtbHiRMnuHjxIkOGDKnQverduze3bt1iwYIFHDx4kIMHD1Z43IWRVOp8BduUBSrjP3G6BYgcsHJCsrJG0mpx8/Ji3a+/kpGRwZUrVzh//jx6vd48lrBKRa6lPeHHzvHHHzt4ZnB/hCEXVV46uWnJWObexGhUITQ2gEASBtzsrPD39SY55t/wkao8S+o6afF1siZPSJiMeWDIBCSEIQcJ0OQkIOXGQs51yE4mDQnJZILc3PywiRI4ubphb2+Pg4ND/vendSQxNZvzZ05zK+YGySkpJYYV+3/2zjwuivr/46892AMWVu5DETAPVNQ0TUQJzFsxzOzQMi2tzKw8K82fgqmk5VWp3Wr51cqrTM0zUbxRDkUUEEGQQ0Tua695//7YdtxhF+RUwHk+HvPQnfnM55hZZl+f97w/77eO0X/PhfV905Ofqrd223UA7DsAMiVg31H/WafSH68nai1jNga6YVM/5FjoPDxNhcoLHH19faFQKNgFjkFBQZg7d67J24QBAwaAiLBlyxakpKQ0WH9EIhFWrVqFffv2YcyYMZzJwJgxY7Bv3z58+eWXZi3ZfJQUHp5Hw0NxO2Gt3sNHsGLTYP1mDv7TqL7fAwcORHh4OPLz8zFv3jxW1AUEBODQoUM4d+4c3njjDQD6SB65ubn4/PPP2deCFy9erLZ+iURi8pDt2bMndDodcnJyqrQ2VEfPnj3Rs2dPzJ8/H76+vjh06BBef/11k3K9evXC77//DicnJ9jY2NS4f5W5d+8erl27hu+++47t76lTp2rdbxPEloC4/H6sb50KEFtCYGFpUlQmk6F169bQaDTYtWsXXnrpJc5xHcNgz+//g6OjE0aNew2C8tsQqIohJQG0ZSJAIYFAXQoSSUBiKfKyspCekQl3R6v/ahAAFjpI1RUgS310D4n6v0gkFtawLkuDAhpAYAmSuAIohSo/AyWwQ5kAcCorgoAKAEtHODi0goPDfT9qgVCEojIdNFodNPn3UJR/DyKRCEqlEra2trCxsYFQKESFRgeRABBXYRkH9KJXV008YJFQCAmg9/GWKStdRCVQoQTUpVWeXxPUWgaJd4qrtdALBQJ0dLaGRMwvKm2JpKWlwd3d3Wzs/vT0dLRt2/YR9ezRY1jguH379ioXOPr5+SEiIoJ9cwoA/fr1w9ChQ3H48GGEhIRgy5YtnHPNuX4Y2nvQvrFjx2Lnzp2YM2cO/Pz82Dq9vLywc+dOs24wVUVJ6dOnD2bOnImMjAzMmDEDQUFBnKg3PDw89afRxTdr9baxgUAmAxllTRPIZICNTaP6fg8cOBDvvfceNBoNAgIC2P0BAQF49913UVFRwUY6adu2LSQSCb7++mtMmzYNcXFx+Oyzz6qt39PTEyUlJTh27Bh69OgBS0tLdOzYEa+++ipef/11rFq1Cj179kRubi7+/fdfdOvWDSNHjjRbV0pKCr7//ns899xzcHNzQ0JCApKSkjgPcGNeffVVfPHFFwgODsaSJUvQpk0bpKWlYffu3Zg3bx7atGkDT09PHDp0CAkJCbC3t4dSqYSFhQWnHkOklO+//x6urq5IS0vDJ598UourbB6BQIDoa7eA8nsoKc5Hbu49xFy7Bakshw1jeP78eWRkZODJJ59ERkYGQkJCwDAMPvroI05dDMPgrz/+h9cmToSlBQMI7ECFOQAYVKAYi5esxTMjxqK7lyNS067j05DlcLBrheeDhgGMENCKAYlan7CHAFdZBQTQARbWEBRlgoggkCsBRgFY20EgaQ1BSR5kxUUQQwtBeQkE0IJUJcgVuUIs0fuHi0QiEKODp7MV7JSWyCvVorCgEDqdFnl5ecjLy4NQKISlwhpyhQ0c7W2r/J7XVPR2saBGfWWlYxgwRHC3s4TMjLiu0DJIzyv7b5LAi++WiJeXF7KystiwjgYMGS4fZx/x+ixwXLp0KQ4fPoxff/0Vs2bNYhevm/Mfd3R0hEAg4ETbMrfP2M88ODi4xr7b5iYR5vrRtm1bbNiwoUo/dh4enjpANaC8vJzi4+OpvLy8JsU5MFotaf63lTTff1f19r+txGi1ta67JqSkpBAA8vb25uxPT08nAPTEE09w9m/bto08PT1JKpVSv379aO/evQSAoqOjiYjo+PHjBIDy8/PZc6ZNm0b29vYEgBYvXkxERGq1mhYtWkSenp5kYWFBLi4u9Pzzz9Ply5eJiGjTpk2kVCo5bWdnZ9OYMWPI1dWVJBIJeXh40MKFC+n8+fOkreL6ZGVl0euvv04ODg4klUqpXbt29NZbb1FhYSEREeXk5NCQIUNIoVAQADp+/LjZeo4cOUKdO3cmqVRK3bt3p/DwcAJAe/bs4VxHw3UgIvLw8GDHWxXQ55/kbB4eHuzx8PBwtl17e3uaOHEiZWRkmNSzd98BAkCXr1wmpugWMRUFxGRdJm1qDJXcvkGDBw8hW3sHsrCwoLburen1l4Pp1sV/iClKJ0ZdQYxaTUzJHWKK0qmivJSYwlukK7qtr/zOVWLyU4kpzCCmMI0Y1X/ly4qIyYohyogkJusqMbnppM6Oo4yMDMrIuE26ihJiGIaYsrukLrhNGrWKiIgqNFrKvJtHickpFB0TS5GRkZzt+vXrdOfOHVKpVJwxlqk0FJueT3mlKipTaUy2vFIVxabnky47jig/1fwFz08lunO12nvyIAz9KFNp6nScp2lS3XO8sLCQALDPDYFAQDk5OSblUlNTydLSstH72tBUHl99MPwGnD171uzxM2fOVPusfeWVVwgADR48mBiGoV27dpFAIKDRo0fT2bNnqbi4mMLCwtjnZVhYWJX7zp49S6NHjyaBQEC7du2q1Ti2bdtGAKi4uJiIyKQfmZmZBIB69epVp/p5eB43avOcERA92OG6oqICKSkp8PLyqlNqYSopASoqqi4gk0FQKZIIjx6dTofo6GhOopemQHl5Oezs7HDgwAH2zUGjtqfWp4Lv4KSATFegd2EpK4BAogBaeaBcrUXK3WJ425SCEUpRXpQHhaACsO/AurkQowNKswAQQASV1AkyqQzIidf7izNWejcZRghoJIA2CyANABFg0RoQCkHWMqiL7kCr0UIh0oJsPYGKXOSr5KhQa6BUKmFlZQUigpYhMERQVZSj+L8QhuXl5eyYvLy82NB+RIQKjY4do1xi+lLKcA18LDIghA5Qetx3PTGEHCxM04dfdOpicn5drnV1/ajqOE/TpLrneFFREZRKJd577z1IJBKsW7cOb731FpuIDNA/i86fPw+RSITTp08/7O7XC8P4CgsLq3TRqyk6nQ7t27dHt27dOO4agP4N3ZgxYxAXF4ekpCSzz+yUlBR4e3tDrVbj999/x8cff8ypS61Wo23btnBzc4NUKkV2djYSEhLQqVMn1qp+9epVXL9+HWfOnEFGRgbWr1+PrKws3Lhxo8a/E4b8FmfPnkWfPn1MxmRYe3Ts2DGsXbu22jHx8PDU7jnzUH45BQoFwIvrFsWJEyfw7LPPPhThbYLUFijNBkinF5wVhYDQCm5y/QSPYQSQMuUgsQWERv7lAqEIJLEB1AUo1FpAKrv/9RcAIKUtoBYB2mLAUgEUiAD6LylQq1aAUAiBUAiplQ2kJTn6hcKqfH20E7XelcTg0iMQCEA6DUqKi1l/9tatW6OiogIFBQUoKCiAUnnfZ/vOnTu4ezcXjEwJoPq/FbW1B2RlGUBeEiDVx0BH6R1AVQRIrAFl48XO52nZ8BkuH4xhgeO4ceMwZswYzJ8/Hz4+PoiLi0NYWBj27duHnTt3VilSvby88PHHH2PDhg2Ij4/nuH7s3r0b7733Hu7cucPJeDtt2jS2HBHBz88PHh4eyM7O5tS9bNkyLFq0qEbjMI6SMnPmTE4/jKOkBAQEQC6Xm/Vj5+HhqRuPndkqNzcXYrGYTZN9+/Zt3L17FzKZDO3atXtgtkMePcOHD2+QVMmkKQNUBYC0ldmFmOYQCEUgWStApwJpVBDkJUFqYQWZ2AJUoYFEk40SyFFh6Q4LdaXslAJLiFABK5EKWjJa2KgqBKTW+tjkYksILGQAMQCjAUQyfZhEQ5+ltkBJjv4YAEhtYS8XgWEYjj+3SqWCSqWCQCBg44TLZDLY2NjA3t6e8+OsT+xTAbHUmt2n0WhQXl4Oa2trTr0klumjmxjFQIdIqg/HaGGjj6pSedz/IRIK+UWSPFWyb98+2NjY4I033sC6devqbSVuqdRlgaMxCxYswAcffIAjR44A0PuJGyKP9OzZE9nZ2cjKysLVq1cxePBgNumRj48P/vrrLwBgk7f5+Pjg/PnzGDx4MEJCQuDj41Mj/2zjSURGRgYAwMPDA2fPnjWZRPCJenh4GpbHTnxnZ2ezK/VLSkqQk5MDd3d3FBQUID09He3bt69xXVlZWcjPz0dFRQWEQiEUCgXatGnDeaVLRMjKysLdu3eh1WqhUCjQtm1byOXyBh9bc0MfNzsfAAOo8kEiqT5EYTVUaBkAWgBSWFhYQyiSQCOzh0VJBgSaUpBQAq2yHVILRGAKGQAlJnWIBRboqFBBoisE4AjYeoIK0yHIvwkyJKvJS9K7tggtAFtPzvkCoUhfTqcBpLZsnytHPjCEkhSL7/+ZMQyjjw8OwNnZmRXgTzzxBHLzCnBXdb+O/Px8pKWlQSwWQ6lUwtLaRm+JB+7HQJcq9RMHmRJqHfgoJTwNAp/h8sHUdoGjMTKZDDKZjE18Exsby0YemTlzJgYNGoTU1FTW7adr1664evUqoqKiMG/ePAD6uOGGpDqGcv369cPcuXMRHBxco34YJhHvvfceALBJ4SpPImqSqKc6+CQ+PDxcHjvxrVarWet2QUEBbG1t4ejoCIVCwaaIrynFxcVwcnJifXwzMjKQmJiIrl27sg+W7OxsZGdns36WmZmZSExM5GSNfBxhXTYEAOTO+jTtqnxA7mC2vEgohFAgQHre/QQyYoEYHRQVEAlU0Fo6IKdUAEd7F0gsxOgorT5kH8gCQnUeSFOmt7gr24CKMwF1OQT5yXpLskgKgPQi22D4/s+3WqAp1Qvzaqz1YrHYJCsqwzCQSCQgIs79Ly0tBRgtZCJ9QwwxYBgGYrEYWq0W9+7dw7179wCBAOkVStjb2UKpVOqFvVyf5l3HaDlRSjQ6gs5IiKu1DO4UVaBUrYWOET7QCn5/omNuP09LpLS0FDY2Npg9e3a15VavXv2QetS0EYlE9XLDGDBgABwdHTF69Gjk5+dj+/bt6NOnDzw9PbFs2TIAeiG8YcMGBAQEYNq0aayANUTvMnYRWbFiBfz9/WvlHjJ27FiMGDECrVu3hlKpxPvvv48ZM2awLkc1SdRTHXXJBMrD09J57MS3UChkw2QVFRWxobQEAoHZxCjV0bFjR85nT09PxMbGoqysDNbW1iAi5OTkwNXVFba2eoHk5eWF2NhY5OXlwdHR0Vy1jwfaMv0mc4BAJNG7clTk3hfDlZCIhejobG0iqBmdBUTaQjAWSjhaWbJiUv9v1cKSyApgKvQWd6FY7/oit4Wg1ROsJRnaCqAwvUF9q8ViMRwcHEwSS2k0Gn1YTghQqlYjvSQdFhYSOLu6QAAhysrLUVRYAK1Gg6LCAhQVFkAgEMDa2hqtWrX6z41KP16ZWC+sk++at4IbJjBVWcHNTXQqIxQIIKpFinme5oFGo3elio6OrrJMY4SEfVyobAH28fFBWVmZfvIN/eSnrKwMb7/9NhYsWAAAWL58OftG9tq1awCAt99+G2VlZSZ+5j169ABQO/cQgzjOz89Hfn4+5syZgy+//BJffPEF2rVrVyM/9urqHjduHIKCgrB9+3bWN3758uUYN25cjVx06gNvcedpqjyUaCdNiZs3b6KiogKWlpbIy8tD9+7dIRaLUVBQgIyMDHTt2rXOdVdUVCAuLg5du3aFXC6HSqXClStX0KVLF07UAMOKdE4WxypoqtFO6oM+6kg2IJZC8J+lm4iAint6Vw9Llwe6nzRoP8AAAqH5donu+1brVHpruE0bQNZK7/rRUH0hQrlKjZR75WAEGogkOZDo5LDUyaAR6FBEEhAjBbQq2Im1UKkqUFRUxIm3LLe0gkaqRCd3/YQyKaeEE6vbEJ/b3U7/XUzPK6syYkmNkv3wrivNippEO2mIaCBNkaYwvqoswC+88AJWrVplUt5gGDKO6W1lZcUKdQNeXl748ssvMXbsWDZCyfHjx2tk+TYWxwsWLEBycjLmzp3LWchpXH9tqG9UmPrCW9x5Hja1ec48dr+ebdu2hUKhgFarxRNPPMH645aWlsLOzq7O9dJ/md8UCgXrz22wIhn7/Bo+G45VhmEY6HQ6ztaS4LibSG3Z/YY07SDojz8EBEIRILMFBCKO7za30H++1Y5dAdsnAKeu+s8NbP0TCASwlEnRwdkG7rZ6cWxv7QgLKylIooNIkgsrq3to6yyHSCSEXC5Hx44d0bp1a1hZWUEikcBCLIJEeH8uTTotSKOCzEIEuUTMinCZWGg2eY4xErEQcom4yo0X3i2b8vJylJXdf/Nx69YtrF27FocPH36EvWq+VJfGffXq1Rg0aBAAfbSkDRs24Pjx48jMzERmZiaOHTuGXr16wdXVFXfv3oWnpyf8/PywdetWHD9+HElJSRg7dmyt3UMqZ7j09fXFq6++itu3b3PavH79ep3EqiGJz4IFCzjCW6fT4eTJk3jqqaeQkpKC8PDwWtf9IKq73uPGjcPu3bsbvE0enlpRk8Dh9Umy87iQmppKsbGxnMQpxcXFFBkZaZJMJSUlhRISEszWk5GRYZKUJTIyssokO80NRqtik+SYPV5RoD+uVZk9/jhQpimjq3fjqExTRkREDMNQedk9UhfeovR71+nW3VS6e+8uMQzDnpOXl0cZGRl043Y2m5Qn6toNSkxMpLS0NNLpdJzkOHyinMeP2iTZGTJkCG3cuJGIiPLz88nJyYnatGlDMpmMNmzY8FD73RA0ZJKd2qLVasnT05NGjx5NOp2Oc0yn09Ho0aPJw8ODWrduTQDIycmJTp06RUVFRXTmzBmTJDrGyXDOnDlD+fn59M0331CvXr0IAP3xxx+cto8fP07btm2j48ePc35H6pss6EFUTuJj6Lunpycn6ZqDg0ODJvCpyfX28vJqMb+pPE2H2jxnHjvzVWFhIYqLi9nPOTk5uHr1Km7evAmt1nx4tgeRlpaGwsJCdOrUiRMX1xDzuXK9Wq3WJMW7ARcXF/Ts2ZPdunfvXqc+NVmEFoDYEtCU6N0+jCBGB6hL9KH+RJIqKngMIQZSbTnEAgFcLSxRIShFkbCQ43srl8shlcmhMVrGIYT+NbVIJGLLkk6DO1kZKMi7Bwu0rLcqPA1HVFQUaz3duXMnXFxccOvWLfzyyy/46quvHnHvmhdVWYAB/Rqk+fPn49atWwgNDYWFhQVycnIwYMAA2NjYwM/PD3FxcRzfaEOEkitXrsDPzw+2traYMWMGoqKiAAAfffQRdu/ejd27d6N9+/YYOHAgJkyYgIEDB6J9+/as1dfgF24II1iZ+oYXNERGMURKqWyNPnr0KACgQ4cODWqNrsn1TklJQURERIO0x8NTFx478X379m12YWVZWRnS09OhVCqhUqmQnp5eq7qICGlpacjPz0fHjh05McJTU1Mhk8mQnJyMoqIidj/DMCguLjaJgmFAKBRCJBJxtqbC5s2b2fjodaUq9xIiAspzATD6uNoPkcmTJ2PMmDEPtc2aQv+56RAImYwWRAxaW1jBydKZU04mk8FSYQ01iVChZVChZSBS2EEslUMik6NCo0OFlgFTUQKdVquf6JTcRXpaKvLy8qDRaHD37l0UFBRwFoNu2rSp3vfcmNTUVAgEAsTExDRYneaoyT0NDAzEzJkza1xnQ3z/mwuGReMAcPjwYYwdOxZCoRC+vr64devWI+5d86KmItfS0hLff/89AODFF1/E//73P45biTFjx47FF198AYFAgF69emH9+vUoKChgXSteeOGFB7pdVBbHlalveEHjJD4ajYbj4vL0009j3bp18PLywokTJzBq1CjMmDED//vf/xAeHl4vd8vGnlTw8DQED0V8azMyob5ypcpNm9k4fwQ5OTl455130LZtW0ilUri4uGDq1KnsD39BQQF69+6Nixcvom3bthyRXBPS0tJw7949tGvXDiKRCBqNBhqNhhM1xc7Ojo0HXl5ejtTUVAiFwnr5lzd1jh8/jpEjR8Le3h6Wlpbo0qUL5syZwyZy0PtatwK0ZfokOwCgKQUYlf7/6kITq3hjsm7dOmzevLledZCmDFSSeX889SQjLQOWFpa4cP4koC1DpqYUahC0FtawFAhgJRAiMDAQAoEAn3/+OQBulJLngkahm7sdFi9bgcwyAZJySpCeVwaR1BJiCwuUlZVBrVKhqKAAN2/exPXr16HRaFBWVsZ5U1NWVqZfDFpezu5jGIbzPRcIBBAIBDh37hxnDCqVCvb29hAIBKxfp7u7O7Kysqr8YXyY7N69G5999lmD1rls2TL4+fnB0tKySqF+7Ngx+Pn5wdraGq6urvj4449N3o4dOnQIvr6+sLa2hqOjI1544QWkpKRwypw4cQJPPfUUmyDs22+/bbBxtG/fHn/++SfS09Nx6NAhDB06FID+mdoSF2Q2JrURuZMnT8b58+fxxx9/YMKECQgMDDRrgNHpdJg3bx6CgoIQGRmJ6dOnQ6lUwtfXF7t27YJcLodcLseuXbvg6+sLhUIBX19f/PnnnwgKCsLcuXPh5+fHiuPKkb7qG14QuJ/EZ9++fQgMDERqaio+/PBDnD9/HmPGjMG+ffvw5Zdf4u+//8bFixeRlZWF1157zcRCX1sae1LBw9MQNLr4JpUKd4PHIGf4yCq3u8FjQCpVg7f9wgsvIDY2Flu2bEFiYiL27t2L3r176+MlAxyxLRaLaz3bvnv3LnQ6HRISEhAbG8tueXl5bBkHBwc4OzsjLS0N8fHxUKvV6NixY5OyaDck3333HQYPHgwXFxfs2rUL8fHx+Pbbb1FYWMhd0S+21G+qfJBO/Z8VXAAYLLoPadElACiVynpZNNlkQaTTj6cBJw4ibSnKiNBK0RqeNl6QymzZ6wYQ3N3d2YQohnCMVtoiRJ6JgKOTC6ykYrjbWbKbp7MdnFzcYO/aFrBxhoOjM2QyGTQaDRtqLDY2FtevX8edO3dYUWjs4mKwkt+9e5fd5+7ujh9++AGlpaXsOXv27DF5wyMSieDi4mKyCLmmqNXqOp1nDjs7O9a621Co1Wq8+OKLePfdd80ev3z5MkaOHInhw4cjOjoav/32G/bu3YtPPvmELXPz5k0EBwfj2WefRUxMDA4dOoTc3FyO9TMlJQUjR46Ev78/oqOj2YyJu3btapBxLFq0CHPnzoWnpyf69u2Lfv36AdBbwXv27NkgbTwuGFuAayJyn376afZ4aWkpbt68yTlHp9Ph66+/RmpqKoYPH24StvT06dPsgtnTp09zjgmFQnz00UdISUnBZ599hjfeeAN///03xowZw7GQG4vj+vxWGVxkkpKSAACDBw/muNIAYLN6AsDPP/9c74WRtb3ePDyPhJo4kddnwSXDMHQn6DnK9OtPqthYUl2+fH+LjaVMv/50J+g5zuKxhiA/P58AUHh4OGd/YmIiJSQkUEZGBrm6unIWfri5ubHl9u7dS7169SKpVEpeXl4UEhJCGs39xWkAaMOGDTR8+HCSyWTk6enJWeiSkpJCAGjXrl0UGBhIcrmcunfvTmfOnOH05/Tp0+Tv708ymYzatGlD77//PpWUlLDHPTw8aPr06TR58mRSKBTk7u5O3333XbVj/+eff6h///6kVCrJzs6ORo0aRTdu3Kh13zZt2kTu7u4kl8tpzJgx9OWXX5JSqayy3fT0dJJIJDRz5kyzx/Pz84mIaPHixdSjRw9idFpiim4TU5RGq8MWkYdHWyIiYtSlNGnCOAoeHUTLli0jJycnUiqV7D2YO3cu2draUuvWremnn34yGdfvv/9OAwYMIJlMRr1796aEhAS6cOECPfXUU2RlZUXDhg2jnJwc9rxJkyZRcHAw+zkgIIDef/99mjdvHtna2pKzszMtXryYM5Zr165R//79SSqVUmfvjnR473YCQLu3/UhM2V1SqVT03nvvkYuLC0mlUvLw8KDly5dXd9tY1Fo1/Rt9jADQpVMHidFxFwYZrluAvx+9++67ZG9vT6dOnWKPL1u2jEYFBVGnLj40bdbHFJueT7Hp+XQx+Q5NnvY+OTq7kkxuSd2efIoOHz1GDMNQWVkZZWZm0tKlS8nZ2ZmkUikFBgbSzJkzycbGhrNwqby8nLKysuju3btEpP9bWLhwIVlbW9ONGzfY58SQIUNowYIFBID+/vtvzj26cOECqdVqYhiG4uLiaOTIkWRtbU0KhYIGDBjAfl8N92b58uXk6upKHh4eRER0+fJlGjhwIMlkMrKzs6O33nqLs7DLcF5ISAg5OjqStbU1vf3225zFzwEBAfThhx+yn/Py8mjixInUqlUrksvlNHz4cEpMTGSPb9q0qdrvvzFVlZ0/fz717t2bs2/Pnj0kk8moqKiIiIh27NhBYrGYc8337t1LAoGA1Go1ERF99NFH5O3tzannnXfeIV9f3yr7VJsFl0REWVlZFBUVxenH+fPn6dq1a9WMvGnyKBdcEpkukqxqMaUxRUVF9Mwzz1Dr1q3ZvwdzCxY9PT055xsWOgKgbdu2mfTDw8ODc76TkxM5Ojpy9nl5eTXoIsijR48SAAoNDWUXfhovjDx16hRncWd9F0bW5Xrz8NSX2jxnHkq0k/LjxyndrQ2VV1o1XdX+hkCj0ZBCoaCZM2dSRUUFu7+iooISExMpLi6O4uPjCQBt2rSJLl68SFFRUUREdPDgQbKxsaHNmzdTcnIyHT58mDw9PSkkJIStBwDZ29vTDz/8QAkJCbRw4UISiUQUHx9PRPdFhre3N+3bt48SEhJo3Lhx5OHhwYr4y5cvk0KhoDVr1lBiYiKdPn2aevbsSZMnT2bb8fDwIKVSSV9//TUlJSVRWFgYCYXCan8Ad+7cSbt27aLExESKjo6m0aNHU7du3dgf0Zr07dy5cyQQCCgsLIwSEhJo3bp11KpVq2rFx+rVqwkAZWZmVntvDOKbiIhRFevF94olrLBiGIYmvfYyWVsraPr06XT9+nX66aefCAANGzaMli1bRomJifTZZ5+RhYUFpaWlmYzr4MGDFB8fT76+vtSrVy8KDAykU6dOUVRUFLVv356mTZvG9sec+LaxsaGQkBBKTEykLVu2kEAgoMOHDxOR/oehU6dONGTIEIqOPEsnD+2kp/v01ovvHb8RU3SLVn6+jNzd3enkyZOUmppKERERJj+E1ZF84zoBoKjzJ80eZyoKKGCAL33wwQz64IMPaMqUKeyxDh060J49e6h79x60YOH/sdFNXn7lFfLt14+OHDtOcfHXKWzFSpJKpazANNzzzz77jE6fPk0LFiwga2trzj0vLi6mtLQ0KikpYSfMAGjPnj3UrVs32rBhA6nVakpLSyOpVEqxsbEEgP7880/OPTp27Jg+OsuNG2RnZ0djx46lM2fO0IULF2jDhg10/fp19t4oFAp67bXX6MqVK3TlyhUqLS0lNzc3Gjt2LF25coWOHTtGXl5eNGnSJM49VSgU9PLLL1NcXBzt27ePHB0dacGCBZz7bCy+n3vuOercuTOdPHmSYmJiaNiwYdS+fXtW8DaE+J49ezYNGDCAs+/gwYMc4ZGSkkJSqZR+/PFH0mq1VFBQQC+++CINGzaMPcff358++OADTj27d+8msVjM9rcytRXfLYmmMD5zwtnR0ZFCQ0PNCsycnBzq0qULASBnZ2d68803SSAQUFBQEK1fv54A0A8//GAiKA1RTIy/U4b2BQIBDRgwgADQgQMH6OzZs+z5oaGhZqOiNATmIpAY+nn69GmzQru+0VbMXe+GnlTw8BjT5MS3wfptbOE2t6+h2blzJ9na2pJMJiM/Pz+aP38+xcbGcsoYhIMx/v7+JlbKX3/9lVxdXTnnGQs4IqK+ffvSu+++S0T3RcaPP/7IHr969SoBYIXzxIkT6e233+bUERERQUKhkL3WHh4eNGLECPahxDAMOTk5sWHAakJOTg4BoCtXrtS4b+PHj6fhw4dz6nn55ZerFR/vvvsu2djYPLA/rOWbYYgpu0tM8W1avXoVK76JiCa9/jp5tG1D2pI77L5OnTqRv78/+1mr1ZKVlRVt3769ynFt376dFXsGwsLCqFOnTvfbMiO+KwukPn360Mcff0xE+jcLYrGYMjNu6y33ZXfpyJEjevG9ezcxZXdpxrQ36NlnB9b5u33z5k29+D59qFrL94cffkixsbFkbW1NJSUldOLECXJyciK1Wk09evRgLfY3btwggUBAGRkZnLoGDRpE8+fPJyLz9/zFF1/k3PNbt25RZGQk3bx5k91neIuydu1aGjhwIBERhYaG0vPPP0+5ubkEgA4dOkRE9+/Rv//+S5mZmfTRRx+Rl5cXqdVqKi0tpYyMDNaiTqS/N46OjpSSksL+TXz//fdka2tLaWlprLV4//79JBQKKS0tjVQqFb3++utkZ2dHpaWlbF0bN24khULB/vgbi+/ExERWCBjIzc0luVzOvtFqCPF96NAhEgqFtG3bNtJqtXT79m1WDBlPzgz3USQSEQDq168f++aISD/BWrZsGafu06dPVzv5ra34Pnr0KM2fP5+mTJlCb7zxBmdrbjQF8U2kf2aFhoaaWJorW68N/PjjjyQWizllPTw86I8//mDFrEaj4YhXtVpNcrmcLC0t2YmYQfwGBQVRUFAQR+g+rPB7la3RBoPKiBEjzFqji4qKzFrva0N1oRZ5eBqaJhdqUCAQwGbOLKijoqA6cQIAoDpxAuqoKNjMmdVo6YpfeOEFZGZmYu/evRg2bBjCw8PRq1cvdnGdwR/MOLENAFy6dAlLliyBQqFgt7feegtZWVmcxBMGP0jjz4b0vwaMQwUaFngYMpZdunQJmzdv5rQzbNgwMAzDWVzVoUMH9v8CgQAuLi6crGeVSU5OxoQJE9CuXTvY2NiwmTTT0tJq3Ldr166ZHV91EFHt7iWj0aeYt1BAIKj0VRQI0LVLFwiZCr1POABnZ2d069aNLSISiWBvb29yLYzH5eys9yE3Ps/Z2bna61e5DkB/fQznJCQkwN3dHS62UjZZkMFP0xDNZfKElxATE4NOnTrhgw8+qHVyEvY6mosKY0hSJBSzfe3QoQN27tyJn3/+GRMnTjQJZRkVFQUiQseOHTnftxMnTiA5ORmA+Xvev39/zmelUgk7OzuTBcOpqakYMGAAzp49i6SkJGzevBlvvvkm6y9qHIITAGxtbeHq6oorV67A398fFhYWsLCwgI2NDScbLAB07twZEomEDRt27do1dOvWDSKRCKr/1or0798fDMMgKioKubm5YBgGPXr0gKWlJVQqFe7cuYOuXbuipKSEjWqk0+mgVquh1Wpx7do1iMVi9OnTh91nb2+PTp06mfxN14ehQ4fiiy++wLRp0yCVStGxY0eMGjUKANhrlZ2djalTp2LSpEmIjIzEiRMnIJFIMG7cOI5/b+W/NcOxhniehoaGYujQoTh27Bhyc3PZ9QCGjefB6HQ6hIeHY/v27WwEj7/++gshISHw9fV9YPKX3bt346233kJgYCAcHPSZgFu1agVPT0+8/PLLePHFF7Fv3z6MHTsWQUFBSElJwbfffosXXngB5eXlKC8vxwsvvICzZ8/i0KFDSE1NRUFBAfbv38/x5X5Y4fcqh0icMmUKACA2NtZsmvmGWBgpEokQGBiI8ePHV7l4lYfnUVC3VU91QBoQAEmvXihatQbSgAAUrVoDSa9ekAYENGq7MpkMQ4YMwZAhQ7Bo0SJMmTIFCxcuxJNPPskuDLt58yaio6MBAL179wbDMAgNDTWb1atyWubKVP7hMxZBhmPGov+dd97BBx98YFJP27Zt2f9XfmAIBAKThSTGjB49ml0A5+bmBoZh4OPjY7JYrbq+UaVFPDWhY8eOKCwsRFZWVrUPTKFQqK/fKOa3Wl1pwS0RLEQCTsxvgUBgIirNXQtz46q8r7rrV7l85XP0kwzoJw4SpUlmTIFQhF5P98PNyxH452QMjv0bjpdeegmDBw9mFxnVGIk1GxVGYGEJaMv17cocoFfget58802sX78e8fHxuHDhgkk1DMNAJBLh0qVLJt8nw6LImtxzpVIJpVJptn4iQv/+/TF+/HiUlpaiT58+D4ydb8gGC4AV4JUxCHUDRAShUAilUmkSx9cQntP477Bytljj77pBaBvGrlarUVhYCAsLCzg6OrITytzcXBQWFnLa0mg0KCoqglgs5lyTsrIyVFRUcMoahL5AIMDs2bMxa9YsZGVlQaFQICUlBfPnz2cnyd988w1sbGwQFhbG3qutW7fC3d0d586dg6+vL1xcXDgpwAH9xFksFsPe3r66S24CEZksNv/222+xefNmTJw4sVZ18eipKrV5aWkpG27P8N01RCEZM2YM5s6di+DgYADAnDlzMGrUKMyaNQu//PILtmzZgoKCAsTGxsLPzw87d+7EH3/8gXnz5uHvv/8GAMyYMQNeXl7swts5c+bAz8+P7UN6erpZofuwwu+NHTsWwcHBiIiIQEZGBubMmYNevXqZhAXlF0bytHQeWpxvY+t3YeiSRrd6V4WbmxtKS0vRtm1bCIVCWFhYQKlUQiKRsD9+vXr1QkJCAtq3b2+yGf/YVw6tdu7cOXh7e9e4L7169cLVq1fNtlPZUlhT7t27h2vXrmHhwoUYNGgQOnfuXCdLVZcuXcyOrzrGjRsHiUSClStXmj1eUFAAAHB0dLwvHP6L+R0bFcmWI6L7YQeNUtA3Fby9vZGWlo4790rYZEGRkUb9/y9ZkI2dM14Z/yp++OEH/P7779i1axcnEk6NEMtMo8KILfVC3IgJEybgypUr8PHxQZcuXUyq6dmzJ3Q6HXJycky+ay4uLgDqds8NtG7dGs7Ozhg7diwuXbqE4cOHIz09HVeuXAEA5OXlmY1U0r17d0RERECj0VRbvyGcoaGfsbGxAO6L99OnT0MoFKJPnz5wdnaGUChEbGwsysvLIZPJ4ODggKtXr0KhUKBNmzYAwP79i8VidOnSBVqtFhcuXGAF/L1795CYmIjOnTuDiEwmbDqdDiqVymRcZWVlnNCMgD6xVn5+PivgBQIB3NzcoNFosHnzZrRp0wa9evUCAJSUlIBhGE40GYMIz8nJQVlZGfr164cjR45Ao9EgMzMT2dnZOHz4MHr37g0LCwsUFBQgMzMTJSUlnD4UFBTgu+++4/Rt3759Jn+zarWaI9p4ak5Vqc3d3Nxw9+5d+Pn5PTD5iyFRzMWLFzFo0CBs2bIFgH6CqtVq8d577yElJQUODg64ceMGvvnmGwDAmjVr2LjgY8eOxY0bN3D8+HEsXLgQAPC///3PrFGpPlZmcxb+6jBYo1999VVs2LAB+/fvb7RoKzw8TZWHmmTHYP0u+eHHRrd637t3D88++yy2bt2Ky5cvIyUlBTt27MDGjRsxevRo9rW5h4cHoqOjIRaLWVePRYsW4ZdffkFISAiuXr2Ka9eu4ffff2cfYAZ27NiBn3/+GYmJiVi8eDEuXLiAGTNm1LiPH3/8Mc6ePYv33nsPMTExSEpKwt69e/H+++/Xedy2trawt7fH999/jxs3buDff//F7Nmza13PBx98gIMHD2LlypVITEzEN998g4MHD1Z7jru7O9asWYN169ZhypQpOHHiBG7duoXTp0/jnXfeYWMqBwYG4u7du1i5ciVupqRi/abf8c+ho9D7WEBv3WV0gNDCxKrcFBgyZAieeOIJTH5nFi5ficfp8IP49NNP7xdQ5WPN+h/w257DuH79OhITE7Fjxw64uLjUOqQhJylR2R39TjMTEltbW2RlZeHYsWNm6+nYsSNeffVVvP7669i9ezdSUlIQGRmJFStW4MCBAwDqds8NyOVyuLu7Y9q0aWy2PmOr9t27d3H58mU25JjBXWTGjBkoKirCK6+8gosXLyIpKQm//vorEhISqmzr1VdfhUwmw6RJkxAXF4fjx4/j/fffx8SJE1k3I0AvIKdMmYLr16/j2LFj+OyzzzBjxgxW+IhEIkilUojFYnTo0AHBwcGYMWMGkpKSkJ6ejtdeew2tW7dGcHAw7OzsTOJbW1hYoFWrVpxwimlpaUhMTGTDkMbExCAmJgalpaWQSCSQSCT44osvcOXKFVy9ehWrVq3C+vXrOSJjxIgRiImJwerVq5GUlISoqCi88cYbcHd3Zy2Uhus8b948JCUlYfv27fjpp58wd+5cANW/xag8iTBnAJk6dSq2bdtWZR085tHpdJxkMsYxtqdPnw5AH47VnEA1tj7/9ddfAPST5rNnz6KgoACurq6wtbVlJ2eGsoA+LryXlxfef/99jlg1CN2QkBB4enpixYoVDRp+70FZNB9EZVeUqrJ68vC0OGriRF7fBZecuk6cpOzBQ6n8hPkoDg1FRUUFffLJJ9SrVy9SKpVkaWlJnTp1oilTprALl2JiYuiPP/6g9u3bk1gs5iyoPHjwIPn5+ZFcLicbGxt6+umn6fvvv2ePA6D169fTkCFD2FByhoV/RPcXlkVHR7P7DOEPjVdvX7hwgYYMGUIKhYKsrKyoe/funIVUHh4eNGvWLM5CEeOFdOY4cuQIde7cmaRSKXXv3p3Cw8M5C0tr2reffvqJ2rRpQ3K5nEaPHv3AUIPG7Q8bNoxd7Ort7U1z587lLATbuHEjubu7k5WVFb3++uu0NGQBebRtQ4xWRUzxbZr02sucRZBEptEpDNdnzZo1VY7LsKLeeLFa5cVw5hZcVm4nODiYE03DEGpQIpGQd8cnaO+enQSA/tn3JzFFt+i7jd/Qk08+SVZWVmRjY0ODBg1io+kY2gwICKjyGiYnJ3MWyTLqUmKKM4hR319AaK6fxlT+nqjValq0aBF5enqShYUFubi40PPPP0+XL19my9Tlnht/tyqTnZ1NAGjLli0UGRlJf/31F2cBJpH+73Do0KFkaWlJ1tbW5O/vT8nJyURkem8M1DTU4KJFi8je3p4UCgVNnTqVE/moqlCDSqWS5HI5DRs2rNahBidNmsRZHGfYjP+uBg4cSEqlkmQyGfXt25cOHDhgUs/27dupZ8+eZGVlRY6OjvTcc8/R1atXSafTsYt4w8PDqWfPniSRSMjT05OzCFur1ZJGo+GECSwrK6O4uDjKzc3ltKVSqdj7ZFgo9MEHH1CrVq3omWeeoRkzZtCsWbM4W0OyePFik+vl7OzMHmcYhhYvXkyurq4kk8koICCA4uLiatXGw1pwaXjenD17tspjlb8PBgzRPY4ePUrOzs4mC4ANCxadnZ3J3t6eAND06dPJycmJXfRcHQ0dfs+4vrNnz1JxcTEnekpt6uMXRvK0BJpctJOmRFxcHBshISEhgQ1Tl52dTTExMTWupzrB0ZBotVqKjIxs8Q8j45jfTPFtkwgfTRVDxJaIw3sIACXFniKm7O4DzwsICKh2AnX27FkCwIn80dxRq9WUk5NDiYmJnFj2+fn5FBMTYxKJhadhqU20k8DAwCo3Q0SbhmLx4sXUtWtXysrKYjfjOPyff/45WVtb065du+jKlSv08ssvk6urK/scrwkPS3wbYmwbTwQNaLVaatu2LQGgrVu3co4ZRxwxxMR2dXXlhOYj0gveyvkpAJCdnR39+++/1fbNEGnFwcGh3uH3zIUONDeWlv67xcNjDC++qyE7O5uys7OJSH+hLl26RBcvXqTIyEh2f03gxXfDY86621TZvXs3HT58mFJSUujwoYPUxbsj9fftXaOJQ1FREbVp08bsD7RGo6GkpCQaO3YsGwu9pZOSkkKRkZF069Ytdh/DMJSfn/9YfO8fFk01zrdx3P/KMAxDLi4u9Pnnn7P7KioqSKlU0rffflvjNpqC5ZuIaPny5WzoyKqszwYBv3XrVrOW6uHDhxMAGj58OP3www/UsWNHAkACgYA++eQTTiIpA7WNMV7fcdY3RjcPT3OkyYUabEo4OzuzfqE2Njbo2rUr2rVrhy5dunD8RXkePgILSwgUbiaLCZsixcXFmD59Ory9vfHGm1PQp08f/PnbJkBq+0A/dWtra6Snp5ukXgf0C5+6d++OrKws/PLLL43V/SZF27Zt0b59ezg6OrL7iouLcePGDcTGxuLGjRu4d+/eAyOn8DQ88fHxOHjwIPbu3ctuhsgaDUlSUhLc3Nzg5eWFV155hU2pnpKSguzsbAwdOpQtK5VKERAQgDNnzjR4P+rLg1Kbnz17Fk5OTsjMzKzSx9mw6PGJJ54w6w99+fJlAPo1Q1OnTkVUVBTeeustEBE+//xz9OnTh7MAvKoFoL6+vggJCWH9y2uDwdfc4KdemYcVPcUctV0AysPzSKiJmm9Jlu/mxuNk+ebhMZCXl0exsbEUGRnJbhcvXqSEhAS6c+eOWeseT/XUxvKdnJxM3bt3J4FAQEKhkAQCAft/oVDYoP06cOAA7dy5ky5fvkxHjhyhgIAAcnZ2ptzcXDZxUGWXpLfeeouGDh1aZZ0VFRVUWFjIbunp6Q/Nsl8T3+rqfJwru3QYlz127JhJkhzjdg2+4EKhkJKSkhrNPaSpWr7NWfirSmDEw9PQ8G4nD6CkpISysrIoLS3NZGtq8OKb53GFYRgqLS2l27dvU1xcHEeIR0ZGUnx8PGVmZraY51JjUxvxHRQURMHBwZSTk0MKhYLi4+MpIiKCnn76aTp5snEXy5eUlJCzszOtWrWqyqydU6dOpWHDhlVZh7lFnA9LfBPVP7V5XRdH5uTk0KuvvkqTJ08mosYTyU3R57shF4Dy8NSF2ojvh5Zkp6mQlZWFjIwMyGQyswk9eHh4mgYCgQCWlpawtLRE69atUVFRgYKCAuTn56O0tJTdDH/Pzs7OHNeVR4Fay0BXTQInkVAIibjpe/udPXsW//77LxwdHSEUCiEUCjFgwACEhYXhgw8+YJOSNQZWVlbo1q0bkpKS2OQr2dnZnBjUOTk51boJzp8/nxNitaioCO7u7o3W58oYJ5MxJB3z9/evccxqQwi+yklyvLy8qg3B5+joiK1bt7IuWga3j/nz52PFihV4+umnodPp2H4ZkkPV1j1EJBJh1apVGDduHMaMGYP58+fDx8cHcXFxCAsLw759+7Bz586HFqO7cojH6hIY8XHDeZoCj534vnPnDjw9Pdl0vTw8PM0DmUwGFxcXuLi4QK1Wo6CgAAUFBSguLkZFRQXHJ1yn06GsrAwKheKhJfJSaxkk3ikGU02MbaFAgI7O1k1egOt0OnZNgoODAzIzM9GpUyd4eHhUG4O9IVCpVLh27Rr8/f3h5eUFFxcXHDlyBD179gSgj99+4sQJrFixoso6pFIppFJpo/bzQRhibNeV+gh4sVj/026YsISHh6Nv377o06cPMjIykJmZySlviL8PgCPOq2uzuglCSEgIVCoVwsPDazXpqCuGpETbt2+vMoGRn58fIiIi6nVPeHgaisdOfAsEArML3Xh4eJoPEokETk5OcHJyglarRWFhIefvuqCgACkpKVAoFLXKOlsfdAwDhgjudpaQmRHXFVoG6Xll/1nGm7b49vHxweXLl9GuXTv07dsXK1euhEQiwffff4927do1aFtz587F6NGj0bZtW+Tk5GDp0qUoKirCpEmTIBAIMHPmTCxfvhwdOnRAhw4dsHz5clhaWmLChAkN2o+mSH0FvL+/P5vRNSMjg12I+eyzzyIsLAxz5sxBVFQUQkJC2EWSc+bMQWpqKluHp6cnVq1aZdbaXnmCkJSUhJ9//hmLFy+u0fkNxaNYAFrTSQoPjzma9i9AI+Ds7IycnJxH3Q0eHp4GQiwWw97enmPp1Ol0EIvFsLa2ZvcxDIObN282euQUmVgIuURsspkT5E2VhQsXstE6li5dilu3bsHf3x8HDhzAV1991aBt3b59G+PHj0enTp0wduxYSCQSnDt3Dh4eHgCAjz76CDNnzsT06dPRu3dvZGRk4PDhw5x7y2MekUiEdevW4fbt25BIJOwb33///Rd9+/bFqVOnsGXLFgQFBeHdd981GxWlW7duGDdunNmslcYCNCkpCSEhIejevXuV5zdWJBKDhT8uLs7sccN+Y9el+lDfzJ48PI/dgkuGYSghIYEuX75MiYmJlJSUxNmaGvyCSx6eusEwDOfvJj8/3yRySk5ODqnV6gZpr0ylodj0fCpTaep0vLGpb5zve/fusdk1mxuPMo75g3gY2R1DQ0PNLkB9+eWXiYgoIiKCAJCvry8dO3aM05eqFlCaW1Qql8tpx44dnLYN5zs5OTVaJJLGWgBq7t7wCzt5qqJJx/mmnGgwZ0NAOTEPu2kAQFpaGoqLiyGVSiEWiyESiTgbT9UEBgZi5syZj7obPDw1QiAQcP6mDT7jMpkMRISioiLcunULsbGxuH79OrKzs6FSqR5hj5sGGo0GAwcORGJiIme/nZ3dQ/Off1x4WBbUDh06AAAOHDiAbdu24fjx47h8+TLWr18PQL8gFQDOnz+PQYMGcfry559/Yv78+UhJSUFERATbb2Mr+YEDBwAATz31FF566SVO/4VCIfr164ecnBy4ubnV2KpeGwwLQPft24cxY8Zw2hgzZgz27duHL7/8EgBqbHmv6t5MmzaNXdjp6+sLhULBLuwMCgrC3Llz+djiPA/koYpvUheDEncCmlJQ0g6QurjR2vr2229hbW3Neb1cUlKCJ554AjNmzEDHjh3h5eUFLy8v3L59G+3atYNGo2m0/jwqwsPDIRAIUFBQ8EjaX7ZsGfz8/GBpaYlWrVqZLXPs2DH4+fnB2toarq6u+Pjjjzn3LSQkBAKBwGSzsrLi1PO///0PPXr0gKWlJVxdXfHGG2/g3r17jTk8nmaETCZDmzZt4OPjg65du6J169bsd6ikpAS3b9/GlStXcPXqVWRmZqKsrAxUzeLJloqFhQXi4uJ4od3IVJX8pjo3DbVaXSe3DYO7ha2tLcaPH4/AwEB069YN9vb2AIAtW7YAAPt9FwgE6NevHxwcHPDCCy+wSY+ysrJMIov4+vqyvy/79+83EaA6nQ7fffcdAGD69OmNJlgNC0ArJyUyJDACUOOJTlX3xs3NDXfv3oWfn1+VCzuNJyk8PFVSE1N6Q7idMAxDuis/k+7UAmJKMkl3agHp4n6uc30P4vr16ybxTQ8cOEDOzs4klUqptPR+CvMlS5aQm5tbo/WlPtTX7cQQ5zU/P7/efQkICKAPP/ywVucsWrSIVq9eTbNnzyalUmlyPDY2liQSCYWGhlJSUhKFh4eTt7c3zZkzhy1TXFxMWVlZnK1Lly40adIktkxERAQJhUJat24d3bx5kyIiIqhr1640ZsyYOo6W53FBpVLRnTt36Pr16yaxxJOTk2tcT0tyO5k9ezZ9/PHHD7uLjUZTczupiZuEOTcNsVhcJ7eN6tpTq9UklUoJAP3f//0fBQYGctqwsLAgS0tLNh64udjhxvsqxw43HDPeZ0xDJ+Sp7CqiUqlYt5t+/frRqVOnqnUVqe5abd26lb3u5n6Ti4qKCABt27atQcbS3KmLS9XDcMNqLJpkkh3mziXSHf+QmDvR/32O+u9zVJ3rfBBubm4UFhbGfv7oo4/ozTffpPbt29PBgwfZ/c8++yy9+uqrxDAMPfHEE/TFF19w6rly5QoJBAK6ceMGEREBoG+//ZZGjRpFcrmcvL296cyZM5SUlEQBAQFkaWlJvr6+bHkiohs3btBzzz1HTk5OZGVlRb1796YjR45w2vHw8KBly5bRG2+8QQqFgtzd3Wnjxo3Vim+GYWjFihXk5eVFMpmMunfvzvrcpaSkmPj4GQtWY3Jzc+mVV16h1q1bk1wuJx8fH5MHSF3Et4FNmzaZFd/z58+n3r17c/bt2bOHZDIZFRUVma0rJiaGAHCSfXzxxRfUrl07TrmvvvqK2rRpU6f+8jyeaDQaunv3LiUlJdHFixfpzp077DGVSkUpKSlUUFBg9lyDuM4rVVGZSmOy5ZWqmo34njFjBtnY2FCvXr3o7bffplmzZnG25kZTE98PSn6zfPlyAkB+fn509uxZ2rp1KwkEAnJ2diYAtHXr1lr7GVeVuKdfv34EgFq1akX9+vWjrVu30q+//kpz585l2wNALi4upNVqadu2bQSA83dgLFgLCgo4AtQgWD08PB66YDXnl+7i4kIzZ86k48ePk1qtNvEHr+7ePOyJRHOmLtlGm3uG0iYnvhlVEekiuJZujiVcZV5k1ZcJEyZwUhD36dOHVq9eTePGjaM333yT4uLiKDo6mmQyGS1ZsoSuXr1Ky5Ytoy5dunDqmTVrFj3zzDPsZwDUunVr+v333ykhIYHGjBlDnp6e9Oyzz9LBgwcpPj6efH19afjw4ew5MTEx9O2337ILPT/99FOSyWR069YttoyHhwfZ2dnR+vXrKSkpicLCwkgoFNKOHTuqFN8LFiwgb29vOnjwICUnJ9OmTZtIKpVSeHg4uzgEACUkJFBWVlaVwuH27dv0xRdfUHR0NCUnJ9NXX31FIpGIzp07x5ZpDPE9e/ZsGjBgAGffwYMHq32AzZgxgzp27MjZd/r0aZJIJLR//35iGIays7PpmWeeoXfeeadO/eXh0Wq1nL+7O3fusJk1jTFYx1QaHV25XUCx6flVblduF5BKw7WmPSxqI74DAwOr3AYOHPiwu15vmpr4NgjY4uJik2NarZY8PDxYkW0sbDUaDUcs1nYxoTlx4+joyPnXsHl4eNBnn31Gffr0IQA0c+ZMIiL6888/CQDZ2trSG2+8QXv27KGSkhJW3A8YMIAA0P79+zni3tgQZkxjCVZDf/z8/AgAhYSEkIuLi4mwM0x0DO0/6N60bduWAND06dM5ltlHldmzKVKXRaktYSFrkxLf1Ylsc6K8Ifn+++/JysqKNBoNFRUVkVgsptjYWNqwYQP17t2bMjIyWHF65swZysjIoMzMTBKJRHT+/Hki0r+Sc3R0pM2bN7P1AqCFCxeyn8+ePUsA6KeffmL3bd++nWQyWbX969KlC3399dfsZw8PD3rttdfYzwzDkJOTE33yySdm/5hLSkpIJpPRmTNnOPunTJlC48ePJ6L6uZ2MHDmS4/7RGOL70KFDJBQKadu2baTVaun27dvsw9ucJaSiooJsbW1pxYoVJsd27NhBCoWCfTX73HPPNVgkCx6ekpISSk1NpZycHHafRqOhS5cuUWJiIuXk5FBpeYVZq7dhe1TCm6h24vvWrVsmr9yJ9M8kY4NBc6Gpie/aWFcrl60sVmsrXiu/1l+8eDEBoAEDBlBYWBgrLg1bq1atOPWHh4eTUCjklJHJZBQUFERvvvkm68JiLHCdnJweaip64wmLwfIOgEaPHk2nT5+mESNGkJubGwUFBbHHDL831d2bXbt2sdfDsLVt25aWL1/erERiY1KXyDONFa3mYdO0xHdxJumOf0i6mwfMHtfdPKB3PynJrHXdDyIpKYkV1vv372ct2llZWWRhYUElJSUUGhpKbdu25Zz33HPPsRbT3bt3k7W1NcdHHAD98ccf7OebN28SALpw4QK7799//+XchJKSEpo3bx517tyZlEolWVlZkVAopHnz5rHneHh40MqVKzl96d69O7399ttmv3QXLlwgAGRlZcXZLCws6OmnnyaimotvrVZLS5cupW7dupGdnR1ZWVmRWCymF198kS3TGOKbiGjVqlVkY2NDIpGILC0tKSwsjADQ77//blJ227ZtJBaLKSsri7P/6tWr5OrqSitXrqTY2Fg6ePAgdevWjd5888069ZeHpybk5eWZ+Ilfu3aNsrOzqaKi4lF3j0NtxLdQKOS43BjIzc0loVDY6H1taJqa+K6JX7HBTaOyJbaym0Z93DYM/ZDJZOTr60tbt26lo0eP0tGjR2nr1q3Ur18/srS0NPFx/v333wkAeXl5kaurq4l7Y2hoKG3bto3++usv1shlzuWlsQSrsYA+evQoO7kwXGvDhOXYsWPUv39/AkBHjx7lXJPK98ZgqHN2diZbW1uTSYqTk9NjL7yJHuxSZW6yWJdzmiK1ec40foZLKxfAoQeQeQrUegAEkvuJEUhdDGScAhx7QGDVMMHvjWnfvj3atGmD48ePIz8/HwEBAQAAFxcXeHl54fTp0zh+/DieffZZznlTp07FxIkTsWbNGmzatAkvv/wyLC0tOWUsLCzY/xuiApjbZ0hUMW/ePBw6dAhffvkl2rdvD7lcjnHjxkGtVldZr6EeqiLigqHu/fv3o3Xr1pxjtU2tvGrVKqxZswZr165Ft27dYGVlhZkzZ5r0rzGYPXs2Zs2ahaysLNja2iI1NRXz58+Hl5eXSdkff/wRQUFBcHFx4ewPCwtD//79MW/ePABA9+7dYWVlBX9/fyxdurTBkivw8BjTqlUrdO3aFfn5+SgoKEBZWRlKSkpQUlKC9PR0NsqPra0tZDJZs4kgUtUzp6SkBDKZ7CH3puVhCI03btw4jBkzBvPnz4ePjw/i4uLY8H/Tpk2DSCTiJJDx9fU1SRhTnwQyhrTstra2OHfuHM6dOwcA8PDwwDvvvAOhUIiysjK88cYbnLCdL730EsRiMebMmcPJGimTybB27Vq88847AIC1a9ciODgY3t7eGDRoEM6ePctJRe/i4oLff/+9wbNfGme8PH/+vMlx44yXlf8mzd2bzp0747333mOT9O3cuZPN7JmRkYH169cjKysLwcHBDTqO5khts43qdDocO3YMAJCfnw+dTmcS9rkxMpQ+ahpdfAsEAqDjONCFz0FJOyHo+gYA/cOdEncCAkDQYVyjtT9w4ECEh4cjPz+fFWYAEBAQgEOHDuHcuXN44403OOeMHDkSVlZW2LhxI/755x+cPHmy3v2IiIjA5MmT8fzzzwPQ/4gZp/CtC126dIFUKkVaWho7saiMRCIBgAeGcYqIiEBwcDBee+01AHphn5SUhM6dO9erjzVFIBDAzc0NALB9+3a4u7ujV69enDIpKSk4fvw49u7da3J+WVkZxGLu19nwB1yVkODhqS8CgQByuRxyuRxubm5QqVQoKChAfn4+SkpKUFZWhrKyMmRmZkIqlcLW1ha2trYmYTKbCgsWLIBEIoFAIMCiRYs4RgedTofz58/jySeffHQdbEEYQuPNmTOHI0g9PT3h5OSEM2fOgGEY+Pv7w9PTE8uXL8fu3bsRFhYGLy8v+Pv7g2EYzufa8tdffwEAfH198cwzz2Djxo1IS0vDrVu3sGDBAjg6OgK4Hye8cv+NU8ubS7FuCFF4/fp1XL9+3aSO7OxsfPTRRxCJRA8U4LVJ5248MTFktD59+jQrpktLSwEA69evZyccxpmvq7o3bm5u2LlzJ9vXwMBAAEC7du3g5+eHiIgIdt/jSuXJYmWMJ4u7d+/GnDlzWC00cuRIeHp6YtWqVZzvQ0NnKG0S1MSU3lyjnRAR/fzzzySXy0ksFlN2dja7f+vWrWRtbU0AKC0tzeS8BQsWkEQiIW9vb5NjAGjPnj3sZ0NUkejoaHZfZXePMWPG0JNPPknR0dEUExNDo0ePJmtra44bh4eHB61Zs4bTVo8ePeitt96q0tfp008/JXt7e9q8eTPduHGDoqKi6JtvvmF91G/fvk0CgYA2b95MOTk5ZheREBHNnDmT3N3d6fTp0xQfH09Tp04lGxsbCg4OZsvUxe3k1q1bFB0dTaGhoaRQKCg6Opqio6M5/Vi5ciVdvnyZ4uLiaMmSJWRhYcG5vgYWLlxIbm5uZq/Fpk2bSCwW04YNGyg5OZlOnTpFvXv3Zt1veHgeNmq1mnJycigxMZEuXrzIuqVUzqT7sLJG1sTtxN/fnwIDA9mFasYLLYcOHUpvv/02JSYmPpT+NiRNze3EmAdlUTxz5ozZaCcGtw0YuXnUJjSbVqtlFyCePn2a05etW7eSn58f61Ji/Lq/tqHgcnNzae/evTRmzBgCwPqLW1hY0IkTJ1jXk1deeYVee+01Wrt2LZ06dYpKSkrYOmobBcPYdeTYsWPsgk9zdVRecGnu3syYMYMAVBmwgA8xeJ+a+m//8ccf7Hf81KlT5OHhQQMGDKCgoCCOKxLv893M4nwbMAjjyiI6PT2dANATTzxh9rzk5GQCYOKDTVQ38Z2SkkIDBw4kuVxO7u7u9M0335iI2bqIb4ZhaN26ddSpUyeysLAgR0dHGjZsGJ04cYIts2TJEnJxcSGBQFBlqMF79+5RcHAwKRQKcnJyooULF9Lrr79erfhevHgxeXh4mK3PwKRJk0z8ASs/6AYOHEhKpZJkMhn17duXDhwwXR+g0+moTZs2tGDBgirb+uqrr6hLly4kl8vJ1dWVXn31Vbp9+3a1/ePheRhotVq6d+8eJScn071799j95eXlFBUVRSkpKY0uwmvj8z158uQmKVTrSlMW31VhTnBWjvPt5ORETk5ONRalxhh+o1xcXMwKpVOnTrHHDb8/dQ0FZyzISktLKSIigg1QYBBXEomEU69QKKROnTqx0VKCgoJqFQXDMIEJCgoiFxcXGjFiBJ08eZL8/PxIIBBQaGio2VCD5vq+Zs0aAkDr1683W665+CQ/LB7k479jxw4TgW58v/r3708eHh4UERHRrBayNknxTWQU3eTE3EYNMVgdVcWOrsypU6dMrOXm6kpMTKSYmBiKjIykvLw8znGGYSgjI4NiYmLo4sWLdP36dSorK6tVf+ubZKcxmTRpUpVinoeH58FkZ2dTZGQkJSQkcPbn5+eTRtOw8cBrI75bGs11fFqtlo4ePUoLFy6khQsX0uHDh+no0aO0bds2Cg0NrVdoNsPCzunTp7Pi1lgojRgxgoD7IQbrEwqupgvqhg8fTr169SI7OzuOELe0tORMDqZNm0Zvvvkmde7cmZycnOjq1atmI1uZmyy4ublx3hwYxKA5a35NJhvVWWabc8KY+mLu2nl5edGuXbuq/D5Ud05zoMmKb6L/3E3OLGbdTx42Fy9epMuXL1NGRgapVCqT4xUVFZSUlEQDBw6kCRMmVFtXQUEB3b59m414UFl8Z2Zm0qVLlygvL4/Kysroxo0bFBMTU6s/wKYsvj09Pc267PDw8NQMhmGoqKiI44alUqlYF5Xr169Tdna22WdVbeHFd/MbX1Xi748//qhXaLZdu3aZxLyubFU3djmpbyi46mJnExEnHKBha9OmDU2cOJEAcBI7MQxDSqXSpLxYLKaOHTvS22+/zak7Ozubjh07RjNnzuQkDTIIu3nz5pm9xvPmzeNMNgx9dHZ2JoFAYCLgKwvE5p4wpiGoavLxoFjqf//9NwGgMWPG0NGjR5uk/jFHkxbfjxqNRkPZ2dl09epV1uJ079499oGyadMmEgqF1KtXr1q5LFQW3wzDUExMDGVm3g+hqNPpKCoqihMn+EE0ZfHNw8PT8JSUlFBcXJxJCMOrV69SZmZmrd+eGeDFd/MaX3WWZoOYO3XqlFlxU50bhDl3jNOnT7O+tjNnzqRjx45RUFAQK6jrGwruQbGzBQIB69ZhbpzGIk2r1dIff/xBixcvphdeeIEAcGKLDxs2jFO/g4MDyWQy8vb2puHDh1NwcDBNmDCBQkNDafXq1WavsSH+d+/evU3CDRoSIFVnmW0JCWMakwd9H8wlQ2oO14wX3zWktLSUXRAYHR1Nt27d4sTzrg2VxXdFRQVFRkaa1JeUlEQ3b96ssh6dTsdm1tNqtawVjBffPDyPFxUVFZSVlUXXrl0zEeJXrlyh9PR0KikpqbGvOC++m8/4HmRp7tWrFwEwiTVtEClVLQCsXK+xSDx16hQn+YyxSHyQ5fpBCw6rGo9hv7Ozs0kscZ1Ox/p7nzp1ymy9xvG609LS6MiRI3Ty5En2eElJiUlCIONNJpNx+vTKK6/Q9OnTacqUKQSA7O3t6dq1a5zfca1WS9988w0BoDVr1ph1NWkJCWMak5rEUndzc6OCgoJmNWnhxXctUKlUlJGRQRcvXqRLly6xSTJqa12qLL6Li4spMjLS5HVxSkqKiX+nMRkZGSY/tLz45uF5vDFETklISOBETomMjKTY2NgavaXjxXfzGd+DLM1Tp04lANStWzezltWqIniYq9ece4SLiwtH6DREEhRzi/AMIhaAWWEVERFBAMjPz6/OQlatVlNycjIdPXqUvv/+e5o/fz698sor1LlzZ86YSkpKqhTpAGjcuHFsnYbJxltvvUV///03RUZGUlpaGqlUqhaTMKaxqfx9yM/PJxcXF9atp6Z+9Y1JbX32efH9AHQ6Hd27d48NARYfH085OTmspTk5OZmuXLlSqzobSnzzlm8eHp7q0Gg0lJubSzdu3GANBsZv0xiGoYKCAhOxwovv5jO+B/nEGizePXv25NxnnU5HQUFBZGlpaVaoVFWvQWT89NNPBOhDGVY+3hDWXHNC31x7BgwitzGyY1a+FqWlpfTtt9/SwoULafjw4WzfDC4tb731FnuuIXyhuc3KysrkGq9cuZJ+/vlnOnDgADuhMIQDfpypakGsuXv6sCctdfHZ58V3NVR2MzFn4Ta4jNSGhnI7qQzv883Dw1MVOp2O8vPzOTGRS0pKKDIykqKjozkuKbz4bj7jq856ajhWlSgdMGAAAfrY37Wpl6hmvuL1FcHG1kRDCL8H9Sc0NLTBo2BUdy2MY6AfPXqU8vPz2bVaOp2Ohg0bRgqFgkaMGEFPPfUUtW7d2mTBqqHe4uLiKoW6XC6nKVOmsO0yDEPvv/8+LV68mL7++mvatm0bHT58mKKiourlFtuUaYqx1Ovqs8+L72q4fv065ebmmszejTFEIKgNVS24zMrKYvfxCy55eHgam4KCAoqNjTVJ5nPjxg06f/483bp1y+Sc5iZOa0tzGJ+xKD169Ch5eHiYtTQbom54eHiw8ZIrW+eqEin1tWA3dCi42vSnocP2Pajt3r17E2AagrEqAabT6Sg3N5cuX75MrVu3ZuvNy8ujSZMm0fDhw+nJJ5/kLA4FwBHfBoFZ1fb888+zZRmGof79+9OIESNowoQJNH36dFqwYAF98cUX9MMPP3ByfRAR5eXlNWkd0VTcderzN8KL7yrQ6XR08+ZNqqioaJD6tFotlZaWUmlpKUVGRlJ2djaVlpay9WdmZlJUVBQbajA5OblFhRrk4eFpmjAMw4kTXl5eTpGRkfTPP/+Qp6cn9evXj1auXMkK9OYgTutDUx+fOVFrSJ5T2dJsWIQYFhZGRKZ+qQa3hqpESn0t2A0tghvKol6Xvj6obXNhCGsy2XhQvTt37qTCwkJKTk6m9PR09ryioiL69NNP6Z133qFx48ZRYGAg+fj4kKurK1lYWNDUqVM5ZasT6mPHjmXLMgxDIpGIAJCNjQ25u7tTly5dyNfXl4YOHUpLlizh9H/t2rW0ceNG2rp1K+3du5eOHz9Oly5dosTERMrNza3z/aiOprJQtT6TgNo8Z8R4jBAKhSgoKICbm1uD1FdWVoaEhAT2c3p6OgDA3t4eXl5ecHFxAREhLS0NWq0WVlZW6NixI0QiUYO0z8PDw2MOgUAAsfj+410kEsHJyQlFRUUgIpw9exZnz57FRx99BB8fH4wcOfIR9vbxZvfu3Rg3bhyCgoKwfft2+Pj4IC4uDsuXL8e+fftw7tw5+Pn5seU9PT3h5OSEM2fOgGEYiEQiBAYGAgAYhsGYMWPg5eUFf39/AIBOp0NERASysrLg6uqK4OBg7Ny5E3PmzOHU6+XlhZ07d2Ls2LEPdfxjx46tsj8hISFQqVQIDw+Hv79/rX47d+/ejTlz5iA1NZXd5+npiVWrVrFjrK5tw7UICwvjXL+a9KMm9QKAjY0N5zxra2ssXbrUbJ1EBI1GA0B/T8+ePYtZs2bBwsICTk5OKCwsREFBAQoKCpCfn48+ffqw55aWlkKn0wEAioqKUFRUxKlboVBw2pkzZw5bvjKDBg3C0aNH2c89evRg9Y2VlRUUCgX7/65du2L27Nls2W3btoGI2OPGm1KpxKpVqzBu3DiMGTMG8+fPZ/8WwsLCsG/fPuzcubPR9VNWVhYAwMfHx+xxw35DuTpTk5lAg1q+1eVEcTv0/z4Cbt68yXEFaSzMpZyvC03d8l055TwPD0/TxPAcT01NpfXr19PgwYNN/FSbqmW4vjRVy3dNrX2GrJYG621NrcXVLRqriwW7MRPHGPcnNDTUJJ52bdqpymfXEL975syZnDE3VibKxqjX3D1wcHCg0NDQB0Z9ycnJocTERLpw4QIdO3aM/vzzT/rll1/o6NGjnHKTJk2i559/ngYPHkxPP/00eXt7U+vWrcna2rpKi7q57dlnn+X0oXLmUuOtd+/eVY5PIpGQt7c3DR48mEaPHk1z5szh1Lt27Vr67LPP6Msvv6T169fTzz//TNu3b6c///yTE3qSiOj27duUkZFBeXl5VF5ebhKq9WFZvh+e+GYYovjdRGu8iBaDaG07/ecaxqitC3fu3KG3336b3N3dSSKRkLOzMwUEBNCmTZvoxo0blJmZSQBo06ZNlJ2dXW0q+drSlMW34cuVn59f77rqIr6XLl1K/fr1I7lcTkql0myZo0ePUr9+/UihUJCLiwt99NFHJum2Dx48SH379iWFQkEODg40duxYk8WsFRUVtGDBAmrbti1JJBJq164d/fTTT7XqLw9PS8DcczwvL49++eUXevvtt5ukOG0omqr4ru8CyOpcIho60cuDkv6EhoY2iMisb7+riyHd3DNOGl+bsLAwkwmKk5NTo4/HWKwyDEPnz5+nY8eO0d69e2n79u30448/0tq1a2nZsmX0yy+/cM598cUXadCgQeTr60vdunWjdu3akbOzMykUCho4cCBbTqvVms1iath69erFqdfLy6vKsh07duSU7datm0kZmUxGtra21KtXL87356233qLAwEA2OdOLL75Ibdq0IWtra5o7dy6n3t9//51WrFjRxMR3VizRpoF60f3rCKLEf4h+Ha7/vPlZouzLdav3AQwYMID69u1L//77L6WmptL58+fpgw8+oK+//ppiY2MpNjaWAH2g/NjYWLp8ueH6wYvvqlm0aBGtXr2aZs+ebVZ8x8bGkkQiodDQUEpKSqLw8HDy9vbmzHaTk5NJKpXS/Pnz2ZBrzzzzDD355JOcup577jnq27cvHTlyhFJSUuj8+fN0+vTpugyVh6dZw0c7aXrja4jkNeYsqw3tP1tdfTt27CC5XN4gorYh+m08oTFcn5kzZ7JZPb///nsC9Nk0m0vyFiLutdmxYwdnglJYWEgDBgwgS0vLZjOeylS2QMfHx9P58+fp33//pf3799POnTvp119/pe+//57++OMPTtmFCxfS1KlT6ZlnniFLS0vOd9HS0pJzPXr06FGltb5du3ZEdH+SY2NjU6Wod3Jy4vTB39+/Vm8QH4743tiTaLUHUcJ+7v6E/fr9G3vWrd5qyM/PJwAUHh5eZZnKs0YPDw/22N69e6lXr14klUrJy8uLQkJCOJZXALRhwwYaPnw4yWQy8vT05HwhDOJ7165dFBgYSHK5nLp3705nzpzh9OH06dPk7+9PMpmM2rRpQ++//z4nbJiHhwdNnz6dJk+eTAqFgtzd3em7776rduwMw9CKFSvIy8uLZDIZde/enXbs2MHpl/E2adIks/Xk5ubSK6+8Qq1btya5XE4+Pj4mPwL1cTvZtGmTWfE9f/589hWUgT179pBMJmOj0OzYsYPEYjHnAb13714SCASkVquJiOiff/4hpVJJ9+7dq1P/eHhaErz4bnrja6wID3Wpt7KQNySMqS4koEGkGMIbfv755xQaGsouCq2tCGyI62GY0GzdutXE0t22bVtasGABAaCFCxeSWq2u00SkMVxUHoTh2pw6dcrsBMVwbfz8/B7LDJqG76Kfnx+FhobSgQMH6NSpU1VOsNRqNRUWFlJ2djalpKRQfHw8Xb16lVOfs7Mz5/tjZ2dHEydOpM8++4y++OILTn2hoaE0duzYJii+/37X/LG/320U8a3RaEihUNDMmTOrjG6Sk5PDup1kZWWxIQAPHjxINjY2tHnzZkpOTqbDhw+Tp6cnhYSEsOcC+tSzP/zwAyUkJNDChQtJJBJRfHw8Ed0Xud7e3rRv3z5KSEigcePGkYeHByviL1++TAqFgtasWUOJiYl0+vRp6tmzJ02ePJltx8PDg5RKJX399deUlJREYWFhJBQK6dq1a1WOfcGCBeTt7U0HDx6k5ORk2rRpE0mlUgoPD2f9BQFQQkICZWVlVRlX8/bt2/TFF19QdHQ0JScn01dffUUikYjOnTvHlmkM8T179mwaMGAAZ9/Bgwc5D92UlBSSSqX0448/klarpYKCAnrxxRdp2LBh7DnvvvsuDRo0iD7++GNyc3OjDh060Jw5c2qdvZSHpyXAi++mN77GivBQW4u6OZeMyusBDILWXN9/+eUXk7JyuZycnJxq1ff6vgkgui9SDZbh9evXEwB69913zVroq8oGao5H6bpiuDYHDhwwO0ExXJuQkJBaTdge1WSiIdFqteTk5GT2/u7YsaPOf0fNP8PlIxDfREQ7d+4kW1tbkkql9NRTT9F7771Hhw4dorS0NHYDQD/88AP7mUj/+mD58uWcun799VdydXVlPwOgadOmccr07duX3n1XP06D+P7xxx/Z41evXiUArHCeOHEivf3225w6IiIiSCgUstfaw8ODRowYwd50hmHIycmJNm7caHbMJSUlJJPJTCzsU6ZMofHjxxNR/dxORo4cyXH/aAzxfejQIRIKhbRt2zbSarV0+/Zt1rJi/NA9ceIEOTk5sa+Q+vXrxxnTsGHDSCqV0qhRo+j8+fO0f/9+8vDwoDfeeKNO/eXhac7w4rtpjq8xQu3VxoJc2cd669atJBAIWKvf1q1bWQFrbM02tBEWFkYCgYB15TD4aFeX7Kch+l0VKpWKxGIxOTs7k0ajYUWrwe3E1taWRCIRnTx5kr3GDxL0RA3vQ19bDNcmNDTU7ATFcG32799fo/EQtQw/eCJir8mAAQPM3pvaTLDqAy++jSgvL6effvqJ3n//fXryySdJJBLR4sWL6dKlS6wY/vLLLykqKoquX79ORESWlpYkk8nIysqK3WQyGQFgM0wBoC1btnDamjlzJgUGBhLRffF94cIF9nheXh4BYIPfd+nShSQSCacdg7+SwYLu4eFBH3zwAWfG1b179yofaBcuXCAAnDqtrKzIwsKCnn76aSKqufjWarW0dOlS6tatG9nZ2ZGVlRWJxWJ68cUX2TKNIb6JiFatWkU2NjYkEonI0tKSwsLCCAD9/vvvRESUlZVFHTp0oHnz5lFUVBSdOHGCAgICaNCgQazv2JAhQ0gmk3Es+4YHKG/95nnc4MV30x3fo0peo1KpOOWMz9NoNCblnJ2dydPTk7RaLStq27Zty9lvoKCggACQo6Njg/iW19bnG9DHSP/qq68IAHXv3p2NdmIQYjqdjp0kGEf8aIx+1RdDHwwuPcYTFOM+PCjOu4FHPZloKLRaLTk4OJj9+zZcF4OLcWNnx2ya4ruyzzfDECXsazSf76qYMGECubm5sa4fAGjnzp2UlJTEhiCUyWS0YsUKSkpKMtkMf3hViW/Dil1zCy4NfuiGPwpvb296//33zbajUqmISC++Z82axfmj7tGjBy1evNjs+M6dO0eA3te9cp0Gy35NxfeKFSvI3t6efv31V4qJiaGkpCQaNWoUBQcHs2UaS3wT6a38GRkZVFZWRvHx8ZzJzMKFC+mpp57ilE9PT+c8lF5//XV64oknOGUM9SQmJtapzzw8zRVefDft8T2K5DWVLc2VP5uzkAOgb775hrWwVraIGzCca/Cvrk04w/q8CajO59vT05PNELpt2zbS6XTUv3//B4rvB1nkDYK3NuOsCwaXUblcTgMGDKCCggLOtampi0VTmEw0FMaTrfXr15v8/Rh/Dx8/y3f2ZX1Uk8XQRzl5SNFOzDFnzhyyt7dnP1tYWNDOnTuprKyMYmJiiIjIz8+P3nzzzWrrMfiQGePr62vidlKd+J4wYYJJHMzK1FZ8FxUVkVQqNQnxY8zp06cJwAMzVQUFBXGug06no44dOz408W3M//3f/5G7uzt7HWbPns1a8g0YQkcaopl89913JJfLOa/n/vzzTxIKhbzlm+exgxffLXd8VfEgi3plH+vKnyv7WBuEa+Wt8u+NIT175agSNXVpqM+bgMrRThYuXMjWERQURN999x07gTCESXyQVbQ6X/Rdu3bVKyZ5bdm1axc5Ojpy2vPw8KDly5fXeIJSX/eepuQnbrg3VX3X6vIGpq40PfFN9F+c7z36+N5snO89jRbnOzc3lwYOHEi//vorxcbG0s2bN+mPP/4ge3t7eu2119hyHTp0oHfffZcSExPZL9rBgwdJLBbT4sWLKS4ujuLj4+m3336jTz/9lD0P0Ae2/+mnnyghIYEWLVpEQqGQXS1bE/EdGxtLcrmcpk+fTtHR0ZSYmEh//fUXzZgxgz2ntuKbiOjTTz8le3t72rx5M924cYOioqLom2++oc2bNxORfiGlQCCgzZs3U05OTpWLW2bOnEnu7u50+vRpio+Pp6lTp5KNjU29xfetW7coOjqaQkNDSaFQUHR0NEVHR3P6sXLlSrp8+TLFxcXRkiVLyMLCgvbs2cMeP3bsGAkEAgoNDaXExES6dOkSDRs2jDw8PFhhXVxcTG3atKFx48bR1atX6cSJE9ShQwdOil4enscFXny33PFVR3VCqTaWb+PPa9asoW3bttHkyZNZX1tjK3Xv3r0J0AccAPSLBGvr0lBXgVfZqmvsm15Z0Lu4uNDLL79cZ8t35WgvdRlnXdBqtRQaGmoiwms6QanPwtam5idu8Pf29vYmgUBAI0aMoO+//55GjBjB+Q7WZu1BXWma4tuAupzo6k4ijfkIJA1FRUUFffLJJ9SrVy9SKpVkaWlJnTp1ohkzZtD58+fp3r17pFKpaNeuXdSuXTsSi8XUunVr9vyDBw+Sn58fyeVysrGxoaeffpq+//579rjhFceQIUNIKpWSh4cHbd++nT1eE/FNpPfRHjJkCCkUCrKysqLu3bvTsmXL2ON1Ed8Mw9C6deuoU6dOZGFhQY6OjjRs2DDW15yIaMmSJeTi4kICgaDKUIP37t2j4OBgUigU5OTkRAsXLqTXX3+9WvG9ePFiTshGc0yaNMms9cT4ugwcOJCUSiXJZDLq27cvHThwwKSe7du3U8+ePcnKyoocHR3pueeeM4kCc+3aNRo8eDDJ5XJq06YNzZ49m7d68zyW8OK75Y6vrlQWqsafKyoqqF+/fuTo6EhHjx41G5avqigTYrGYnnrqKQoKCuKUf1guDcauKxEREeTh4UEDBgygUaNGsRZwFxeXGgtIc24ahn1BQUGPbJx1naDU1fLd1PzEtVoteXh4kFwup6CgINqxY4fJxEAgENQ66k5dadri+xGj1WopNTWVLl68SJGRkRQZGUkXL16k1NTUWt0cABxLbGPR1NPLV2bSpElVinkeHp5HBy++W+746kNlH2uDa4lQKOSIGIPAriywjOMrh4SEsIvjBwwYYFaQ1TV2eV3GVVmIWVpa0oQJE1hrtcFiHRYW9kABWfk6GXzeH+Y4G8rdoy4+303RT7xyxB3DZGv//v0UEhJCPj4+D83qTcSL7xqh1WqptLSUSktL6/Rl4cW3eTw9PdmFnTw8PE0HXny33PHVF3NC1bC40li4ViVOzZ3v4eFhtmxNYnVXR20EqHHZ0NBQE99sYzeNmgjIhznOmrRdH3eP2i5sbYgwkA2NsftMVdenIe/Bg+DF90OAF988PDzNCV58t9zxNQRarZaOHj1Kjo6O5OfnR+Xl5RyR+6BskAaha1jgeOrUKbPt1Eek1VeAHj16lLWEmhPuNenbwxhnZRrL3aM2C1sbIgFSQ1N5QlB5YlbTsIsNBS++WxC8+Obh4WkIePHdcsfXUDSEdbOx3BMaQoA2pIB8WG4Yjd1OTd8kNEXLd1NzheHFdwuCF988PDwNAS++W+74GoqGEqcNnbWzoURWQwvIxshO2th9ritNTegaeBj3oKbw4rsFwYtvHh6ehoAX3y13fA1FQwq9hsza2VD9agwB2dDZSSvTlNw9mpLQrdyvxrwHNaU2zxkxeHh4eHh4eB57/P394enpieXLl+PPP/+EUChkjzEMg7CwMHh5ecHf3/+BdY0dOxbBwcGIiIhAVlYWXF1d4e/vD5FIVOt+ZWVlAQB8fHzMHjfsN5SrCpFIhFWrVmHcuHEYM2YM5s+fDx8fH8TFxSEsLAz79u3Dzp07a9XHhhynOVxdXQEAcXFx8PX1NTkeFxfHKdeYjB07Fjt37sScOXPg5+fH7vfy8sLOnTsxduzYRu9DVf1qzHvQGPDim4eHh4eHh6fBxalIJEJgYGC9+9WQArQxBGRDjdMcDTkhagiaqtBtzHvQGAiIiB5UqKKiAikpKfDy8oJMJnsY/eL5D51Oh+joaPTs2fORf7l5eHiaL9U9x4uKiqBUKlFYWAgbG5tH1MPGo6WPr6HZvXs35syZg9TUVHafl5cXvvzyy0di3dTpdGjfvj26detmVoCOGTMGcXFxSEpKqvHvpE6na3ICsip2796NcePGISgoqMoJ0aOyOvPcpzbPGd7yzcPDw8PDw8PS1KybjeEu0pwspU3V3YOn7rR48R0YGIgnn3wSa9euhaenJ2bOnImZM2c+6m7x8PDw8PA0WZqaOH3cBWhTmxDx1I8WL76NiYyMhJWV1aPuBg8PDw8PD08tedwFaFObEPHUncdKfDs6Oj7qLvDw8PDwPCqys4HS0kfdC556IAIQ2KkT0KmTfkdOziPtDw8PS3FxjYvWW3yX1uFBJpVKIRbrm9ZqtVCpVBAKhZDL5dXWW1+rdWW3E4FAgB9++AH79+/HoUOH0Lp1a6xatQrPPfdcvdrh4eHh4WmChIQAEsmj7gUPD09LRK2ucdF6i2+FQlHrc/744w+8+OKLAIA9e/bgpZdeQkBAAMLDw9kynp6eyM3N5ZxXg8AstSY0NBQrV67EF198ga+//hqvvvoqbt26BTs7uwZvi4eHh4fnESKXA61aPepe8PDwtERUqhoXfazcTswxefJkjB8/HgCwfPlyfP3117hw4QKGDx/+iHvGw8PDw9OgyOWAtfWj7gUPD09LpBZv1eotvktKSmp9jlQqZf///PPPo6SkhBO3EwAnvmhj0r17d/b/VlZWsLa2Rg7vQ8bDw8PDw8PDw9MI1Ft819cPWywWs/7fDVlvTbGwsOB8FggEYBjmobTNw8PDw8PDw8PzePHYu508LHJycpCdnQ2NRgO5XA53d3dY868/eXh4eGrMhg0b8MUXXyArKwtdu3bF2rVra5dWu7y8VhEJeHh4eGoM7/PdtMjLy0N6ejratm0LhUKBu3fvIikpCV27duW44PDw8PDwmOf333/HzJkzsWHDBvTv3x/fffcdRowYgfj4eLRt27ZmlZSXAzpd43aUh4fn8eRhRjvheTB37tyBg4MDG2e8bdu2KCoqwt27d9GmTZtH3DseHh6eps/q1asxZcoUTJ06FQCwdu1aHDp0CBs3bkRYWFjNKgkJ4Rdc8vDwNA7FxcD27TUq2uLFt3H4wsqLOM2FLiwoKGjQ9hmGQWlpKVxcXDj7bWxszC5WZRiG0y/df1YaHW+t4eHhqQc6nQ4Mw6CkpATqShaaoqIiAI0TzrUhUKvVuHTpEj755BPO/qFDh+LMmTMm5VUqFVRGr4ALCwsBAEWWlgCf5ZiHh6cRKPpPp9XkOdrixfejRqvVAjBd2GlhYcEeMyY7OxuZmZkm+y9fvtw4HeTh4XlsyM3NxahRo3Dr1i2zx+/duwelUvmQe/VgcnNzodPp4OzszNnv7OyM7Oxsk/JhYWEIDQ012e/u7t5ofeTh4eEBgOLi4gc+R3nx3cRwcXHh/MBotVpcuXIF3bt3h0gkeoQ9azx0Oh0uX77cYsfIj6/50xLGWFFRgdTUVFy8eBGSSvFoCwsL0bZt2yafXEwgEHA+E5HJPgCYP38+Zs+ezX4uKCiAh4cH0tLSmuTkorYUFRXB3d0d6enpsLGxedTdqRf8WJomLWkswMMZDxGhuLgYbm5uDyzLi+9GxhBGUaPRcPZrNBqzIRYrxzs3IBKJmu2Pfk1p6WPkx9f8ac5jFIlEEAqFUCgUkMlkZstU9fx51Dg4OEAkEplYuXNyckys4YA+l4S5xexKpbJFCAkDNjY2LWY8/FiaJi1pLEDjj6emk/um+aRtQQiFQlhZWbE+lQaKioqgUCgeUa94eHh4mg8SiQRPPfUUjhw5wtl/5MgR+Pn5PaJe8fDw8NQN3vL9EHB2dkZKSgqsrKxgZWWF3NxcqNVqNvoJDw8PD0/1zJ49GxMnTkTv3r3Rr18/fP/990hLS8O0adMeddd4eHh4agUvvh8CdnZ20Gq1yMzMZJPsdOjQoUYxvgUCAdzc3Mz6NbYUWvoY+fE1f1r6GKVSKRYvXtyk8w68/PLLuHfvHpYsWYKsrCz4+PjgwIED8PDweOC5zWF8taEljYcfS9OkJY0FaHrjEVANYqJUVFQgJSUFXl5eVfoK8vDw8PA0XfjnOA8PD0/TgPf55uHh4eHh4eHh4XlI8OK7BZCamgqBQICYmJganxMYGIiZM2c2Wp94eHh4eHh4eHhM4cV3HRAIBPjzzz8fdTd4eHh4eHh4eHiaGbz4rgWVUzLz8PDw8PDw8PDw1IYWLb537tyJbt26QS6Xw97eHoMHD0ZpaSkAfca62bNno1WrVrC3t8dHH32ESZMmYcyYMez5gYGBmDFjBmbPng0HBwcMGTIEnp6eAIDnn38eAoGA/VwZgyvIH3/8AX9/f8jlcvTp0weJiYmIjIxE7969oVAoMHz4cNy9e5c9j2EYLFmyBG3atIFUKoWPjw82btyIS5cuIT4+HsXFxbhw4QJ69uwJmUyG3r17Izo62qT9+Ph4jBw5EgqFAs7Ozpg4cSJyc3Mb7No+iOLiYiQlJSE2NhYXL15Efn4+5zgRITMzE7Gxsbh06RISEhJQXl7OKcMwDNLS0hATE4OoqCgkJSU1mQlQVlYW4uPjERUVhZiYGNy4cQMVFRWcMs15jDk5Obh69SqioqIQFRWFa9euobCwkD3enMdmjqysLFy8eBFpaWnsvuY+xszMTFy8eJHd4uLikJ6ezh4nIoSEhMDNzQ1yuRyBgYG4evUqpw6VSoX3338fDg4OsLKywnPPPYfbt28/7KHUmw0bNrALTZ966ilEREQ86i6ZcPLkSYwePZqNqlP57Wpzul9hYWHo06cPrK2t4eTkhDFjxiAhIYFTprmMZ+PGjejevTubnKVfv374559/mt04zBEWFgaBQMBxQW0u4wkJCYFAIOBsLi4uzWccVAPKy8spPj6eysvLTY6pVCpSqVTEMAy7T6vVkkqlIo1G06Bla0NmZiaJxWJavXo1paSk0OXLl2n9+vVUXFxMREQrVqwgpVJJO3fupPj4eJoyZQpZW1tTcHAwW0dAQAApFAqaN28eXb9+na5du0Y5OTkEgDZt2kRZWVmUk5Njtv2UlBQCQN7e3nTw4EGKj48nX19f6tWrFwUGBtKpU6coKiqK2rdvT9OmTWPPW716NdnY2ND27dvp3Llz9Prrr5OFhQVdvnyZbt26RadOnSJHR0d6+eWXKS4ujv7++29q164dAaDo6Gh27A4ODjR//ny6du0aRUVF0ZAhQ2jgwIGcsX344Yd1urY1oaCggG7fvk15eXkUGRlJeXl5nOOZmZl06dIlysvLo7KyMrpx4wbFxMSQVqtly6SmplJMTAwVFhZSaWkpXb9+neLi4ur8nWhIEhIS6O7du1RWVkalpaWUmJhIsbGxnP435zHm5+dTQUEBlZeXU3l5Od2+fZsuXrxIZWVlRNS8x1aZkpISio2Npbi4OLp16xa7v7mPMSMjg+Li4kitVpNaraaioiK6cuUK+xz//PPPydramnbt2kVXrlyhl19+mVxdXamoqIitY9q0adS6dWs6cuQIRUVF0cCBA6lHjx6ca9DU+e2338jCwoJ++OEHio+Ppw8//JCsrKw497opcODAAfr0009p165dBID27NnDOd6c7tewYcNo06ZNFBcXRzExMTRq1Chq27YtlZSUNLvx7N27l/bv308JCQmUkJBACxYsIAsLC4qLi2tW46jMhQsXyNPTk7p3787RAs1lPIsXL6auXbtSVlYWuxnrsaY+jnqL75CQEAoJCeH8UZ04cYJCQkLor7/+4pRdtmwZhYSEUH5+Prvv7NmzFBISQrt27eKUXblyJYWEhNCdO3dqMx6WS5cuEQBKTU01e9zV1ZU+//xz9rNGo6E2bdqYiO8nn3zS5FxzD8bKGMT3jz/+yO7bvn07AaBjx46x+8LCwqhTp07sZzc3N1q2bBkREcXHx1Nqair16dOHpk+fTkREixYtolatWlFpaSl7zsaNGzni+//+7/9o6NChnP6kp6cTAEpISGDH1pji25jK4pthGIqJiaHMzEx2n06no6ioKPaPR6PR0MWLF+nevXtsGZVKRZGRkVRQUPBQ+l0b1Go1RUZGsn/YLXGMhr63pLFptVq6fPkyFRYW0vXr11lB1hLGaBDfBoyf4wzDkIuLC+cZWFFRQUqlkr799lsi0k+gLSws6LfffuPUKRQK6eDBgw9vIPXk6aef5hg4iIi8vb3pk08+eUQ9ejCVf2Oa+/0yGK1OnDhBRM1/PLa2tvTjjz8223EUFxdThw4d6MiRIxwt0JzGs3jxYurRo4fZY81hHC3W7aRHjx4YNGgQunXrhhdffBE//PAD6/pQWFiIrKws9OvXjy0vFovRu3dvk3rM7asN3bt3Z//v7OwMAOjWrRtnX05ODgB9yvnMzEz0798fDMOgtLQUNjY26N+/P65duwYAyMjIQKdOnWBpacnWYTwOALh06RKOHz8OhULBbt7e3gCA5OTkeo2nIVCr1dBoNFAqlew+oVAIa2trlJSUAADKyspARLCxsWHLSCQSyOVytkxTQqfTAdB/j4CWNUYiQl5eHhiGgUKhaFFjS0tLQ6tWrTj9BFrO/VOpVIiNjcXly5eRlpYGrVYLAEhJSUF2djaGDh3KlpVKpQgICMCZM2cA6J8jGo2GU8bNzQ0+Pj5smaaOWq3GpUuXOGMAgKFDhzabMQDN/34ZXNbs7OwANN/x6HQ6/PbbbygtLUW/fv2a7Tjee+89jBo1CoMHD+bsb27jSUpKgpubG7y8vPDKK6/g5s2bzWYc9c5wOX/+fACAhYUFu69///7w9fWFUMjV9nPnzjUp26dPH/Tq1cuk7IcffmhStjaIRCIcOXIEZ86cweHDh/H111/j008/xfnz59kHQE2wsrKqU/sGjPtvyI5XeR/DMJxzBAIB+yNpYWEBImLPFQgEoAfkRWIYBqNHj8aKFStMjrm6utZtIA2IRqMBcF+oGhCLxay/rEajgUAgMCljYWHBXpumAhEhPT0dCoUCcrkcQMsYY1lZGa5fvw6GYSASidC+fXuOsGzOYwOAvLw8lJWVoXPnzibHWsL9s7KygpeXF6RSKbRaLTIyMpCVlQUHBwdkZ2cDuG8QMODs7Ixbt24BALKzsyGRSGBra2tSxnB+Uyc3Nxc6nc7sOJvLGAA06/tFRJg9ezYGDBgAHx8fAM1vPFeuXEG/fv1QUVEBhUKBPXv2oEuXLqxIay7jAIDffvsNUVFRiIyMNDnWnO5L37598csvv6Bjx464c+cOli5dCj8/P1y9erVZjKPelm+JRAKJRMJJuywSiSCRSEx+lOpbtrYIBAL0798foaGhiI6OhkQiwZ49e6BUKuHq5oi7cQAAHglJREFU6opz586xZbVaLS5dulSjei0sLFhLZ0NiY2MDNzc3nDp1irP/zJkzrEDo2LGjycIv43EAQK9evXD16lV4enqiffv2nK2+kwkeU9LS0lBeXo527do96q40KDKZDF26dEHnzp3h6OiIlJQUkwWHzRW1Wo20tDR4eXmZTPxbCkqlEra2trC0tISNjQ3atm0LAJyFfJWfr8YT/aqoSZmmRl3G2RRpjvdrxowZuHz5MrZv325yrLmMp1OnToiJicG5c+fw7rvvYtKkSYiPj2ePN5dxpKen48MPP8TWrVurzXLbHMYzYsQIvPDCC+jWrRsGDx6M/fv3AwC2bNnClmnK42iZvzoAzp8/j+XLl7MRDHbv3o27d++yIvbDDz/E559/jj179uD69euYPn06CgoKalS3p6cnjh07huzsbJMoHvVl3rx5WLFiBXbt2oXU1FQsXLgQMTEx7JuAoKAgCIVCTJkyBfHx8Thw4AC+/PJLTh3vvfce8vLyMH78eFy4cAE3b97E4cOH8eabbzbKpKG2GCz/la2DWq2WPWaw+Fcuo9FoTCZqj5K0tDQUFhaiU6dOkEgk7P6WMEahUAiZTAYrKyu0adMGcrkcOTk5LWJspaWl0Gq1iI+PZ6OBFBcXIycnBxcvXmT72JzHWBmDoePWrVtsVIDKFp6cnBzWWuTi4gK1Wm3yjDMu09RxcHCASCSqdpzNgeZ6v95//33s3bsXx48fR5s2bdj9zW08EokE7du3R+/evREWFoYePXpg3bp1zW4cly5dQk5ODp566imIxWKIxWKcOHECX331FcRiMduf5jIeY6ysrNCtWzckJSU1i/vSYsW3jY0NTp48iZEjR6Jjx45YuHAhVq1ahREjRgAA5syZg9dffx2TJ09Gv379YG1tjeeff75Gda9atQpHjhyBu7s7evbs2aD9/uCDDzBnzhzMmzcP48ePx+HDh7F371506NABgN7nbPPmzYiPj0fPnj3x6aefmriXuLm54fTp09DpdBg2bBh8fHzw4YcfQqlUNgkrn0QigYWFBYqKith9DMOguLgYCoUCAGBpaQmBQMApo1arUV5ezpZ5lBAR0tLSkJ+fj44dO0IqlXKOt4QxmoNhmBYxNhsbG3Tt2pWzWVpaws7ODl27doVUKm32Y6wMwzDQaDRwdHSEl5cXXFxccOTIEfa4Wq3GiRMn4OfnBwB46qmnYGFhwSmTlZWFuLg4tkxTRyKR4KmnnuKMAQCOHDnSbMYAoNndLyLCjBkzsHv3bvz777/w8vLiHG9u46kMEUGlUjW7cQwaNAhXrlxBTEwMu/Xu3RuvvvoqYmJi0K5du2Y1HmNUKhWuXbsGV1fX5nFfarIqs7poJy2JSZMmcaKdPGru3btHFy9eZEPapaWl0aVLl6iiouJRd+2BaLVaKi0tpdLSUoqMjKTs7GwqLS1l+56ZmUlRUVFsGLfk5GSzYdxiY2ObZBi31NRUioqKoqKiIjaUm1qtJp1Ox5ZpzmNMT0+noqIiqqiooNLSUrp9+zZFRkZSYWEhETXvsVWFcbQTouY/xrS0NPYeFhcX07Vr1+jgwYN048YNItKH4lIqlbR79266cuUKjR8/3mworjZt2tDRo0cpKiqKnn322UceIq22GEIN/vTTTxQfH08zZ84kKyurKiNhPSqKi4spOjqaoqOjCQCtXr2aoqOj2e9kc7pf7777LimVSgoPD+eEgjOEKm1O45k/fz6dPHmSDVm8YMECEgqFdPjw4WY1jqqoHPmsuYxnzpw5FB4eTjdv3qRz585RUFAQWVtbs3/XTX0cTe/dKA+LnZ0dtFotMjMzodFoIJfL0aFDBxMra1OkrKyMk1TBkNzD3t6enZXSf9ZjrVYLKysrdOzYESKRiD3H3d0dAoEAN2/eBMMwsLa2RocOHZqEr6YhMVLlxBGenp5wcHAAgGY9Rq1Wi5SUFGg0GohEIsjlcnTs2JGN7NGcx1ZTmvsY1Wo1bt68Ca1WC7FYDJlMBldXV7Ru3RoA8NFHH6G8vBzTp09Hfn4++vbti8OHD8Pa2pqtY82aNRCLxXjppZdQXl6OQYMGYfPmzZxr0NR5+eWXce/ePSxZsgRZWVnw8fHBgQMH4OHh8ai7xuHixYsYOHAg+3n27NkAgEmTJmHz5s3N6n5t3LgRgD5RnTGbNm3C5MmTATSf79+dO3cwceJEZGVlQalUonv37jh48CCGDBnSrMZRU5rLeG7fvo3x48cjNzcXjo6O8PX1xblz59i/66Y+DgHRA0JnAKioqEBKSgqbIaylMnnyZBQUFJhkFuPh4eFp7jwuz3EeHh6epg5v+TZi8+bNj7oLPDw8PDw8PDw8LZhHv/qOh4eHh4eHh4eH5zGBF988PDw8PDw8PDw8D4laie8auIfz8PDw8DRB+Oc3Dw8PT9OgRuLbkFSirKysUTvDw8PDw9M4GJ7fhuc5Dw8PD8+joUYLLkUiEVq1aoWcnBwA95NL8PDw8PA0bYgIZWVlyMnJQatWrZpkeDMeHh6ex4kahRoE9A/w7OzsGqdg5+Hh4eFpOrRq1QouLi684YSHh4fnEVNj8W1Ap9NBo9E0Vn94HmOICIsXL8ahQ4dQWFiIPXv2oHPnzo3erre3N7755hsMHjy40dt6VGRkZGDQoEG1uqZ79uzB8uXLERkZ2SB9yM/Px6hRo7Bjxw420UtD8frrr8Pb2xsLFixokPrUajWGDRuGb775Bl27dm2QOh8lFhYWvMWbh4eHp4lQ6zjfIpGIf4jzNAr//PMP1q5di/DwcLRr1w4ODg4Qixs/FP2tW7eg1WofWuIRT09PzJw5EzNnznwo7RnaPHfuXK2uqUqlQnp6erXXpTaJqVatWoWePXviiSeeqGm3WcLDwzF+/HhkZmaatdzm5OTAzc2twe6hTCbD+PHj8fHHH+Po0aMNUicPDw8PDw/AhxrkaUIkJyfD1dUVfn5+cHFxeSjC+3FBJBI90mtaXl6On376CVOnTq3T+Xv37sVzzz33UF0mXn31VURERODatWsPrU0enoaAiPD222/Dzs4OAoEAMTExjd5mYGDgQzUoPCrqMs6GvDb37t2Dk5MTUlNTG6S+ynz++efo169fg9U3btw4rF69usHqaynw4punSTB58mS8//77SEtLg0AggKenJwD9j8jKlSvRrl07yOVy9OjRAzt37uSce/XqVYwaNQo2NjawtraGv78/kpOTAQCRkZEYMmQIHBwcoFQqERAQgKioqBr36++//0arVq3AMAwAICYmBgKBAPPmzWPLvPPOOxg/fjz7+cyZM3jmmWcgl8vh7u6ODz74AKWlpQD0D+Fbt25h1qxZEAgEVYrJOXPmYPTo0ezntWvXQiAQYP/+/ey+Tp064bvvvmM/b9q0CZ07d4ZMJoO3tzc2bNjAHktNTTX5Ed67dy86dOgAuVyOgQMHYsuWLRAIBCbrOg4dOoTOnTtDoVBg+PDhyMrKAgCEhIRgy5Yt+Ouvv9ixhIeHmx3PP//8A7FYbPJQr2kfDOIbAEpLS/H6669DoVDA1dUVq1atMmlv69at6N27N6ytreHi4oIJEyawC8aJCO3bt8eXX37JOScuLg5CoZD97tjb28PPzw/bt283OyYenqbKwYMHsXnzZuzbtw9ZWVnw8fF51F1qcB6V2N+9ezc+++yzBq+3puMJCwvD6NGj2d/I2jJ58mR88sknVR6PjY1Fjx496lS3ORYtWoRly5ahqKiowepsCfDim6dJsG7dOixZsgRt2rRBVlYW62e8cOFCbNq0CRs3bsTVq1cxa9YsvPbaazhx4gQAvS/zM888A5lMhn///ReXLl3Cm2++Ca1WCwAoLi7GpEmTEBERgXPnzqFDhw4YOXIkiouLa9SvZ555BsXFxYiOjgYAnDhxAg4ODmz7gN4lIiAgAABw5coVDBs2DGPHjsXly5fx+++/49SpU5gxYwYA/YO7TZs2WLJkCbKyslghW5nAwEBERESwor9yu9nZ2UhMTGTb/eGHH/Dpp59i2bJluHbtGpYvX47/+7//w5YtW8zWn5qainHjxmHMmDGIiYnBO++8g08//dSkXFlZGb788kv8+uuvOHnyJNLS0jB37lwAwNy5c/HSSy+xgjwrKwt+fn5m2zt58iR69+5dpz5cvXoV2dnZGDRoEABg3rx5OH78OPbs2YPDhw8jPDwcly5d4pyjVqvx2WefITY2Fn/++SdSUlIwefJkAIBAIMCbb76JTZs2cc75+eef4e/vz3GLefrppxEREWF2TDw8TRX+LWLjYWdnB2tr60fSdn3fIDIMg/379yM4OLjKMrGxsXjyySfr2ENTunfvDk9PT/zvf/9rsDpbBMTD00RYs2YNeXh4sJ9LSkpIJpPRmTNnOOWmTJlC48ePJyKi+fPnk5eXF6nV6hq1odVqydramv7++292HwDas2dPlef06tWLvvzySyIiGjNmDC1btowkEgkVFRVRVlYWAaBr164REdHEiRPp7bff5pwfERFBQqGQysvLiYjIw8OD1qxZU20/CwoKSCgU0sWLF4lhGLK3t6ewsDDq06cPERFt27aNnJ2d2fLu7u60bds2Th2fffYZ9evXj4iIUlJSCABFR0cTEdHHH39MPj4+nPKffvopAaD8/HwiItq0aRMBoBs3brBl1q9fz2l30qRJFBwcXO1YiIiCg4PpzTff5OyrSR+IiJYtW0Zjx44lIqLi4mKSSCT022+/scfv3btHcrmcPvzwwyrbv3DhAgGg4uJiIiLKzMwkkUhE58+fJyIitVpNjo6OtHnzZs5569atI09PzweOj4enqTBp0iQCwG4eHh7EMAytWLGCvLy8SCaTUffu3WnHjh2c83Q6HX3++ef0xBNPkEQiIXd3d1q6dCl7/J9//qH+/fuTUqkkOzs7GjVqFOfZEBAQUOXf4N69e0mpVJJOpyMioujoaAJAc+fOZcu8/fbb9Morr7Cfq+tz5TECoJSUlHq3W5PrVHmcRUVFNGHCBLK0tCQXFxdavXq1SZmAgAB6//33ad68eWRra0vOzs60ePFi9nhNx7Nr1y5ycHAwe43j4uLI39+fZDIZ9ejRg06dOkUAKCYmhi1z8uRJcnJyYq9HfHw8BQQEkEwmoyeffJIiIyNJJBJxfnNDQ0PJx8eHLC0tycnJiaZNm8b5vW3dujWtX7+e05fTp0+TXC6n1NRUIiIKCQkhf39/s/1+XOEt3zxNlvj4eFRUVGDIkCFQKBTs9ssvv7CuATExMfD3968ycUhOTg6mTZuGjh07QqlUQqlUoqSkBGlpaTXuR2BgIMLDw0FEiIiIQHBwMHx8fHDq1CkcP34czs7O8Pb2BgBcunQJmzdv5vR32LBhYBgGKSkpNW5TqVTiySefRHh4OK5cuQKhUIh33nkHsbGxKC4u5ljb7969i/T0dEyZMoXT7tKlS9nrVJmEhAT06dOHs+/pp582KWdpacmxBLu6urLuG7WhvLzcZDFkTfvw119/sS4nycnJUKvVHPcVOzs7dOrUiXNOdHQ0goOD4eHhAWtrawQGBgIAe99dXV0xatQo/PzzzwCAffv2oaKiAi+++CKnHrlczicX42lWmHuL+KA3iAAwf/58rFixAv/3f/+H+Ph4bNu2Dc7Ozuzx0tJSzJ49G5GRkTh27BiEQiGef/559u1cddT2DSJQ/VvPdevWoV+/fnjrrbfYt27u7u71brcm16kys2fPxunTp7F3714cOXIEERERZl0bt2zZAisrK5w/fx4rV67EkiVLcOTIEQCo8XjMvUEE9G8HfX194e/vj+joaCxatAjjxo2DhYUFJ7rV3r17MXr0aAiFQly/fh19+/ZF7969ERcXh0WLFiE4OBhEhO7duwPQu+jpdDp89913iI+Px+bNm7Fz5078+OOPbJ2+vr6ciFhExAYU8PDwAKB/rl+4cAEqlarK6/i4wb+L4mmyGB7q+/fvNwlNJ5VKAejFUXVMnjwZd+/exdq1a+Hh4QGpVIp+/fpBrVbXuB+BgYH46aefEBsbC6FQiC5duiAgIAAnTpxAfn4+5weDYRi88847+OCDD0zqadu2bY3bNLQbHh4OiUSCgIAA2NraomvXrjh9+jTCw8NZ/0DDdfrhhx/Qt29fTh1VRSYiIhN/czITdbTypEYgENQpTbmDgwPy8/Nr3Yfs7GxERUVh1KhRVfaxMqWlpRg6dCiGDh2KrVu3wtHREWlpaRg2bBjnvk+dOhUTJ07EmjVrsGnTJrz88suwtLTk1JWXlwdHR8dajZWH51GiVCphbW3NLrIuLS3F6tWr8e+//7KT1nbt2uHUqVP47rvvEBAQgOLiYqxbtw7ffPMNJk2aBAB44oknMGDAALbeF154gdPOTz/9BCcnJ8THxz/Qp9zYmPDUU08hPDwcs2bNQmhoKIqLi1FaWorExER2kvygPm/btg0SiQSWlpZwcXFpkHZrcp0qU1xcjC1btmDbtm2sW9ymTZvg5uZmUrZ79+5YvHgxAKBDhw745ptvcOzYMQwZMgRKpbJG40lNTTVb94wZMzBy5EgsW7YMgD587tatW3Hz5k1IJBK23N69e9m1Lu+99x6Cg4PZz0888QT++OMPREVFwcrKCoD+eR8aGsqe7+HhgSFDhuD69evsPl9fX2zevJn9/OuvvyItLQ3z589n97Vu3RoqlQrZ2dmsIH/c4cU3T5OlS5cukEqlSEtLM/vgA/QPtC1btkCj0Zi1fkdERGDDhg0YOXIkACA9PR25ubm16ofBerJ27VoEBARAIBAgICAAYWFhyM/Px4cffsiW7dWrF65evYr27dtXWZ9EIoFOp3tguwbRLxaL2RjkAQEB+O233zj+3s7OzmjdujVu3ryJV199tUZj8vb2xoEDBzj7Ll68WKNzjanpWHr27ImtW7fWug979+5Fv3794ODgAABo3749LCwscO7cOXYyk5+fz7ke169fR25uLj7//HPWemRubCNHjoSVlRU2btyIf/75BydPnjQpExcXh549ez5wfDw8TRXjN4jGqNVq9rt97do1qFQqVkCaIzk5Gf/3f/+Hc+fOITc3l530p6Wl1WhBp8GYMHv2bERERGDp0qXYtWsXTp06hYKCAs4bxJr0uabUtN3IyMhat3nz5k1oNBrOGzulUmnyJg4Aa002UJe3iObeIKampiI8PBxxcXGc/VKplLNw8tq1a7h9+zYGDx6MW7du4d9//zWx0FtYWHDOuXXrFr744guEh4cjIyMDGo0GFRUVCAsLY8v4+vri448/RklJCYRCIRYsWIClS5dy/OINRjL+LeJ9ePHN02SxtrbG3LlzMWvWLDAMgwEDBqCoqAhnzpyBQqHApEmTMGPGDHz99dd45ZVXMH/+fCiVSpw7dw5PP/00OnXqhPbt2+PXX39F7969UVRUhHnz5j3QWl4Zg/Vk69atWLduHQC9IH/xxReh0WhYaw0AfPzxx/D19cV7772Ht956C1ZWVrh27RqOHDmCr7/+GoA+5vbJkyfxyiuvQCqVssKyMgbR//fff2Pp0qUA/r+9uw1p8u/iAP7NbS4pHXNN8bFEw2apzCeEUtE0iTAofZMURipWiIgROawQNXuwRpJSpNUqIsiSUhKkFLVCkFALH5tSREbiE4aVme78X4i7W9Oc3f/t7sbzAV94Pf3Odb24dnad33U2+0ESHx8PuVwOHx8f/ba5ubnIyMiAnZ0dtm/fju/fv+Ply5cYGxtDVlaW0bHT0tKgVqtx7NgxJCcno729Xf/0Yint/NatW4fa2lr09vZCJpNBIpHM+yUoNjYWKpUKY2NjkEqlJsdQVVVl8HLQ6tWrkZycjKNHj0Imk8HR0RE5OTmwsvrPDDp3d3dYW1vj0qVLOHjwIDo6OubtTiAQCLB//36oVCp4eXnN217r2bNnZulswJil/BsVRACIi4uDm5sbysrK4OzsDJ1Oh02bNplcRVxqBXGxmE1l6rh/MuZcJe5Pq4imTNn52XwVxFevXsHa2trox8C6u7v1VQxg9l4aExMDGxsbtLe3QygUwtfX12Cf1tZWJCYmAgCGh4cREhKCyMhIqNVquLi4QKfTISgoyOCFzKCgIAgEArS2tuLp06eQyWQ4cOCAwXFHR0cBgKuIP+E53+yvlp+fj5MnT+L06dNQKBSIjY1FdXU1PDw8AMy2g6uvr8fExAQiIiIQGBiIsrIy/Y3u+vXrGBsbg1KpxL59+5CRkQEHB4clxxEZGYmZmRl9oi2VSuHj4wO5XG4wp87Pzw+NjY3QarUICwuDUqnEiRMn4OTkpN8mLy8P7969g6en529vRhKJBEqlEvb29vpEOywsDDqdzqgSkJKSgvLycmg0Gvj6+iIiIgIajUZ/nX7l4eGB+/fvo7KyEn5+frh8+bK+08hSPtxSU1Ph7e2NoKAgyOVyvHjxYt7tfH19ERQUhHv37pkcw5cvX1BXV6ef7z2nqKgI4eHh2LlzJ6Kjo7FlyxYEBgbq18vlcmg0GlRUVMDHxwdnzpwxais4Jzk5GVNTU0YfFgDQ3NyM8fFxJCQkmHw9GPvb/FxB9PLyMvibqwzNtfusq6ub9xgjIyPo7u7G8ePHsXXrVigUCqMkcDELVRAbGhqM5nubErOpVTdTxzVlzF95enpCJBKhpaVFv+zz58/QarVLujamno9SqURXV5fBMoFAgOnpaUxOTuqXNTY2GrUM/PndGSsrK+h0OoMvTjU1Nejs7NQn1jU1NZiensbdu3exbds2bNy4EU1NTZiamjJIvleuXAl/f39UVlbi/PnzUKvVBg9DgNkKoqur64IPmpal/817noyxv01BQQG5urqa7fiPHz8mhUKhf9N+sRgePHhACoXCbPEQET1//pyEQiF9+vTJaF1CQgKdOnXKrOMzZg6/do7KyckhmUxGGo2G+vr6qLW1lUpKSgy6++Tm5pJUKqWbN29SX18fNTc3U3l5ORHNdkKRyWS0d+9e0mq1VFdXR8HBwQadon7X7WROQEAACQQCKikpISKi0dFREolEBIA6OzsNtl0s5tTUVAoODqa3b9/S0NDQb+8rpo5rynX69TxTUlLIw8OD6uvrqaOjg+Lj48nW1pYyMzMX3IdotgNUUlKS/n9Tzuf169ckFAppdHRUv+zjx48kFospPT2d+vv7qbq6mjw9PQkAjYyMEBHR4OAgCYVCGhwcJCKigYEBEovFdOjQIerv76dHjx6Rs7MzAaAPHz4QEVF1dTUJhUJ6+PAhvXnzhi5cuEBr1qwhFxcXo7jS09NpxYoVFBcXN+/1T0pKMup2tdxx8s3YMlVaWkotLS3U399Pt27dIolEQjk5OWYd8+LFi/T+/XuTYqitraWqqiqzxDE5OUlarZYiIyMpMTFx3vX5+fn09etXs4zPmDn9mnzrdDoqLi4mb29vEolEJJfLKTY2lhobG/XbzMzMUEFBAa1du5ZEIhG5u7tTYWGhfv2TJ09IoVCQWCwmPz8/amhoWHLyfeTIEQJAHR0d+mX+/v4kl8tJp9MZbLtYzL29vRQaGko2NjYLtuZb6rimXCdTWg2GhIRQdnb2gvsQGSffpp5PaGgoXblyxWDZnTt3yM3NjVatWkW7du2igoIC8vLy0q8vLy+nzZs3G+xz+/ZtcnV1JalUSlFRUaRSqQzaGOp0OkpLSyNbW1tycHCgrKwsOnz4MO3YscMopvLychKJRNTT02O07tu3b2RnZ0fNzc3zns9yxck3Y8tUZmYmOTk5kVgspvXr11NeXh79+PFjWcRw48YNsrKyooCAAP2THsYY+29NTEyQRCLRVw3+bYtVEGdmZig8PJxUKpV+WVxcHJ09e9Ys8RARRUZGLvjFq6SkhGJiYsw29v+rFUR/0DeMMcYYY2yZa2trQ09PD0JCQjA+Po68vDw0NDSgr6/PbHOci4uLsXv3bri5uaGpqQlDQ0NQKpUYHh5GUVERWlpa0NbWBnt7ewDAuXPnsGfPngXnrv8JnU6HoaEhXLt2DaWlpejq6oJEIjHa7urVq4iIiJi3A8xyxsk3Y4wxxtgfaGtrQ0pKCnp7e2FtbY3AwECo1WqjTiLmUlFRgezsbAwMDMDR0RHR0dEoLCw0+IEkc2hoaEBUVBQ2bNiAGzduGP3GBPs9Tr4ZY4wxxhizEG41yBhjjDHGmIVw8s0YY4wxxpiFcPLNGGOMMcaYhXDyzRhjjDHGmIVw8s0YY4wxxpiFcPLNGGOMMcaYhXDyzRhjjDHGmIVw8s0YY4wxxpiFcPLNGGOMMcaYhXDyzRhjjDHGmIVw8s0YY4wxxpiF/AO5Wim+HwLBXwAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 800x250 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["watercontent=pd.read_csv(\"data/watercontent.csv\")\n", "\n", "colorl3=['#a6cee3','#1f78b4','#b2df8a','#33a02c','#fb9a99','#e31a1c','#fdbf6f','#ff7f00','#cab2d6']\n", "\n", "#load study\n", "#use all studies, including some where diet was changed. But explude here Wyman data as that data tend to scatter much more\n", "#watercontent=watercontent.loc[watercontent['source'] != '<PERSON><PERSON> et al, 1978']\n", "x=watercontent['fec_wet']\n", "y=watercontent['water']/100.\n", "studylist=watercontent.source.unique()\n", "\n", "\n", "#define functions to describe relation between fecal wet and dry weight\n", "\n", "#fit non-linear function to data\n", "def func2_waterc(x, a, b):\n", "  return a +b*np.sqrt(x)\n", "  #return a * np.log(b * x) + c\n", "from scipy.optimize import curve_fit\n", "\n", "#fit non-linear function to data\n", "def func3_waterc(x, a, b, c):\n", "  return a +b*np.sqrt(x)+c*x\n", "  #return a * np.log(b * x) + c\n", "\n", "\n", "#fit non-linear function to data\n", "def func4_waterc(x, a, b):\n", "  return 1-(a+b/x)\n", "  #return a * np.log(b * x) + c\n", "\n", "from scipy.optimize import curve_fit\n", "\n", "#plot data and fits\n", "fig, axs = plt.subplots(1,2, figsize=(8,2.5))\n", "markerlist=['o','s','<','>','^','v','d','h']\n", "#plot data from different stuides\n", "ilc=-1\n", "for il in studylist:\n", "    ilc=ilc+1\n", "    sel=watercontent.loc[watercontent['source'] ==il]\n", "    #axs[0].plot(sel['fec_wet'],sel['water'],marker=markerlist[ilc],color=colorl3[ilc],ls='',label=il,fillstyle='none')\n", "    axs[0].plot(sel['fec_wet'],100.-sel['water'],marker=markerlist[ilc],color=colorl3[ilc],ls='',label=il,fillstyle='none')\n", "    \n", "    #axs[0].plot(BDdata['WET_WymanTable1'],BDdata['DRY_WymanTable1'],marker='o',ls='',c='b',label='<PERSON><PERSON> et al - female',fillstyle='none')\n", "\n", "\n", "#plot constant value\n", "\n", "#axs[0].axhline(100-100*drymassfraction_fit,ls='--',color='k',label='const. fraction')\n", "axs[0].axhline(100*drymassfraction_fit,ls='--',color='k')\n", "\n", "\n", "#add linear fit\n", "fit=np.polyfit(x,y,1)\n", "linfit_water_par=fit\n", "print(\"funct parwater lin\")\n", "print(linfit_water_par)\n", "xr=np.linspace(0,500)\n", "xr2=np.linspace(50,500)\n", "#axs[0].plot(xr,100*(xr*fit[0]+fit[1]),color='k',label=\"linear model\",ls='-.')\n", "axs[0].plot(xr,100-100*(xr*fit[0]+fit[1]),color='k',label=\"lin\",ls='-.')\n", "\n", "#add sqrt fit\n", "#popt, pcov = curve_fit(func2_waterc, x, y)\n", "#func2_water=popt\n", "#print(\"funct parwater 2\")\n", "#print(func2_water)\n", "#axs[0].plot(xr, 100*func2_waterc(xr, *func2_water), color='r',ls='--', label=\"sqrt (2)\")\n", "#axs[1].plot(xr, 100-100*func2_waterc(xr, *func2_water), color='r',ls='--', label=\"sqrt\")\n", "\n", "#add other non-linear fit\n", "popt, pcov = curve_fit(func3_waterc, x, y)\n", "func3_water_par=popt\n", "print(\"funct parwater 3\")\n", "print(func3_water_par)\n", "#axs[0].plot(xr, 100*func3_waterc(xr, *func3_water_par), color='gray',ls=':', label=\"sqrt model\")\n", "axs[0].plot(xr,100-100*func3_waterc(xr, *func3_water_par), color='gray',ls=':', label=\"sqrt model\")\n", "\n", "#add 1/x fit\n", "#popt, pcov = curve_fit(func4_waterc, x, y)\n", "#func4_water_par=popt\n", "#print(\"funct parwater 4 noW<PERSON>man\")\n", "#print(func4_water_par)\n", "#axs[0].plot(xr2, 100*func4_waterc(xr2, *func4_water_par), color='magenta',ls=':', label=\"1/x (4)\")\n", "#axs[1].plot(xr2, 100-100*func4_waterc(xr2, *func4_water_par), color='magenta',ls=':', label=\"1/x\")\n", "\n", "\n", "axs[0].set_xlabel('fecal wet weight (g/day)')\n", "axs[0].set_ylabel('dry mass fraction (%)')\n", "axs[0].legend()\n", "\n", "axs[0].set_xlim(0,500)\n", "axs[0].set_ylim(0,50)\n", "\n", "\n", "pearsonc=np.corrcoef(x,y)\n", "print('pearson coefficient - fecal drymass fraction and wet weight')\n", "print(pearsonc)\n", "\n", "\n", "#### plot transit time into same figure\n", "\n", "Burkittdata=pd.read_csv('data/burkitt_data2.csv')\n", "\n", "###look at transit time vs fecal weight\n", "axs[1].plot(Burkittdata['fecal weight'],Burkittdata['transit time'],ls='',marker='o',markeredgewidth=1,markeredgecolor='k',markerfacecolor='None',label='<PERSON><PERSON><PERSON><PERSON> et al')\n", "axs[1].set_xlabel(\"fecal wet weight $(g/day)$\")\n", "axs[1].set_ylabel(\"transit time $(h)$\")\n", "axs[1].set_xlim(0,550)\n", "axs[1].set_ylim(0,160)\n", "\n", "#fit non-linear function to data\n", "def func2(x, a, b):\n", "  return a +b/(x)\n", "  #return a * np.log(b * x) + c\n", "xc=Burkittdata['fecal weight']\n", "yc=Burkittdata['transit time']\n", "#print(xc)\n", "#print(yc)\n", "from scipy.optimize import curve_fit\n", "popt, pcov = curve_fit(func2, xc, yc)\n", "xcc=np.linspace(0,75,100)\n", "#axs[2].plot(xcc, func(xcc, *popt), color='k', label=\"Fitted Curve\")\n", "#p0 = popt[:2]\n", "popt, pcov = curve_fit(func2, xc, yc,method='trf')\n", "xcc=np.linspace(0,600,200)\n", "axs[1].plot(xcc, func2(xcc, *popt), color='k',ls='--', label=\"1/x model\")\n", "\n", "#https://www.ncbi.nlm.nih.gov/pmc/articles/PMC4015195/#:~:text=The%20normal%20range%20for%20transit,(10–73%20hours).\n", "#axs[1].axhline(2,color='r',ls='--',label=\"transit stomach + SI\")\n", "x=np.linspace(0,600)\n", "axs[1].fill_between(x, 0*x+2, 0*x+7, color='red', alpha=0.5,label=\"transit stomach + SI\")\n", "print('pearson coefficient - transit time and wet weight')\n", "\n", "pearson_transittime=np.corrcoef(xc,yc)\n", "print(pearson_transittime)\n", "axs[1].legend()\n", "\n", "fig.savefig(\"plot_output_figure2/fecaldrymassfraction_trend.pdf\")\n"]}, {"cell_type": "code", "execution_count": 4, "id": "efa69ff5-3830-4ab5-a46a-af5be45098e7", "metadata": {}, "outputs": [], "source": ["#fraction bacterial dry weight\n", "\n", "BRD[\"fecealfractionbac\"]=0.546888888888889"]}, {"cell_type": "code", "execution_count": 5, "id": "df97722b-fac9-455b-94c0-7ad0ef5e2f37", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'energy': 2275.0,\n", " 'carbohydrates': 276.75,\n", " 'sugars': 59.0,\n", " 'proteins': 72.05000000000001,\n", " 'fat': 105.5,\n", " 'fiber': 19.9,\n", " 'fiber_low': 14.599999999999998,\n", " 'fiber_high': 25.2,\n", " 'carbLI_standard': 35.670500000000004,\n", " 'carbLI_higher': 44.6025,\n", " 'carbLI_lower': 25.755000000000003,\n", " 'carbLI_error': 9.423749999999998,\n", " 'bacwetweight': 117.72413793103448,\n", " 'fecalwetmass': 117.72413793103448,\n", " 'fecaldrymass': 29.58620689655172,\n", " 'fecaldrymassstd': 6.845624030794191,\n", " 'fecealfractionbac': 0.546888888888889,\n", " 'bacterialdrymass_feces': 16.180367816091955,\n", " 'energybacteria_fromfeces': 100.84956469130942,\n", " 'FP_fromfeces': 466.61884926595684,\n", " 'FP_fromfeces_g': [8.273028254513578,\n", "  2.679218579054624,\n", "  6.2112485745426635,\n", "  8.917506078661688,\n", "  1.5865312839653274,\n", "  5.089146712515624],\n", " 'FP_fromfeces_gsum': 32.75667948325351,\n", " 'energyfrac_fromfeces': 0.044329478985190955,\n", " 'energybacteria_fromfeces_error': 23.334461425213142,\n", " 'energyfrac_fromfeces_error': 0.01025690612097281,\n", " 'FP_fromfeces_error': 107.96575643932452,\n", " 'bacterialdrymass_feces_error': 3.743795719952113,\n", " 'energy_fromcarbs_standard': 97.849963476916,\n", " 'FPlist_fromcarbs_standard': [133.6668370726343,\n", "  29.5032313525979,\n", "  130.92560068770663,\n", "  96.05095261662028,\n", "  20.779742854909536,\n", "  41.81368935697409],\n", " 'FP_fromcarbs_standard': 452.74005394144274,\n", " 'FP_fromcarbs_g_standard': [8.026960899885836,\n", "  2.5995297144774008,\n", "  6.026505399655137,\n", "  8.652269811705155,\n", "  1.5393425709488433,\n", "  4.93777857616507],\n", " 'FP_fromcarbs_gsum_standard': 31.782386972837443,\n", " 'ferm_fromcarbs_cc_standard': 1034.0190440858526,\n", " 'drymass_fromcarbs_standard': 15.699109903883397,\n", " 'energyfrac_fromcarbs_standard': 0.043010972956886155,\n", " 'energy_fromcarbs_lower': 70.65013973305594,\n", " 'FPlist_fromcarbs_lower': [96.51082515820347,\n", "  21.302076603528377,\n", "  94.53158340118263,\n", "  69.35120855163386,\n", "  15.00349805099999,\n", "  30.190537541914683],\n", " 'FP_fromcarbs_lower': 326.889729307463,\n", " 'FP_fromcarbs_g_lower': [5.795668072400434,\n", "  1.8769259695368854,\n", "  4.351288783956436,\n", "  6.247156866331178,\n", "  1.1114441321200281,\n", "  3.565200578324705],\n", " 'FP_fromcarbs_gsum_lower': 22.947684402669665,\n", " 'ferm_fromcarbs_cc_lower': 746.5878101072634,\n", " 'drymass_fromcarbs_lower': 11.335153013681246,\n", " 'energy_fromcarbs_higher': 122.35188730124743,\n", " 'FPlist_fromcarbs_higher': [167.13741328358648,\n", "  36.89092881804987,\n", "  163.70976310041732,\n", "  120.10239873516788,\n", "  25.983052681798757,\n", "  52.28396236510385],\n", " 'FP_fromcarbs_higher': 566.1075189841242,\n", " 'FP_fromcarbs_g_higher': [10.036935942505936,\n", "  3.2504597381583746,\n", "  7.53556039551221,\n", "  10.818824078063923,\n", "  1.92479855961497,\n", "  6.174213115695114],\n", " 'FP_fromcarbs_gsum_higher': 39.74079182955053,\n", " 'ferm_fromcarbs_cc_higher': 1292.940508651105,\n", " 'drymass_fromcarbs_higher': 19.63021402806126,\n", " 'energyfrac_fromcarbs_lower': 0.031055006476068545,\n", " 'energyfrac_fromcarbs_higher': 0.05378104936318568,\n", " 'FP_fromcarbs_standard_error': 119.60889483833054,\n", " 'FP_fromcarbs_gsum_standard_error': 8.396553713440428,\n", " 'drymass_fromcarbs_standard_error': 4.147530507190006,\n", " 'energy_fromcarbs_standard_error': 25.850873784095736,\n", " 'energyfrac_fromcarbs_standard_error': 0.011363021443558566,\n", " 'FP_infeces': 9.041213793103447,\n", " 'FP_infeces_error': 9.41793103448276}"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "#save updated results\n", "display(BRD)\n", "\n", "with open('data_analysisresults/BRD_characteristics.json', 'w') as fp:\n", "    json.dump(BRD, fp)"]}, {"cell_type": "markdown", "id": "6b80eb44-2eae-4256-9e3d-d9a27b733548", "metadata": {}, "source": ["We use here the sqrt model, which describes the avialable data well and additionally assumes that for very high fecal wet weights the dry mass fraction (>300g/day) is not falling further. We expect this assumption to be not physiological, as for higher wet weight the fraction of drymass presumably falls further. However, without further data for high fecal weight this assumption is an upper boundary of dry mass."]}, {"cell_type": "code", "execution_count": null, "id": "0c47fd7b-c0f2-467c-b545-4f150202b1bd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8d24098f-c82c-4ab4-9466-bec7bf1fa59d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "63257556-714e-45a3-98f3-50cec60aa7c5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a0cf3949-5262-4717-ba73-d4604ec7f37c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}