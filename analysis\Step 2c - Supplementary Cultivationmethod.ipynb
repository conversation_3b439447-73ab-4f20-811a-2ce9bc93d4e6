{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Plot data to explain method\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>species_HPLCname</th>\n", "      <th>species</th>\n", "      <th>new_species</th>\n", "      <th>species.1</th>\n", "      <th>species_short</th>\n", "      <th>new_genus</th>\n", "      <th>genus</th>\n", "      <th>new_family</th>\n", "      <th>family</th>\n", "      <th>new_order</th>\n", "      <th>order</th>\n", "      <th>new_class</th>\n", "      <th>class</th>\n", "      <th>new_phylum</th>\n", "      <th>phylum</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON>.uniformis</td>\n", "      <td>Bacteroides uniformis</td>\n", "      <td>Bacteroides uniformis</td>\n", "      <td>Bacteroides uniformis</td>\n", "      <td><PERSON>. uniformis</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON><PERSON>fragilis</td>\n", "      <td>Bacteroides fragilis</td>\n", "      <td>Bacteroides fragilis</td>\n", "      <td>Bacteroides fragilis</td>\n", "      <td><PERSON><PERSON> fragilis</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON><PERSON>ovatus</td>\n", "      <td>Bacteroides ovatus</td>\n", "      <td>Bacteroides ovatus</td>\n", "      <td>Bacteroides ovatus</td>\n", "      <td><PERSON><PERSON> ovatus</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON>.theta</td>\n", "      <td>Bacteroides thetaiotaomicron</td>\n", "      <td>Bacteroides thetaiotaomicron</td>\n", "      <td>Bacteroides thetaiotaomicron</td>\n", "      <td><PERSON>. theta</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Bacteroides <PERSON>ii</td>\n", "      <td>Bacteroides <PERSON>ii</td>\n", "      <td>Bacteroides <PERSON>ii</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  species_HPLCname                       species  \\\n", "0      B.uniformis         Bacteroides uniformis   \n", "1       B.fragilis          Bacteroides fragilis   \n", "2         B.ovatus            Bacteroides ovatus   \n", "3          B.theta  Bacteroides thetaiotaomicron   \n", "4     <PERSON><PERSON>finegoldii        Bacteroides finegoldii   \n", "\n", "                    new_species                     species.1  species_short  \\\n", "0         Bacteroides uniformis         Bacteroides uniformis   B. uniformis   \n", "1          Bacteroides fragilis          Bacteroides fragilis    B. fragilis   \n", "2            Bacteroides ovatus            Bacteroides ovatus      B. ovatus   \n", "3  Bacteroides thetaiotaomicron  Bacteroides thetaiotaomicron       B. theta   \n", "4        Bacteroides finegoldii        Bacteroides finegoldii  B<PERSON> finegoldii   \n", "\n", "     new_genus        genus      new_family          family      new_order  \\\n", "0  Bacteroides  Bacteroides  Bacteroidaceae  Bacteroidaceae  Bacteroidales   \n", "1  Bacteroides  Bacteroides  Bacteroidaceae  Bacteroidaceae  Bacteroidales   \n", "2  Bacteroides  Bacteroides  Bacteroidaceae  Bacteroidaceae  Bacteroidales   \n", "3  Bacteroides  Bacteroides  Bacteroidaceae  Bacteroidaceae  Bacteroidales   \n", "4  Bacteroides  Bacteroides  Bacteroidaceae  Bacteroidaceae  Bacteroidales   \n", "\n", "           order    new_class        class    new_phylum         phylum  \n", "0  Bacteroidales  Bacteroidia  Bacteroidia  Bacteroidota  Bacteroidetes  \n", "1  Bacteroidales  Bacteroidia  Bacteroidia  Bacteroidota  Bacteroidetes  \n", "2  Bacteroidales  Bacteroidia  Bacteroidia  Bacteroidota  Bacteroidetes  \n", "3  Bacteroidales  Bacteroidia  Bacteroidia  Bacteroidota  Bacteroidetes  \n", "4  Bacteroidales  Bacteroidia  Bacteroidia  Bacteroidota  Bacteroidetes  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["import sys, os\n", "import glob \n", "import matplotlib as mpl\n", "mpl.rcParams['pdf.fonttype'] = 42\n", "import collections\n", "import builtins\n", "\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import scipy.integrate as spi\n", "from scipy.integrate import odeint #this is the module to solve ODEs\n", "\n", "import met_brewer\n", "\n", "%matplotlib inline\n", "import scipy.stats \n", "import json\n", "\n", "import csv\n", "from collections import defaultdict\n", "from pprint import pprint\n", "\n", "\n", "################################\n", "#load information of species to include\n", "#################################\n", "#load species information for all characterized species\n", "speciesinformation=pd.read_csv(\"data_hplc/species_properties.csv\",skiprows=1)\n", "display(speciesinformation.head())\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Read in data"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0.2</th>\n", "      <th>Unnamed: 0.1</th>\n", "      <th>Unnamed: 0</th>\n", "      <th>notes</th>\n", "      <th>strain</th>\n", "      <th>species</th>\n", "      <th>experiment</th>\n", "      <th>experiment_short</th>\n", "      <th>exp_number</th>\n", "      <th>medium</th>\n", "      <th>...</th>\n", "      <th>succinate_std</th>\n", "      <th>lactate_std</th>\n", "      <th>butyrate_std</th>\n", "      <th>formate_std</th>\n", "      <th>maltose_std</th>\n", "      <th>total_ferm</th>\n", "      <th>total_ferm_std</th>\n", "      <th>total_uptake</th>\n", "      <th>total_uptake_std</th>\n", "      <th>growth_rate_std</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>29.0</td>\n", "      <td>NaN</td>\n", "      <td>DSM20083</td>\n", "      <td><PERSON><PERSON>adolescent<PERSON></td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>33</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "      <td>45.0</td>\n", "      <td>NaN</td>\n", "      <td>DSM20083</td>\n", "      <td><PERSON><PERSON>adolescent<PERSON></td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>51</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>2.0</td>\n", "      <td>38.0</td>\n", "      <td>NaN</td>\n", "      <td>DSM20083</td>\n", "      <td><PERSON><PERSON>adolescent<PERSON></td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>42</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>3.0</td>\n", "      <td>143.0</td>\n", "      <td>NaN</td>\n", "      <td>DSM20083</td>\n", "      <td><PERSON><PERSON>adolescent<PERSON></td>\n", "      <td>NaN</td>\n", "      <td>stan_BHI_30Dec22</td>\n", "      <td>199</td>\n", "      <td>BHI</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>4.0</td>\n", "      <td>142.0</td>\n", "      <td>NaN</td>\n", "      <td>DSM20083</td>\n", "      <td><PERSON><PERSON>adolescent<PERSON></td>\n", "      <td>NaN</td>\n", "      <td>stan_BHI_30Dec22</td>\n", "      <td>198</td>\n", "      <td>BHI</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 65 columns</p>\n", "</div>"], "text/plain": ["   Unnamed: 0.2  Unnamed: 0.1  Unnamed: 0  notes    strain         species  \\\n", "0             0           0.0        29.0    NaN  DSM20083  <PERSON><PERSON><PERSON><PERSON>   \n", "1             1           1.0        45.0    NaN  DSM20083  <PERSON><PERSON><PERSON><PERSON>   \n", "2             2           2.0        38.0    NaN  DSM20083  <PERSON><PERSON><PERSON><PERSON>   \n", "3             3           3.0       143.0    NaN  DSM20083  <PERSON><PERSON><PERSON><PERSON>   \n", "4             4           4.0       142.0    NaN  DSM20083  <PERSON><PERSON><PERSON><PERSON>   \n", "\n", "                       experiment                experiment_short exp_number  \\\n", "0  analysis_Zur_Newruns_2023_June  analysis_Zur_Newruns_2023_June         33   \n", "1  analysis_Zur_Newruns_2023_June  analysis_Zur_Newruns_2023_June         51   \n", "2  analysis_Zur_Newruns_2023_June  analysis_Zur_Newruns_2023_June         42   \n", "3                             NaN                stan_BHI_30Dec22        199   \n", "4                             NaN                stan_BHI_30Dec22        198   \n", "\n", "  medium  ... succinate_std  lactate_std  butyrate_std formate_std  \\\n", "0    YCA  ...           NaN          NaN           NaN         NaN   \n", "1    YCA  ...           NaN          NaN           NaN         NaN   \n", "2    YCA  ...           NaN          NaN           NaN         NaN   \n", "3    BHI  ...           NaN          NaN           NaN         NaN   \n", "4    BHI  ...           NaN          NaN           NaN         NaN   \n", "\n", "  maltose_std total_ferm  total_ferm_std total_uptake  total_uptake_std  \\\n", "0         NaN        NaN             NaN          NaN               NaN   \n", "1         NaN        NaN             NaN          NaN               NaN   \n", "2         NaN        NaN             NaN          NaN               NaN   \n", "3         NaN        NaN             NaN          NaN               NaN   \n", "4         NaN        NaN             NaN          NaN               NaN   \n", "\n", "   growth_rate_std  \n", "0              NaN  \n", "1              NaN  \n", "2              NaN  \n", "3              NaN  \n", "4              NaN  \n", "\n", "[5 rows x 65 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>species_HPLCname</th>\n", "      <th>species</th>\n", "      <th>new_species</th>\n", "      <th>species.1</th>\n", "      <th>species_short</th>\n", "      <th>new_genus</th>\n", "      <th>genus</th>\n", "      <th>new_family</th>\n", "      <th>family</th>\n", "      <th>new_order</th>\n", "      <th>order</th>\n", "      <th>new_class</th>\n", "      <th>class</th>\n", "      <th>new_phylum</th>\n", "      <th>phylum</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON>.uniformis</td>\n", "      <td>Bacteroides uniformis</td>\n", "      <td>Bacteroides uniformis</td>\n", "      <td>Bacteroides uniformis</td>\n", "      <td><PERSON>. uniformis</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON><PERSON>fragilis</td>\n", "      <td>Bacteroides fragilis</td>\n", "      <td>Bacteroides fragilis</td>\n", "      <td>Bacteroides fragilis</td>\n", "      <td><PERSON><PERSON> fragilis</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON><PERSON>ovatus</td>\n", "      <td>Bacteroides ovatus</td>\n", "      <td>Bacteroides ovatus</td>\n", "      <td>Bacteroides ovatus</td>\n", "      <td><PERSON><PERSON> ovatus</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON>.theta</td>\n", "      <td>Bacteroides thetaiotaomicron</td>\n", "      <td>Bacteroides thetaiotaomicron</td>\n", "      <td>Bacteroides thetaiotaomicron</td>\n", "      <td><PERSON>. theta</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Bacteroides <PERSON>ii</td>\n", "      <td>Bacteroides <PERSON>ii</td>\n", "      <td>Bacteroides <PERSON>ii</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  species_HPLCname                       species  \\\n", "0      B.uniformis         Bacteroides uniformis   \n", "1       B.fragilis          Bacteroides fragilis   \n", "2         B.ovatus            Bacteroides ovatus   \n", "3          B.theta  Bacteroides thetaiotaomicron   \n", "4     <PERSON><PERSON>finegoldii        Bacteroides finegoldii   \n", "\n", "                    new_species                     species.1  species_short  \\\n", "0         Bacteroides uniformis         Bacteroides uniformis   B. uniformis   \n", "1          Bacteroides fragilis          Bacteroides fragilis    B. fragilis   \n", "2            Bacteroides ovatus            Bacteroides ovatus      B. ovatus   \n", "3  Bacteroides thetaiotaomicron  Bacteroides thetaiotaomicron       B. theta   \n", "4        Bacteroides finegoldii        Bacteroides finegoldii  B<PERSON> finegoldii   \n", "\n", "     new_genus        genus      new_family          family      new_order  \\\n", "0  Bacteroides  Bacteroides  Bacteroidaceae  Bacteroidaceae  Bacteroidales   \n", "1  Bacteroides  Bacteroides  Bacteroidaceae  Bacteroidaceae  Bacteroidales   \n", "2  Bacteroides  Bacteroides  Bacteroidaceae  Bacteroidaceae  Bacteroidales   \n", "3  Bacteroides  Bacteroides  Bacteroidaceae  Bacteroidaceae  Bacteroidales   \n", "4  Bacteroides  Bacteroides  Bacteroidaceae  Bacteroidaceae  Bacteroidales   \n", "\n", "           order    new_class        class    new_phylum         phylum  \n", "0  Bacteroidales  Bacteroidia  Bacteroidia  Bacteroidota  Bacteroidetes  \n", "1  Bacteroidales  Bacteroidia  Bacteroidia  Bacteroidota  Bacteroidetes  \n", "2  Bacteroidales  Bacteroidia  Bacteroidia  Bacteroidota  Bacteroidetes  \n", "3  Bacteroidales  Bacteroidia  Bacteroidia  Bacteroidota  Bacteroidetes  \n", "4  Bacteroidales  Bacteroidia  Bacteroidia  Bacteroidota  Bacteroidetes  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0.2</th>\n", "      <th>Unnamed: 0.1</th>\n", "      <th>Unnamed: 0</th>\n", "      <th>notes</th>\n", "      <th>strain</th>\n", "      <th>species</th>\n", "      <th>experiment</th>\n", "      <th>experiment_short</th>\n", "      <th>exp_number</th>\n", "      <th>medium</th>\n", "      <th>...</th>\n", "      <th>succinate_std</th>\n", "      <th>lactate_std</th>\n", "      <th>butyrate_std</th>\n", "      <th>formate_std</th>\n", "      <th>maltose_std</th>\n", "      <th>total_ferm</th>\n", "      <th>total_ferm_std</th>\n", "      <th>total_uptake</th>\n", "      <th>total_uptake_std</th>\n", "      <th>growth_rate_std</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>182</th>\n", "      <td>182</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>ATCC8492</td>\n", "      <td><PERSON>.uniformis</td>\n", "      <td>av [25, 24, 23]_used_only: [59.0, 60.0, 61.0]</td>\n", "      <td>av</td>\n", "      <td>av_B.uniformis_ATCC8492_YCA</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>5.682633e-16</td>\n", "      <td>0.417771</td>\n", "      <td>0.016282</td>\n", "      <td>0.112282</td>\n", "      <td>0.000000</td>\n", "      <td>19.522489</td>\n", "      <td>0.447882</td>\n", "      <td>-6.650701</td>\n", "      <td>0.217517</td>\n", "      <td>0.048445</td>\n", "    </tr>\n", "    <tr>\n", "      <th>56</th>\n", "      <td>56</td>\n", "      <td>67.0</td>\n", "      <td>20.0</td>\n", "      <td>NaN</td>\n", "      <td>ATCC8492</td>\n", "      <td><PERSON>.uniformis</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>23</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55</th>\n", "      <td>55</td>\n", "      <td>66.0</td>\n", "      <td>21.0</td>\n", "      <td>NaN</td>\n", "      <td>ATCC8492</td>\n", "      <td><PERSON>.uniformis</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>24</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>54</td>\n", "      <td>65.0</td>\n", "      <td>22.0</td>\n", "      <td>NaN</td>\n", "      <td>ATCC8492</td>\n", "      <td><PERSON>.uniformis</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>25</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>20</td>\n", "      <td>20.0</td>\n", "      <td>14.0</td>\n", "      <td>NaN</td>\n", "      <td>ATCC25285</td>\n", "      <td><PERSON><PERSON>fragilis</td>\n", "      <td>analysis_stan_YCA_230607_2.5mM</td>\n", "      <td>analysis_stan_YCA_230607_2.5mM</td>\n", "      <td>17</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>96</td>\n", "      <td>119.0</td>\n", "      <td>106.0</td>\n", "      <td>NaN</td>\n", "      <td>NCM3722</td>\n", "      <td><PERSON><PERSON>coli</td>\n", "      <td>NaN</td>\n", "      <td>analysis_stan_eplus_Oct22.csv</td>\n", "      <td>150</td>\n", "      <td>epsilon</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>97</th>\n", "      <td>97</td>\n", "      <td>120.0</td>\n", "      <td>104.0</td>\n", "      <td>NaN</td>\n", "      <td>NCM3722</td>\n", "      <td><PERSON><PERSON>coli</td>\n", "      <td>NaN</td>\n", "      <td>analysis_stan_simple.csv</td>\n", "      <td>146</td>\n", "      <td>gamma</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>98</td>\n", "      <td>121.0</td>\n", "      <td>86.0</td>\n", "      <td>NaN</td>\n", "      <td>NCM3722</td>\n", "      <td><PERSON><PERSON>coli</td>\n", "      <td>NaN</td>\n", "      <td>analysis_stan_eplus_wotrp_Oct22.csv</td>\n", "      <td>112</td>\n", "      <td>gamma</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>194</th>\n", "      <td>194</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NCM3722</td>\n", "      <td><PERSON><PERSON>coli</td>\n", "      <td>av [162, 150]_used_only: [111.0, 113.0]</td>\n", "      <td>av</td>\n", "      <td>av_E.coli_NCM3722_epsilon</td>\n", "      <td>epsilon</td>\n", "      <td>...</td>\n", "      <td>5.784812e-01</td>\n", "      <td>0.632041</td>\n", "      <td>0.000000</td>\n", "      <td>0.371618</td>\n", "      <td>0.028349</td>\n", "      <td>20.556771</td>\n", "      <td>0.795447</td>\n", "      <td>-5.493158</td>\n", "      <td>0.613745</td>\n", "      <td>0.508830</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90</th>\n", "      <td>90</td>\n", "      <td>113.0</td>\n", "      <td>131.0</td>\n", "      <td>NaN</td>\n", "      <td>NCM3722</td>\n", "      <td><PERSON><PERSON>coli</td>\n", "      <td>NaN</td>\n", "      <td>stan_BHI_30Dec22</td>\n", "      <td>187</td>\n", "      <td>BHI</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>211 rows × 65 columns</p>\n", "</div>"], "text/plain": ["     Unnamed: 0.2  Unnamed: 0.1  Unnamed: 0  notes     strain      species  \\\n", "182           182           NaN         NaN    NaN   ATCC8492  B.uniformis   \n", "56             56          67.0        20.0    NaN   ATCC8492  <PERSON><PERSON>uniformis   \n", "55             55          66.0        21.0    NaN   ATCC8492  B<PERSON>uniformis   \n", "54             54          65.0        22.0    NaN   ATCC8492  B<PERSON>uniformis   \n", "20             20          20.0        14.0    NaN  ATCC25285   B.fragilis   \n", "..            ...           ...         ...    ...        ...          ...   \n", "96             96         119.0       106.0    NaN    NCM3722       E.coli   \n", "97             97         120.0       104.0    NaN    NCM3722       E.coli   \n", "98             98         121.0        86.0    NaN    NCM3722       E.coli   \n", "194           194           NaN         NaN    NaN    NCM3722       E.coli   \n", "90             90         113.0       131.0    NaN    NCM3722       E.coli   \n", "\n", "                                        experiment  \\\n", "182  av [25, 24, 23]_used_only: [59.0, 60.0, 61.0]   \n", "56                  analysis_stan_YCA_230712_2.5mM   \n", "55                  analysis_stan_YCA_230712_2.5mM   \n", "54                  analysis_stan_YCA_230712_2.5mM   \n", "20                  analysis_stan_YCA_230607_2.5mM   \n", "..                                             ...   \n", "96                                             NaN   \n", "97                                             NaN   \n", "98                                             NaN   \n", "194        av [162, 150]_used_only: [111.0, 113.0]   \n", "90                                             NaN   \n", "\n", "                        experiment_short                   exp_number  \\\n", "182                                   av  av_B.uniformis_ATCC8492_YCA   \n", "56        analysis_stan_YCA_230712_2.5mM                           23   \n", "55        analysis_stan_YCA_230712_2.5mM                           24   \n", "54        analysis_stan_YCA_230712_2.5mM                           25   \n", "20        analysis_stan_YCA_230607_2.5mM                           17   \n", "..                                   ...                          ...   \n", "96         analysis_stan_eplus_Oct22.csv                          150   \n", "97              analysis_stan_simple.csv                          146   \n", "98   analysis_stan_eplus_wotrp_Oct22.csv                          112   \n", "194                                   av    av_E.coli_NCM3722_epsilon   \n", "90                      stan_BHI_30Dec22                          187   \n", "\n", "      medium  ... succinate_std  lactate_std  butyrate_std formate_std  \\\n", "182      YCA  ...  5.682633e-16     0.417771      0.016282    0.112282   \n", "56       YCA  ...           NaN          NaN           NaN         NaN   \n", "55       YCA  ...           NaN          NaN           NaN         NaN   \n", "54       YCA  ...           NaN          NaN           NaN         NaN   \n", "20       YCA  ...           NaN          NaN           NaN         NaN   \n", "..       ...  ...           ...          ...           ...         ...   \n", "96   epsilon  ...           NaN          NaN           NaN         NaN   \n", "97     gamma  ...           NaN          NaN           NaN         NaN   \n", "98     gamma  ...           NaN          NaN           NaN         NaN   \n", "194  epsilon  ...  5.784812e-01     0.632041      0.000000    0.371618   \n", "90       BHI  ...           NaN          NaN           NaN         NaN   \n", "\n", "    maltose_std total_ferm  total_ferm_std total_uptake  total_uptake_std  \\\n", "182    0.000000  19.522489        0.447882    -6.650701          0.217517   \n", "56          NaN        NaN             NaN          NaN               NaN   \n", "55          NaN        NaN             NaN          NaN               NaN   \n", "54          NaN        NaN             NaN          NaN               NaN   \n", "20          NaN        NaN             NaN          NaN               NaN   \n", "..          ...        ...             ...          ...               ...   \n", "96          NaN        NaN             NaN          NaN               NaN   \n", "97          NaN        NaN             NaN          NaN               NaN   \n", "98          NaN        NaN             NaN          NaN               NaN   \n", "194    0.028349  20.556771        0.795447    -5.493158          0.613745   \n", "90          NaN        NaN             NaN          NaN               NaN   \n", "\n", "     growth_rate_std  \n", "182         0.048445  \n", "56               NaN  \n", "55               NaN  \n", "54               NaN  \n", "20               NaN  \n", "..               ...  \n", "96               NaN  \n", "97               NaN  \n", "98               NaN  \n", "194         0.508830  \n", "90               NaN  \n", "\n", "[211 rows x 65 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["list output\n", "[]\n", "['av_B.vulgatus_DSM 1447_BHI', 'av_B.fragilis_ATCC25285_BHI', 'av_B.ovatus_ATCC8483_BHI', 'av_B.theta_ATCC29148_BHI', 'av_B.finegoldii_HM-727_BHI', 'av_P<PERSON>distastonis_HM-169_BHI', 'av_E.rectale_ATCC33656_BHI', 'av_R.intestinalis_DSM14610_BHI', 'av_F.pra<PERSON>nitzii_DSM17677_BHI', 'av_B.longum_DSM20219_BHI', 'av_B.adolescentis_DSM20083_BHI', 'av_C.aerofaciens_DSM3979_BHI', 'av_E.coli_NCM3722_BHI']\n", "[]\n", "['av_B.vulgatus_DSM 1447_YCA', 'av_B.fragilis_ATCC25285_YCA', 'av_B.ovatus_ATCC8483_YCA', 'av_B.theta_ATCC29148_YCA', 'av_B.finegoldii_HM-727_YCA', 'av_B.uniformis_ATCC8492_YCA', 'av_P.copri_DSM18205_YCA', 'av_P.distastonis_HM-169_YCA', 'av_E.rectale_ATCC33656_YCA', 'av_R.intestinalis_DSM14610_YCA', 'av_F.pra<PERSON>ii_DSM17677_YCA', 'av_R.bromii_ATCC27255_YCA', 'av_B.longum_DSM20219_YCA', 'av_B.adolescentis_DSM20083_YCA', 'av_C.aerofaciens_DSM3979_YCA', 'av_E.coli_NCM3722_YCA']\n", "['av_B.vulgatus_DSM 1447_YCA', 'av_B.fragilis_ATCC25285_YCA', 'av_B.ovatus_ATCC8483_YCA', 'av_B.theta_ATCC29148_YCA', 'av_B.finegoldii_HM-727_YCA', 'av_B.uniformis_ATCC8492_YCA', 'av_P.copri_DSM18205_YCA', 'av_P.distastonis_HM-169_YCA', 'av_E.rectale_ATCC33656_YCA', 'av_R.intestinalis_DSM14610_YCA', 'av_F.pra<PERSON>ii_DSM17677_YCA', 'av_R.bromii_ATCC27255_YCA', 'av_B.longum_DSM20219_YCA', 'av_B.adolescentis_DSM20083_YCA', 'av_C.aerofaciens_DSM3979_YCA', 'av_E.coli_NCM3722_YCA']\n"]}], "source": ["###################\n", "#decide what to plot\n", "####################\n", "\n", "#use a table with average values  (to generate this table, run Final_analysis_hplcdata.ipynb\n", "dataout_av=pd.read_csv(\"data_hplc/analysis_out_av.csv\")\n", "display(dataout_av.head())\n", "display(speciesinformation.head())\n", "\n", "#sort list manually \n", "sorter=[\"B.vulgatus\",\"<PERSON>.fragilis\",\"<PERSON>.ovatus\",\"B.theta\",\"<PERSON><PERSON>ii\",\"<PERSON>.uniformis\",'<PERSON>.copri','<PERSON><PERSON>distastonis',\"<PERSON>.rectale\",\"<PERSON>.intestinalis\",\"<PERSON><PERSON>ii\",\"<PERSON>.bromii\",\"<PERSON>.longum\",\"B.adolescentis\",\"C.aerofaciens\",\"E.coli\",\"E.coliI\",\"E.coliII\",\"Fecal\",\"FecalPP\",\"ECOR\",\"E.halli\"]\n", "\n", "sorter=[]\n", "sorter=sorter+[\"<PERSON>.uniformis\",\"<PERSON>.fragilis\",\"<PERSON>.ovatus\",\"B.theta\",\"<PERSON><PERSON>ii\"]\n", "sorter=sorter+[\"B.vulgatus\"]\n", "sorter=sorter+[\"P.copri\"]\n", "sorter=sorter+[\"<PERSON><PERSON>distastonis\"]\n", "sorter=sorter+[\"R.intestinalis\",\"E.rectale\",\"L.eligens\",\"D.longicatena\",\"F.saccharivorans\",\"<PERSON><PERSON>i\",\"B.hydrogenotrophica\"]\n", "sorter=sorter+[\"<PERSON><PERSON>ii\",\"<PERSON>.siraeum\",\"<PERSON>.bromii\"]\n", "sorter=sorter+[\"<PERSON><PERSON>longum\",\"<PERSON>.adolescentis\"]\n", "sorter=sorter+[\"C.aerofaciens\"]\n", "sorter=sorter+[\"E.coli\",\"E.coliI\",\"E.coliII\",\"Fecal\",\"FecalPP\",\"ECOR\"]\n", "\n", "\n", "dataout_av.sort_values(by=\"species\", key=lambda column: column.map(lambda e: sorter.index(e)), inplace=True)\n", "\n", "display(dataout_av)\n", "\n", "speciesall=[\"<PERSON>.vulgatus\",\"<PERSON>.fragilis\",\"<PERSON>.ovatus\",\"B.theta\",\"<PERSON>.<PERSON>ii\",\"<PERSON>.uniformis\",'<PERSON>.copri','<PERSON><PERSON>distastonis',\"<PERSON>.rectale\",\"<PERSON>.intestinalis\",\"<PERSON><PERSON>ii\",\"<PERSON>.bromii\",\"<PERSON>.longum\",\"<PERSON>.adolescentis\",\"C.aerofaciens\",\"E.coli\"]\n", "\n", "samplenamelist=[[],[],[],[]]\n", "for species in speciesall:\n", "    mc=-1\n", "    for medium in [\"e\",\"BHI\",\"simple\",\"YCA\"]:\n", "            mc=mc+1\n", "            selectc=dataout_av.loc[(dataout_av[\"experiment_short\"]==\"av\") & (dataout_av[\"species\"]==species) & (dataout_av[\"medium\"]==medium)]\n", "            #print(selectc)\n", "            for il in range(0,selectc.shape[0]):\n", "                samplenamelist[mc].append(selectc[\"exp_number\"].iloc[il])\n", "print(\"list output\")\n", "for mc in range(0,len(samplenamelist)):\n", "    print(samplenamelist[mc])\n", "\n", "print(samplenamelist[3])\n", "\n", "sublistshort=['glu','mal','ace','but','for','lac','pro','suc']\n", "\n", "#tol bright https://personal.sron.nl/~pault/#sec:qualitative, glucose added as black\n", "colorlist=['#5d5d5d','#BBBBBB','#4477AA', '#EE6677', '#228833', '#CCBB44', '#66CCEE', '#AA3377']\n", "#colorlist=['#dd5129', '#1e8b99', '#2c7591', '#85635d', '#34a28d', '#fab255', '#acb269', '#5db27d']\n", "#colorlist=['#dd5129', '#85635d', '#2c7591', '#34a28d', '#fab255','#5db27d', '#1e8b99','#acb269']\n", "sublist=['glucose','maltose','acetate','butyrate','formate','lactate','propionate','succinate'] #skipp ethanol here\n", "sublistshort=['glu','mal','ace','but','for','lac','pro','suc']\n", "\n", "\n", "\n", "markerlist=['s','h','v','^','<','>','d','o']\n", "energycontent=np.array([0.68,1.36,0.21,0.52,0.,.33,0.37,0.36]) #kcal/mmol #the energy per mm for different fermentation products3\n", "cfactorlist=[6,12,2,4,1,3,3,4]\n", "markerlist=['s','v','^','<','>','d','o','h']\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# plot repeats ets for one example with chromotograms..."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0.2</th>\n", "      <th>Unnamed: 0.1</th>\n", "      <th>Unnamed: 0</th>\n", "      <th>notes</th>\n", "      <th>strain</th>\n", "      <th>species</th>\n", "      <th>experiment</th>\n", "      <th>experiment_short</th>\n", "      <th>exp_number</th>\n", "      <th>medium</th>\n", "      <th>...</th>\n", "      <th>succinate_std</th>\n", "      <th>lactate_std</th>\n", "      <th>butyrate_std</th>\n", "      <th>formate_std</th>\n", "      <th>maltose_std</th>\n", "      <th>total_ferm</th>\n", "      <th>total_ferm_std</th>\n", "      <th>total_uptake</th>\n", "      <th>total_uptake_std</th>\n", "      <th>growth_rate_std</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>29.0</td>\n", "      <td>NaN</td>\n", "      <td>DSM20083</td>\n", "      <td><PERSON><PERSON>adolescent<PERSON></td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>33</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "      <td>45.0</td>\n", "      <td>NaN</td>\n", "      <td>DSM20083</td>\n", "      <td><PERSON><PERSON>adolescent<PERSON></td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>51</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>2.0</td>\n", "      <td>38.0</td>\n", "      <td>NaN</td>\n", "      <td>DSM20083</td>\n", "      <td><PERSON><PERSON>adolescent<PERSON></td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>42</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>3.0</td>\n", "      <td>143.0</td>\n", "      <td>NaN</td>\n", "      <td>DSM20083</td>\n", "      <td><PERSON><PERSON>adolescent<PERSON></td>\n", "      <td>NaN</td>\n", "      <td>stan_BHI_30Dec22</td>\n", "      <td>199</td>\n", "      <td>BHI</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>4.0</td>\n", "      <td>142.0</td>\n", "      <td>NaN</td>\n", "      <td>DSM20083</td>\n", "      <td><PERSON><PERSON>adolescent<PERSON></td>\n", "      <td>NaN</td>\n", "      <td>stan_BHI_30Dec22</td>\n", "      <td>198</td>\n", "      <td>BHI</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 65 columns</p>\n", "</div>"], "text/plain": ["   Unnamed: 0.2  Unnamed: 0.1  Unnamed: 0  notes    strain         species  \\\n", "0             0           0.0        29.0    NaN  DSM20083  <PERSON><PERSON><PERSON><PERSON>   \n", "1             1           1.0        45.0    NaN  DSM20083  <PERSON><PERSON><PERSON><PERSON>   \n", "2             2           2.0        38.0    NaN  DSM20083  <PERSON><PERSON><PERSON><PERSON>   \n", "3             3           3.0       143.0    NaN  DSM20083  <PERSON><PERSON><PERSON><PERSON>   \n", "4             4           4.0       142.0    NaN  DSM20083  <PERSON><PERSON><PERSON><PERSON>   \n", "\n", "                       experiment                experiment_short exp_number  \\\n", "0  analysis_Zur_Newruns_2023_June  analysis_Zur_Newruns_2023_June         33   \n", "1  analysis_Zur_Newruns_2023_June  analysis_Zur_Newruns_2023_June         51   \n", "2  analysis_Zur_Newruns_2023_June  analysis_Zur_Newruns_2023_June         42   \n", "3                             NaN                stan_BHI_30Dec22        199   \n", "4                             NaN                stan_BHI_30Dec22        198   \n", "\n", "  medium  ... succinate_std  lactate_std  butyrate_std formate_std  \\\n", "0    YCA  ...           NaN          NaN           NaN         NaN   \n", "1    YCA  ...           NaN          NaN           NaN         NaN   \n", "2    YCA  ...           NaN          NaN           NaN         NaN   \n", "3    BHI  ...           NaN          NaN           NaN         NaN   \n", "4    BHI  ...           NaN          NaN           NaN         NaN   \n", "\n", "  maltose_std total_ferm  total_ferm_std total_uptake  total_uptake_std  \\\n", "0         NaN        NaN             NaN          NaN               NaN   \n", "1         NaN        NaN             NaN          NaN               NaN   \n", "2         NaN        NaN             NaN          NaN               NaN   \n", "3         NaN        NaN             NaN          NaN               NaN   \n", "4         NaN        NaN             NaN          NaN               NaN   \n", "\n", "   growth_rate_std  \n", "0              NaN  \n", "1              NaN  \n", "2              NaN  \n", "3              NaN  \n", "4              NaN  \n", "\n", "[5 rows x 65 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>species_HPLCname</th>\n", "      <th>species</th>\n", "      <th>new_species</th>\n", "      <th>species.1</th>\n", "      <th>species_short</th>\n", "      <th>new_genus</th>\n", "      <th>genus</th>\n", "      <th>new_family</th>\n", "      <th>family</th>\n", "      <th>new_order</th>\n", "      <th>order</th>\n", "      <th>new_class</th>\n", "      <th>class</th>\n", "      <th>new_phylum</th>\n", "      <th>phylum</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON>.uniformis</td>\n", "      <td>Bacteroides uniformis</td>\n", "      <td>Bacteroides uniformis</td>\n", "      <td>Bacteroides uniformis</td>\n", "      <td><PERSON>. uniformis</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON><PERSON>fragilis</td>\n", "      <td>Bacteroides fragilis</td>\n", "      <td>Bacteroides fragilis</td>\n", "      <td>Bacteroides fragilis</td>\n", "      <td><PERSON><PERSON> fragilis</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON><PERSON>ovatus</td>\n", "      <td>Bacteroides ovatus</td>\n", "      <td>Bacteroides ovatus</td>\n", "      <td>Bacteroides ovatus</td>\n", "      <td><PERSON><PERSON> ovatus</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON>.theta</td>\n", "      <td>Bacteroides thetaiotaomicron</td>\n", "      <td>Bacteroides thetaiotaomicron</td>\n", "      <td>Bacteroides thetaiotaomicron</td>\n", "      <td><PERSON>. theta</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Bacteroides <PERSON>ii</td>\n", "      <td>Bacteroides <PERSON>ii</td>\n", "      <td>Bacteroides <PERSON>ii</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidota</td>\n", "      <td>Bacteroidetes</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  species_HPLCname                       species  \\\n", "0      B.uniformis         Bacteroides uniformis   \n", "1       B.fragilis          Bacteroides fragilis   \n", "2         B.ovatus            Bacteroides ovatus   \n", "3          B.theta  Bacteroides thetaiotaomicron   \n", "4     <PERSON><PERSON>finegoldii        Bacteroides finegoldii   \n", "\n", "                    new_species                     species.1  species_short  \\\n", "0         Bacteroides uniformis         Bacteroides uniformis   B. uniformis   \n", "1          Bacteroides fragilis          Bacteroides fragilis    B. fragilis   \n", "2            Bacteroides ovatus            Bacteroides ovatus      B. ovatus   \n", "3  Bacteroides thetaiotaomicron  Bacteroides thetaiotaomicron       B. theta   \n", "4        Bacteroides finegoldii        Bacteroides finegoldii  B<PERSON> finegoldii   \n", "\n", "     new_genus        genus      new_family          family      new_order  \\\n", "0  Bacteroides  Bacteroides  Bacteroidaceae  Bacteroidaceae  Bacteroidales   \n", "1  Bacteroides  Bacteroides  Bacteroidaceae  Bacteroidaceae  Bacteroidales   \n", "2  Bacteroides  Bacteroides  Bacteroidaceae  Bacteroidaceae  Bacteroidales   \n", "3  Bacteroides  Bacteroides  Bacteroidaceae  Bacteroidaceae  Bacteroidales   \n", "4  Bacteroides  Bacteroides  Bacteroidaceae  Bacteroidaceae  Bacteroidales   \n", "\n", "           order    new_class        class    new_phylum         phylum  \n", "0  Bacteroidales  Bacteroidia  Bacteroidia  Bacteroidota  Bacteroidetes  \n", "1  Bacteroidales  Bacteroidia  Bacteroidia  Bacteroidota  Bacteroidetes  \n", "2  Bacteroidales  Bacteroidia  Bacteroidia  Bacteroidota  Bacteroidetes  \n", "3  Bacteroidales  Bacteroidia  Bacteroidia  Bacteroidota  Bacteroidetes  \n", "4  Bacteroidales  Bacteroidia  Bacteroidia  Bacteroidota  Bacteroidetes  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0.2</th>\n", "      <th>Unnamed: 0.1</th>\n", "      <th>Unnamed: 0</th>\n", "      <th>notes</th>\n", "      <th>strain</th>\n", "      <th>species</th>\n", "      <th>experiment</th>\n", "      <th>experiment_short</th>\n", "      <th>exp_number</th>\n", "      <th>medium</th>\n", "      <th>...</th>\n", "      <th>succinate_std</th>\n", "      <th>lactate_std</th>\n", "      <th>butyrate_std</th>\n", "      <th>formate_std</th>\n", "      <th>maltose_std</th>\n", "      <th>total_ferm</th>\n", "      <th>total_ferm_std</th>\n", "      <th>total_uptake</th>\n", "      <th>total_uptake_std</th>\n", "      <th>growth_rate_std</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>182</th>\n", "      <td>182</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>ATCC8492</td>\n", "      <td><PERSON>.uniformis</td>\n", "      <td>av [25, 24, 23]_used_only: [59.0, 60.0, 61.0]</td>\n", "      <td>av</td>\n", "      <td>av_B.uniformis_ATCC8492_YCA</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>5.682633e-16</td>\n", "      <td>0.417771</td>\n", "      <td>0.016282</td>\n", "      <td>0.112282</td>\n", "      <td>0.000000</td>\n", "      <td>19.522489</td>\n", "      <td>0.447882</td>\n", "      <td>-6.650701</td>\n", "      <td>0.217517</td>\n", "      <td>0.048445</td>\n", "    </tr>\n", "    <tr>\n", "      <th>56</th>\n", "      <td>56</td>\n", "      <td>67.0</td>\n", "      <td>20.0</td>\n", "      <td>NaN</td>\n", "      <td>ATCC8492</td>\n", "      <td><PERSON>.uniformis</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>23</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55</th>\n", "      <td>55</td>\n", "      <td>66.0</td>\n", "      <td>21.0</td>\n", "      <td>NaN</td>\n", "      <td>ATCC8492</td>\n", "      <td><PERSON>.uniformis</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>24</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>54</td>\n", "      <td>65.0</td>\n", "      <td>22.0</td>\n", "      <td>NaN</td>\n", "      <td>ATCC8492</td>\n", "      <td><PERSON>.uniformis</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>25</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>20</td>\n", "      <td>20.0</td>\n", "      <td>14.0</td>\n", "      <td>NaN</td>\n", "      <td>ATCC25285</td>\n", "      <td><PERSON><PERSON>fragilis</td>\n", "      <td>analysis_stan_YCA_230607_2.5mM</td>\n", "      <td>analysis_stan_YCA_230607_2.5mM</td>\n", "      <td>17</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>96</td>\n", "      <td>119.0</td>\n", "      <td>106.0</td>\n", "      <td>NaN</td>\n", "      <td>NCM3722</td>\n", "      <td><PERSON><PERSON>coli</td>\n", "      <td>NaN</td>\n", "      <td>analysis_stan_eplus_Oct22.csv</td>\n", "      <td>150</td>\n", "      <td>epsilon</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>97</th>\n", "      <td>97</td>\n", "      <td>120.0</td>\n", "      <td>104.0</td>\n", "      <td>NaN</td>\n", "      <td>NCM3722</td>\n", "      <td><PERSON><PERSON>coli</td>\n", "      <td>NaN</td>\n", "      <td>analysis_stan_simple.csv</td>\n", "      <td>146</td>\n", "      <td>gamma</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>98</td>\n", "      <td>121.0</td>\n", "      <td>86.0</td>\n", "      <td>NaN</td>\n", "      <td>NCM3722</td>\n", "      <td><PERSON><PERSON>coli</td>\n", "      <td>NaN</td>\n", "      <td>analysis_stan_eplus_wotrp_Oct22.csv</td>\n", "      <td>112</td>\n", "      <td>gamma</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>194</th>\n", "      <td>194</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NCM3722</td>\n", "      <td><PERSON><PERSON>coli</td>\n", "      <td>av [162, 150]_used_only: [111.0, 113.0]</td>\n", "      <td>av</td>\n", "      <td>av_E.coli_NCM3722_epsilon</td>\n", "      <td>epsilon</td>\n", "      <td>...</td>\n", "      <td>5.784812e-01</td>\n", "      <td>0.632041</td>\n", "      <td>0.000000</td>\n", "      <td>0.371618</td>\n", "      <td>0.028349</td>\n", "      <td>20.556771</td>\n", "      <td>0.795447</td>\n", "      <td>-5.493158</td>\n", "      <td>0.613745</td>\n", "      <td>0.508830</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90</th>\n", "      <td>90</td>\n", "      <td>113.0</td>\n", "      <td>131.0</td>\n", "      <td>NaN</td>\n", "      <td>NCM3722</td>\n", "      <td><PERSON><PERSON>coli</td>\n", "      <td>NaN</td>\n", "      <td>stan_BHI_30Dec22</td>\n", "      <td>187</td>\n", "      <td>BHI</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>211 rows × 65 columns</p>\n", "</div>"], "text/plain": ["     Unnamed: 0.2  Unnamed: 0.1  Unnamed: 0  notes     strain      species  \\\n", "182           182           NaN         NaN    NaN   ATCC8492  B.uniformis   \n", "56             56          67.0        20.0    NaN   ATCC8492  <PERSON><PERSON>uniformis   \n", "55             55          66.0        21.0    NaN   ATCC8492  B<PERSON>uniformis   \n", "54             54          65.0        22.0    NaN   ATCC8492  B<PERSON>uniformis   \n", "20             20          20.0        14.0    NaN  ATCC25285   B.fragilis   \n", "..            ...           ...         ...    ...        ...          ...   \n", "96             96         119.0       106.0    NaN    NCM3722       E.coli   \n", "97             97         120.0       104.0    NaN    NCM3722       E.coli   \n", "98             98         121.0        86.0    NaN    NCM3722       E.coli   \n", "194           194           NaN         NaN    NaN    NCM3722       E.coli   \n", "90             90         113.0       131.0    NaN    NCM3722       E.coli   \n", "\n", "                                        experiment  \\\n", "182  av [25, 24, 23]_used_only: [59.0, 60.0, 61.0]   \n", "56                  analysis_stan_YCA_230712_2.5mM   \n", "55                  analysis_stan_YCA_230712_2.5mM   \n", "54                  analysis_stan_YCA_230712_2.5mM   \n", "20                  analysis_stan_YCA_230607_2.5mM   \n", "..                                             ...   \n", "96                                             NaN   \n", "97                                             NaN   \n", "98                                             NaN   \n", "194        av [162, 150]_used_only: [111.0, 113.0]   \n", "90                                             NaN   \n", "\n", "                        experiment_short                   exp_number  \\\n", "182                                   av  av_B.uniformis_ATCC8492_YCA   \n", "56        analysis_stan_YCA_230712_2.5mM                           23   \n", "55        analysis_stan_YCA_230712_2.5mM                           24   \n", "54        analysis_stan_YCA_230712_2.5mM                           25   \n", "20        analysis_stan_YCA_230607_2.5mM                           17   \n", "..                                   ...                          ...   \n", "96         analysis_stan_eplus_Oct22.csv                          150   \n", "97              analysis_stan_simple.csv                          146   \n", "98   analysis_stan_eplus_wotrp_Oct22.csv                          112   \n", "194                                   av    av_E.coli_NCM3722_epsilon   \n", "90                      stan_BHI_30Dec22                          187   \n", "\n", "      medium  ... succinate_std  lactate_std  butyrate_std formate_std  \\\n", "182      YCA  ...  5.682633e-16     0.417771      0.016282    0.112282   \n", "56       YCA  ...           NaN          NaN           NaN         NaN   \n", "55       YCA  ...           NaN          NaN           NaN         NaN   \n", "54       YCA  ...           NaN          NaN           NaN         NaN   \n", "20       YCA  ...           NaN          NaN           NaN         NaN   \n", "..       ...  ...           ...          ...           ...         ...   \n", "96   epsilon  ...           NaN          NaN           NaN         NaN   \n", "97     gamma  ...           NaN          NaN           NaN         NaN   \n", "98     gamma  ...           NaN          NaN           NaN         NaN   \n", "194  epsilon  ...  5.784812e-01     0.632041      0.000000    0.371618   \n", "90       BHI  ...           NaN          NaN           NaN         NaN   \n", "\n", "    maltose_std total_ferm  total_ferm_std total_uptake  total_uptake_std  \\\n", "182    0.000000  19.522489        0.447882    -6.650701          0.217517   \n", "56          NaN        NaN             NaN          NaN               NaN   \n", "55          NaN        NaN             NaN          NaN               NaN   \n", "54          NaN        NaN             NaN          NaN               NaN   \n", "20          NaN        NaN             NaN          NaN               NaN   \n", "..          ...        ...             ...          ...               ...   \n", "96          NaN        NaN             NaN          NaN               NaN   \n", "97          NaN        NaN             NaN          NaN               NaN   \n", "98          NaN        NaN             NaN          NaN               NaN   \n", "194    0.028349  20.556771        0.795447    -5.493158          0.613745   \n", "90          NaN        NaN             NaN          NaN               NaN   \n", "\n", "     growth_rate_std  \n", "182         0.048445  \n", "56               NaN  \n", "55               NaN  \n", "54               NaN  \n", "20               NaN  \n", "..               ...  \n", "96               NaN  \n", "97               NaN  \n", "98               NaN  \n", "194         0.508830  \n", "90               NaN  \n", "\n", "[211 rows x 65 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["['YCA']\n", "list output\n", "['av_B.uniformis_ATCC8492_YCA', 'av_B.fragilis_ATCC25285_YCA', 'av_B.ovatus_ATCC8483_YCA', 'av_B.theta_ATCC29148_YCA', 'av_B.finegoldii_HM-727_YCA', 'av_B.vulgatus_DSM 1447_YCA', 'av_P.copri_DSM18205_YCA', 'av_P.distastonis_HM-169_YCA', 'av_R.intestinalis_DSM14610_YCA', 'av_E.rectale_ATCC33656_YCA', 'av_L.eligens_L.eligens_YCA', 'av_D.longicatena_D.longicatena_YCA', 'av_F.saccharivorans_F.saccharivorans_YCA', 'av_B.wexleri_B.wexleri_YCA', 'av_B.hydrogenotrophica_B.hydrogenotrophica_YCA', 'av_F.p<PERSON>ii_DSM17677_YCA']\n"]}], "source": ["###################\n", "#decide what to plot\n", "####################\n", "\n", "#use a table with average values  (to generate this table, run Final_analysis_hplcdata.ipynb\n", "dataout_av=pd.read_csv(\"data_hplc/analysis_out_av.csv\")\n", "display(dataout_av.head())\n", "display(speciesinformation.head())\n", "\n", "#sort list manually \n", "\n", "\n", "sorter=[\"B.vulgatus\",\"<PERSON>.fragilis\",\"<PERSON>.ovatus\",\"B.theta\",\"<PERSON><PERSON>ii\",\"<PERSON>.uniformis\",'<PERSON>.copri','<PERSON><PERSON>distastonis',\"<PERSON>.rectale\",\"<PERSON>.intestinalis\",\"<PERSON><PERSON>ii\",\"<PERSON>.bromii\",\"<PERSON>.longum\",\"B.adolescentis\",\"C.aerofaciens\",\"E.coli\",\"E.coliI\",\"E.coliII\",\"Fecal\",\"FecalPP\",\"ECOR\",\"E.halli\"]\n", "\n", "sorter=[]\n", "sorter=sorter+[\"<PERSON>.uniformis\",\"<PERSON>.fragilis\",\"<PERSON>.ovatus\",\"B.theta\",\"<PERSON><PERSON>ii\"]\n", "sorter=sorter+[\"B.vulgatus\"]\n", "sorter=sorter+[\"P.copri\"]\n", "sorter=sorter+[\"<PERSON><PERSON>distastonis\"]\n", "sorter=sorter+[\"R.intestinalis\",\"E.rectale\",\"L.eligens\",\"D.longicatena\",\"F.saccharivorans\",\"<PERSON><PERSON>i\",\"B.hydrogenotrophica\"]\n", "sorter=sorter+[\"<PERSON><PERSON>ii\",\"<PERSON>.siraeum\",\"<PERSON>.bromii\"]\n", "sorter=sorter+[\"<PERSON><PERSON>longum\",\"<PERSON>.adolescentis\"]\n", "sorter=sorter+[\"C.aerofaciens\"]\n", "sorter=sorter+[\"E.coli\",\"E.coliI\",\"E.coliII\",\"Fecal\",\"FecalPP\",\"ECOR\"]\n", "\n", "dataout_av.sort_values(by=\"species\", key=lambda column: column.map(lambda e: sorter.index(e)), inplace=True)\n", "\n", "display(dataout_av)\n", "\n", "#speciesall=speciesinformation[\"species_HPLCname\"]\n", "speciesall=sorter[:16]\n", "\n", "media=[\"YCA\"]\n", "#'BHI', 'YCA', 'epsilon plus', 'simple', 'epsilon', 'fecal\n", "print(media)\n", "samplenamelist=[]\n", "for m in media:\n", "    samplenamelist.append([])\n", "\n", "for species in speciesall:\n", "    mc=-1\n", "    for medium in media:\n", "            mc=mc+1\n", "            selectc=dataout_av.loc[(dataout_av[\"experiment_short\"]==\"av\") & (dataout_av[\"species\"]==species) & (dataout_av[\"medium\"]==medium)]\n", "            #print(selectc)\n", "            for il in range(0,selectc.shape[0]):\n", "                samplenamelist[mc].append(selectc[\"exp_number\"].iloc[il])\n", "            if selectc.shape[0]>1:\n", "                print(\"error - more than one entry\")\n", "                display( selectc)\n", "                error\n", "\n", "\n", "print(\"list output\")\n", "for mc in range(0,len(samplenamelist)):\n", "    print(samplenamelist[mc])\n", "\n", "\n", "\n", "   "]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["YCA\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0.2</th>\n", "      <th>Unnamed: 0.1</th>\n", "      <th>Unnamed: 0</th>\n", "      <th>notes</th>\n", "      <th>strain</th>\n", "      <th>species</th>\n", "      <th>experiment</th>\n", "      <th>experiment_short</th>\n", "      <th>exp_number</th>\n", "      <th>medium</th>\n", "      <th>...</th>\n", "      <th>succinate_std</th>\n", "      <th>lactate_std</th>\n", "      <th>butyrate_std</th>\n", "      <th>formate_std</th>\n", "      <th>maltose_std</th>\n", "      <th>total_ferm</th>\n", "      <th>total_ferm_std</th>\n", "      <th>total_uptake</th>\n", "      <th>total_uptake_std</th>\n", "      <th>growth_rate_std</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>182</th>\n", "      <td>182</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>ATCC8492</td>\n", "      <td><PERSON>.uniformis</td>\n", "      <td>av [25, 24, 23]_used_only: [59.0, 60.0, 61.0]</td>\n", "      <td>av</td>\n", "      <td>av_B.uniformis_ATCC8492_YCA</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>5.682633e-16</td>\n", "      <td>0.417771</td>\n", "      <td>0.016282</td>\n", "      <td>0.112282</td>\n", "      <td>0.000000</td>\n", "      <td>19.522489</td>\n", "      <td>0.447882</td>\n", "      <td>-6.650701</td>\n", "      <td>0.217517</td>\n", "      <td>0.048445</td>\n", "    </tr>\n", "    <tr>\n", "      <th>56</th>\n", "      <td>56</td>\n", "      <td>67.0</td>\n", "      <td>20.0</td>\n", "      <td>NaN</td>\n", "      <td>ATCC8492</td>\n", "      <td><PERSON>.uniformis</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>23</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55</th>\n", "      <td>55</td>\n", "      <td>66.0</td>\n", "      <td>21.0</td>\n", "      <td>NaN</td>\n", "      <td>ATCC8492</td>\n", "      <td><PERSON>.uniformis</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>24</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>54</td>\n", "      <td>65.0</td>\n", "      <td>22.0</td>\n", "      <td>NaN</td>\n", "      <td>ATCC8492</td>\n", "      <td><PERSON>.uniformis</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>25</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>20</td>\n", "      <td>20.0</td>\n", "      <td>14.0</td>\n", "      <td>NaN</td>\n", "      <td>ATCC25285</td>\n", "      <td><PERSON><PERSON>fragilis</td>\n", "      <td>analysis_stan_YCA_230607_2.5mM</td>\n", "      <td>analysis_stan_YCA_230607_2.5mM</td>\n", "      <td>17</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>96</td>\n", "      <td>119.0</td>\n", "      <td>106.0</td>\n", "      <td>NaN</td>\n", "      <td>NCM3722</td>\n", "      <td><PERSON><PERSON>coli</td>\n", "      <td>NaN</td>\n", "      <td>analysis_stan_eplus_Oct22.csv</td>\n", "      <td>150</td>\n", "      <td>epsilon</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>97</th>\n", "      <td>97</td>\n", "      <td>120.0</td>\n", "      <td>104.0</td>\n", "      <td>NaN</td>\n", "      <td>NCM3722</td>\n", "      <td><PERSON><PERSON>coli</td>\n", "      <td>NaN</td>\n", "      <td>analysis_stan_simple.csv</td>\n", "      <td>146</td>\n", "      <td>gamma</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>98</td>\n", "      <td>121.0</td>\n", "      <td>86.0</td>\n", "      <td>NaN</td>\n", "      <td>NCM3722</td>\n", "      <td><PERSON><PERSON>coli</td>\n", "      <td>NaN</td>\n", "      <td>analysis_stan_eplus_wotrp_Oct22.csv</td>\n", "      <td>112</td>\n", "      <td>gamma</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>194</th>\n", "      <td>194</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NCM3722</td>\n", "      <td><PERSON><PERSON>coli</td>\n", "      <td>av [162, 150]_used_only: [111.0, 113.0]</td>\n", "      <td>av</td>\n", "      <td>av_E.coli_NCM3722_epsilon</td>\n", "      <td>epsilon</td>\n", "      <td>...</td>\n", "      <td>5.784812e-01</td>\n", "      <td>0.632041</td>\n", "      <td>0.000000</td>\n", "      <td>0.371618</td>\n", "      <td>0.028349</td>\n", "      <td>20.556771</td>\n", "      <td>0.795447</td>\n", "      <td>-5.493158</td>\n", "      <td>0.613745</td>\n", "      <td>0.508830</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90</th>\n", "      <td>90</td>\n", "      <td>113.0</td>\n", "      <td>131.0</td>\n", "      <td>NaN</td>\n", "      <td>NCM3722</td>\n", "      <td><PERSON><PERSON>coli</td>\n", "      <td>NaN</td>\n", "      <td>stan_BHI_30Dec22</td>\n", "      <td>187</td>\n", "      <td>BHI</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>211 rows × 65 columns</p>\n", "</div>"], "text/plain": ["     Unnamed: 0.2  Unnamed: 0.1  Unnamed: 0  notes     strain      species  \\\n", "182           182           NaN         NaN    NaN   ATCC8492  B.uniformis   \n", "56             56          67.0        20.0    NaN   ATCC8492  <PERSON><PERSON>uniformis   \n", "55             55          66.0        21.0    NaN   ATCC8492  B<PERSON>uniformis   \n", "54             54          65.0        22.0    NaN   ATCC8492  B<PERSON>uniformis   \n", "20             20          20.0        14.0    NaN  ATCC25285   B.fragilis   \n", "..            ...           ...         ...    ...        ...          ...   \n", "96             96         119.0       106.0    NaN    NCM3722       E.coli   \n", "97             97         120.0       104.0    NaN    NCM3722       E.coli   \n", "98             98         121.0        86.0    NaN    NCM3722       E.coli   \n", "194           194           NaN         NaN    NaN    NCM3722       E.coli   \n", "90             90         113.0       131.0    NaN    NCM3722       E.coli   \n", "\n", "                                        experiment  \\\n", "182  av [25, 24, 23]_used_only: [59.0, 60.0, 61.0]   \n", "56                  analysis_stan_YCA_230712_2.5mM   \n", "55                  analysis_stan_YCA_230712_2.5mM   \n", "54                  analysis_stan_YCA_230712_2.5mM   \n", "20                  analysis_stan_YCA_230607_2.5mM   \n", "..                                             ...   \n", "96                                             NaN   \n", "97                                             NaN   \n", "98                                             NaN   \n", "194        av [162, 150]_used_only: [111.0, 113.0]   \n", "90                                             NaN   \n", "\n", "                        experiment_short                   exp_number  \\\n", "182                                   av  av_B.uniformis_ATCC8492_YCA   \n", "56        analysis_stan_YCA_230712_2.5mM                           23   \n", "55        analysis_stan_YCA_230712_2.5mM                           24   \n", "54        analysis_stan_YCA_230712_2.5mM                           25   \n", "20        analysis_stan_YCA_230607_2.5mM                           17   \n", "..                                   ...                          ...   \n", "96         analysis_stan_eplus_Oct22.csv                          150   \n", "97              analysis_stan_simple.csv                          146   \n", "98   analysis_stan_eplus_wotrp_Oct22.csv                          112   \n", "194                                   av    av_E.coli_NCM3722_epsilon   \n", "90                      stan_BHI_30Dec22                          187   \n", "\n", "      medium  ... succinate_std  lactate_std  butyrate_std formate_std  \\\n", "182      YCA  ...  5.682633e-16     0.417771      0.016282    0.112282   \n", "56       YCA  ...           NaN          NaN           NaN         NaN   \n", "55       YCA  ...           NaN          NaN           NaN         NaN   \n", "54       YCA  ...           NaN          NaN           NaN         NaN   \n", "20       YCA  ...           NaN          NaN           NaN         NaN   \n", "..       ...  ...           ...          ...           ...         ...   \n", "96   epsilon  ...           NaN          NaN           NaN         NaN   \n", "97     gamma  ...           NaN          NaN           NaN         NaN   \n", "98     gamma  ...           NaN          NaN           NaN         NaN   \n", "194  epsilon  ...  5.784812e-01     0.632041      0.000000    0.371618   \n", "90       BHI  ...           NaN          NaN           NaN         NaN   \n", "\n", "    maltose_std total_ferm  total_ferm_std total_uptake  total_uptake_std  \\\n", "182    0.000000  19.522489        0.447882    -6.650701          0.217517   \n", "56          NaN        NaN             NaN          NaN               NaN   \n", "55          NaN        NaN             NaN          NaN               NaN   \n", "54          NaN        NaN             NaN          NaN               NaN   \n", "20          NaN        NaN             NaN          NaN               NaN   \n", "..          ...        ...             ...          ...               ...   \n", "96          NaN        NaN             NaN          NaN               NaN   \n", "97          NaN        NaN             NaN          NaN               NaN   \n", "98          NaN        NaN             NaN          NaN               NaN   \n", "194    0.028349  20.556771        0.795447    -5.493158          0.613745   \n", "90          NaN        NaN             NaN          NaN               NaN   \n", "\n", "     growth_rate_std  \n", "182         0.048445  \n", "56               NaN  \n", "55               NaN  \n", "54               NaN  \n", "20               NaN  \n", "..               ...  \n", "96               NaN  \n", "97               NaN  \n", "98               NaN  \n", "194         0.508830  \n", "90               NaN  \n", "\n", "[211 rows x 65 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0.2</th>\n", "      <th>Unnamed: 0.1</th>\n", "      <th>Unnamed: 0</th>\n", "      <th>notes</th>\n", "      <th>strain</th>\n", "      <th>species</th>\n", "      <th>experiment</th>\n", "      <th>experiment_short</th>\n", "      <th>exp_number</th>\n", "      <th>medium</th>\n", "      <th>...</th>\n", "      <th>succinate_std</th>\n", "      <th>lactate_std</th>\n", "      <th>butyrate_std</th>\n", "      <th>formate_std</th>\n", "      <th>maltose_std</th>\n", "      <th>total_ferm</th>\n", "      <th>total_ferm_std</th>\n", "      <th>total_uptake</th>\n", "      <th>total_uptake_std</th>\n", "      <th>growth_rate_std</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>56</th>\n", "      <td>56</td>\n", "      <td>67.0</td>\n", "      <td>20.0</td>\n", "      <td>NaN</td>\n", "      <td>ATCC8492</td>\n", "      <td><PERSON>.uniformis</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>23</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55</th>\n", "      <td>55</td>\n", "      <td>66.0</td>\n", "      <td>21.0</td>\n", "      <td>NaN</td>\n", "      <td>ATCC8492</td>\n", "      <td><PERSON>.uniformis</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>24</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>54</td>\n", "      <td>65.0</td>\n", "      <td>22.0</td>\n", "      <td>NaN</td>\n", "      <td>ATCC8492</td>\n", "      <td><PERSON>.uniformis</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>25</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>20</td>\n", "      <td>20.0</td>\n", "      <td>14.0</td>\n", "      <td>NaN</td>\n", "      <td>ATCC25285</td>\n", "      <td><PERSON><PERSON>fragilis</td>\n", "      <td>analysis_stan_YCA_230607_2.5mM</td>\n", "      <td>analysis_stan_YCA_230607_2.5mM</td>\n", "      <td>17</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>18</td>\n", "      <td>18.0</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>ATCC25285</td>\n", "      <td><PERSON><PERSON>fragilis</td>\n", "      <td>analysis_stan_YCA_230424_2.5mM</td>\n", "      <td>analysis_stan_YCA_230424_2.5mM</td>\n", "      <td>3</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79</th>\n", "      <td>79</td>\n", "      <td>102.0</td>\n", "      <td>30.0</td>\n", "      <td>NaN</td>\n", "      <td>DSM3979</td>\n", "      <td>C.aerofaciens</td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>34</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>76</th>\n", "      <td>76</td>\n", "      <td>99.0</td>\n", "      <td>43.0</td>\n", "      <td>NaN</td>\n", "      <td>DSM3979</td>\n", "      <td>C.aerofaciens</td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>49</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91</th>\n", "      <td>91</td>\n", "      <td>114.0</td>\n", "      <td>19.0</td>\n", "      <td>NaN</td>\n", "      <td>NCM3722</td>\n", "      <td><PERSON><PERSON>coli</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>analysis_stan_YCA_230712_2.5mM</td>\n", "      <td>22</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93</th>\n", "      <td>93</td>\n", "      <td>116.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NCM3722</td>\n", "      <td><PERSON><PERSON>coli</td>\n", "      <td>analysis_stan_YCA_230424_2.5mM</td>\n", "      <td>analysis_stan_YCA_230424_2.5mM</td>\n", "      <td>2</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95</th>\n", "      <td>95</td>\n", "      <td>118.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NCM3722</td>\n", "      <td><PERSON><PERSON>coli</td>\n", "      <td>analysis_stan_YCA_230424_2.5mM</td>\n", "      <td>analysis_stan_YCA_230424_2.5mM</td>\n", "      <td>1</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>83 rows × 65 columns</p>\n", "</div>"], "text/plain": ["    Unnamed: 0.2  Unnamed: 0.1  Unnamed: 0  notes     strain        species  \\\n", "56            56          67.0        20.0    NaN   ATCC8492    <PERSON><PERSON>uniformis   \n", "55            55          66.0        21.0    NaN   ATCC8492    B<PERSON>uniformis   \n", "54            54          65.0        22.0    NaN   ATCC8492    B<PERSON>uniformis   \n", "20            20          20.0        14.0    NaN  ATCC25285     B.fragilis   \n", "18            18          18.0         2.0    NaN  ATCC25285     B.fragilis   \n", "..           ...           ...         ...    ...        ...            ...   \n", "79            79         102.0        30.0    NaN    DSM3979  C.aerofaciens   \n", "76            76          99.0        43.0    NaN    DSM3979  C.aerofaciens   \n", "91            91         114.0        19.0    NaN    NCM3722         E.coli   \n", "93            93         116.0         1.0    NaN    NCM3722         E.coli   \n", "95            95         118.0         0.0    NaN    NCM3722         E.coli   \n", "\n", "                        experiment                experiment_short exp_number  \\\n", "56  analysis_stan_YCA_230712_2.5mM  analysis_stan_YCA_230712_2.5mM         23   \n", "55  analysis_stan_YCA_230712_2.5mM  analysis_stan_YCA_230712_2.5mM         24   \n", "54  analysis_stan_YCA_230712_2.5mM  analysis_stan_YCA_230712_2.5mM         25   \n", "20  analysis_stan_YCA_230607_2.5mM  analysis_stan_YCA_230607_2.5mM         17   \n", "18  analysis_stan_YCA_230424_2.5mM  analysis_stan_YCA_230424_2.5mM          3   \n", "..                             ...                             ...        ...   \n", "79  analysis_<PERSON>ur_Newruns_2023_June  analysis_Zur_Newruns_2023_June         34   \n", "76  analysis_<PERSON>ur_Newruns_2023_June  analysis_Zur_Newruns_2023_June         49   \n", "91  analysis_stan_YCA_230712_2.5mM  analysis_stan_YCA_230712_2.5mM         22   \n", "93  analysis_stan_YCA_230424_2.5mM  analysis_stan_YCA_230424_2.5mM          2   \n", "95  analysis_stan_YCA_230424_2.5mM  analysis_stan_YCA_230424_2.5mM          1   \n", "\n", "   medium  ... succinate_std  lactate_std  butyrate_std formate_std  \\\n", "56    YCA  ...           NaN          NaN           NaN         NaN   \n", "55    YCA  ...           NaN          NaN           NaN         NaN   \n", "54    YCA  ...           NaN          NaN           NaN         NaN   \n", "20    YCA  ...           NaN          NaN           NaN         NaN   \n", "18    YCA  ...           NaN          NaN           NaN         NaN   \n", "..    ...  ...           ...          ...           ...         ...   \n", "79    YCA  ...           NaN          NaN           NaN         NaN   \n", "76    YCA  ...           NaN          NaN           NaN         NaN   \n", "91    YCA  ...           NaN          NaN           NaN         NaN   \n", "93    YCA  ...           NaN          NaN           NaN         NaN   \n", "95    YCA  ...           NaN          NaN           NaN         NaN   \n", "\n", "   maltose_std total_ferm  total_ferm_std total_uptake  total_uptake_std  \\\n", "56         NaN        NaN             NaN          NaN               NaN   \n", "55         NaN        NaN             NaN          NaN               NaN   \n", "54         NaN        NaN             NaN          NaN               NaN   \n", "20         NaN        NaN             NaN          NaN               NaN   \n", "18         NaN        NaN             NaN          NaN               NaN   \n", "..         ...        ...             ...          ...               ...   \n", "79         NaN        NaN             NaN          NaN               NaN   \n", "76         NaN        NaN             NaN          NaN               NaN   \n", "91         NaN        NaN             NaN          NaN               NaN   \n", "93         NaN        NaN             NaN          NaN               NaN   \n", "95         NaN        NaN             NaN          NaN               NaN   \n", "\n", "    growth_rate_std  \n", "56              NaN  \n", "55              NaN  \n", "54              NaN  \n", "20              NaN  \n", "18              NaN  \n", "..              ...  \n", "79              NaN  \n", "76              NaN  \n", "91              NaN  \n", "93              NaN  \n", "95              NaN  \n", "\n", "[83 rows x 65 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0.2</th>\n", "      <th>Unnamed: 0.1</th>\n", "      <th>Unnamed: 0</th>\n", "      <th>notes</th>\n", "      <th>strain</th>\n", "      <th>species</th>\n", "      <th>experiment</th>\n", "      <th>experiment_short</th>\n", "      <th>exp_number</th>\n", "      <th>medium</th>\n", "      <th>...</th>\n", "      <th>succinate_std</th>\n", "      <th>lactate_std</th>\n", "      <th>butyrate_std</th>\n", "      <th>formate_std</th>\n", "      <th>maltose_std</th>\n", "      <th>total_ferm</th>\n", "      <th>total_ferm_std</th>\n", "      <th>total_uptake</th>\n", "      <th>total_uptake_std</th>\n", "      <th>growth_rate_std</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "      <td>45.0</td>\n", "      <td>NaN</td>\n", "      <td>DSM20083</td>\n", "      <td><PERSON><PERSON>adolescent<PERSON></td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>51</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>29.0</td>\n", "      <td>NaN</td>\n", "      <td>DSM20083</td>\n", "      <td><PERSON><PERSON>adolescent<PERSON></td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>33</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>2.0</td>\n", "      <td>38.0</td>\n", "      <td>NaN</td>\n", "      <td>DSM20083</td>\n", "      <td><PERSON><PERSON>adolescent<PERSON></td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>42</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3 rows × 65 columns</p>\n", "</div>"], "text/plain": ["   Unnamed: 0.2  Unnamed: 0.1  Unnamed: 0  notes    strain         species  \\\n", "1             1           1.0        45.0    NaN  DSM20083  <PERSON><PERSON><PERSON><PERSON>   \n", "0             0           0.0        29.0    NaN  DSM20083  <PERSON><PERSON><PERSON><PERSON>   \n", "2             2           2.0        38.0    NaN  DSM20083  <PERSON><PERSON><PERSON><PERSON>   \n", "\n", "                       experiment                experiment_short exp_number  \\\n", "1  analysis_Zur_Newruns_2023_June  analysis_Zur_Newruns_2023_June         51   \n", "0  analysis_Zur_Newruns_2023_June  analysis_Zur_Newruns_2023_June         33   \n", "2  analysis_Zur_Newruns_2023_June  analysis_Zur_Newruns_2023_June         42   \n", "\n", "  medium  ... succinate_std  lactate_std  butyrate_std formate_std  \\\n", "1    YCA  ...           NaN          NaN           NaN         NaN   \n", "0    YCA  ...           NaN          NaN           NaN         NaN   \n", "2    YCA  ...           NaN          NaN           NaN         NaN   \n", "\n", "  maltose_std total_ferm  total_ferm_std total_uptake  total_uptake_std  \\\n", "1         NaN        NaN             NaN          NaN               NaN   \n", "0         NaN        NaN             NaN          NaN               NaN   \n", "2         NaN        NaN             NaN          NaN               NaN   \n", "\n", "   growth_rate_std  \n", "1              NaN  \n", "0              NaN  \n", "2              NaN  \n", "\n", "[3 rows x 65 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["No artists with labels found to put in legend.  Note that artists whose label start with an underscore are ignored when legend() is called with no argument.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["********** Strain:\n", "DSM20083\n", "['0.089', ' 0.129', ' 0.189', ' 0.295', ' 0.437', ' 0.517', ' nan', ' nan', ' nan', ' nan', ' nan', ' nan', ' nan']\n", "['0.0', ' 0.516666667', ' 1.066666667', ' 1.716666667', ' 2.333333333', ' 2.766666667', ' nan', ' nan', ' nan', ' nan', ' nan', ' nan', ' nan']\n", "['0.055', ' 0.089', ' 0.112', ' 0.172', ' 0.21', ' 0.278', ' 0.393', ' 0.5', ' 0.563', ' nan', ' nan', ' nan', ' nan']\n", "['0.0', ' 0.516666667', ' 0.85', ' 1.533333333', ' 1.9', ' 2.333333333', ' 3.0', ' 3.366666667', ' 3.583333333', ' nan', ' nan', ' nan', ' nan']\n", "['0.141', ' 0.274', ' 0.3', ' 0.383', ' 0.433', ' 0.533', ' nan', ' nan', ' nan', ' nan', ' nan', ' nan', ' nan']\n", "['0.0', ' 0.766666667', ' 1.1', ' 1.433333333', ' 1.683333333', ' 1.85', ' nan', ' nan', ' nan', ' nan', ' nan', ' nan', ' nan']\n", "[[8.880348863401624, 6.983131802616277, 8.853173988433403], [-0.0, -0.0, -0.0], [10.558169713153124, 13.580201569555198, 12.2039671076068], [], [0.0, 2.6301725044379856, 0.3667206961814968], [2.9200860167654907, 3.4115187682358967, 3.3341547325481686], [1.1719558158329193, 1.698931976266007, 3.829331046265992], [0.196952591351974, 0.6259908449966409, 0.8108074198772997]]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x200 with 5 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x500 with 13 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 700x500 with 10 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "#plot concentration vs OD\n", "####################\n", "\n", "\n", "for medium in media:\n", "    print(medium)\n", "    display(dataout_av)\n", "    select=dataout_av.loc[(dataout_av[\"use_in_study\"]==\"yes\") & (dataout_av[\"experiment_short\"]!=\"av\") &  (dataout_av[\"medium\"]==medium) ]\n", "    display(select)\n", "\n", "    select=select.sort_values(by=[\"strain\"])\n", "\n", "    strains=select[\"strain\"].unique()\n", "    \n", "    strains=[\"DSM20083\"]\n", "    num_strains=len(strains)\n", "\n", "    if num_strains>0:\n", "        #for index,row in select.iterrows():\n", "\n", "        fontsize=12\n", "        fig, ax = plt.subplots(num_strains,5,figsize=(5*3,2*num_strains))  #for barplots yields/excretion\n", "        if num_strains==1:\n", "            fig, ax = plt.subplots(2,5,figsize=(5*3,2.5*2))  #for barplots yields/excretion\n", "        fig2, ax2 = plt.subplots(2,5,figsize=(5*1.4,2.5*2))  #for barplots yields/excretion\n", "        \n", "        axGR=ax2[1,0]\n", "        markerlistgr=[\"8\",\"P\",\"D\"]\n", "        linestylegr=[\"-\",\"--\",\":\"]\n", "        hatch=[\"//\",\"\\\\\\\\\",\"--\"]\n", "        iS=-1\n", "        for strain in strains:\n", "\n", "\n", "\n", "\n", "            FPvalues=[]\n", "            iS=iS+1\n", "            results=[[],[],[],[],[],[],[],[]]\n", "\n", "            select2=select.loc[select[\"strain\"]==strain]\n", "            display(select2)\n", "            specieslistcc=select2[\"species\"].tolist()[0]\n", "\n", "\n", "            print(\"********** Strain:\")\n", "            print(strain)\n", "            #display(select2)\n", "\n", "            gr=[]\n", "\n", "\n", "            axTW=[]\n", "            #go through all repeats of strains\n", "            iA=-1\n", "            for index,row in select2.iterrows():\n", "                iA=iA+1\n", "\n", "                #prepare plot\n", "                if iA<3:\n", "                    axTW.append(ax[iS,iA+2].twinx())\n", "                    if strain in [\"DSM17677\",\"ATCC27255\",\"DSM15702\"]:\n", "                        ax[iS,iA+2].set_ylabel(\"$\\Delta $ maltose (mM)\",fontsize=fontsize,color='#BBBBBB')\n", "                    else:\n", "                        ax[iS,iA+2].set_ylabel(\"$\\Delta $ glucose (mM)\",fontsize=fontsize,color='#5d5d5d')\n", "                    axTW[iA].set_ylabel(\"$\\Delta $ fermentation\\n product (mM)\",fontsize=fontsize)\n", "                    ax[iS,iA+2].set_xlabel(\"bacterial density (OD600)\",fontsize=fontsize)\n", "\n", "                    #plot OD\n", "                    gr.append(row[\"growth_rate\"])\n", "\n", "                    try:\n", "                        odcc=row[\"growthcurve_OD\"][1:-1].split(\",\")\n", "                        timecc=row[\"growthcurve_time\"][1:-1].split(\",\")\n", "                        print(odcc)\n", "                        print(timecc)\n", "                        odcc=np.array(odcc,dtype=float)\n", "                        timecc=np.array(timecc,dtype=float)\n", "\n", "                        odcc = odcc[~np.isnan(timecc)]\n", "                        timecc = timecc[~np.isnan(timecc)]\n", "                        timecc = timecc[~np.isnan(odcc)]\n", "                        odcc = odcc[~np.isnan(odcc)]\n", "\n", "                        fitgr=np.polyfit(timecc,np.log(odcc),1)\n", "                        odcc_range=np.linspace(0,np.nanmax(1.1*timecc),50)\n", "                        ax[iS,1].plot(odcc_range,np.exp(odcc_range*fitgr[0]+fitgr[1]),ls=linestylegr[iA],label=iA+1,color=\"k\",lw=1)\n", "                        ax[iS,1].set_yscale(\"log\")\n", "                        ax[iS,1].minorticks_off()\n", "                        ytickod=[0.04,0.08,0.16,0.32,0.64]\n", "                        ax[iS,1].set_yticks(ytickod)\n", "                        ax[iS,1].set_yticklabels(ytickod)\n", "                        ax[iS,1].plot(timecc,odcc,ls='',marker=markerlistgr[iA],color='k',fillstyle=\"none\")\n", "                    except:\n", "                        print(\"no growth rate data\")\n", "\n", "\n", "                #go through different substrates\n", "                ic=-1\n", "                for sub in sublist:\n", "                    #print(sub)\n", "                    ic=ic+1\n", "                    cfactor=cfactorlist[ic]\n", "                    color=colorlist[ic]\n", "                    label=sublistshort[ic]\n", "                    conversionfactor=1/0.5\n", "                    #print(\"OD\")\n", "                    #print(row[sub+\"_OD\"])\n", "\n", "\n", "\n", "                    #print(row[sub+\"_conc\"])\n", "                    if row[sub+\"_OD\"] in [\"nan\",np.nan]:\n", "                        pass\n", "                    else:\n", "                        odc=row[sub+\"_OD\"].replace(\"[\",\"\").replace(\"]\",\"\").replace(\"  \",\" \").replace(\"  \",\" \").strip().split(\" \")\n", "                        concc=row[sub+\"_conc\"].replace(\"[\",\"\").replace(\"]\",\"\").replace(\"  \",\" \").replace(\"  \",\" \").strip().split(\" \")\n", "                        odc=np.array(odc,dtype=float)\n", "                        concc=np.array(concc,dtype=float)\n", "                        odcval=np.linspace(0,1.1*np.nanmax(odc),10)\n", "                        m=row[sub]\n", "                        y0=row[sub+\"_y0\"]\n", "\n", "                        if sub not in [\"glucose\",\"maltose\"]:\n", "                            FPvalues.append(np.nanmax(concc)-concc.min())\n", "\n", "                        if iA<3:\n", "                            if sub in [\"glucose\",\"maltose\"]: # and (strain not in [\"DSM17677\",\"ATCC27255\"]):\n", "                                ax[iS,iA+2].plot(odc,concc-concc.min(),color=color,ls='',marker=markerlist[ic],fillstyle=\"none\")\n", "                                ax[iS,iA+2].plot(odcval,odcval*m+y0-concc.min(),color=color,label=label,ls='--')\n", "                                results[ic].append(-1*m)\n", "                            else:\n", "                                    axTW[iA].plot(odc,concc-concc.min(),color=color,ls='',marker=markerlist[ic],fillstyle=\"none\")\n", "                                    axTW[iA].plot(odcval,odcval*m+y0-concc.min(),color=color,label=label,ls='-')\n", "                                    results[ic].append(m)\n", "                            if ic==0:\n", "                                ax[iS,iA+2].set_title(\"Replicate \"+str(iA+1))\n", "\n", "\n", "            ax[iS,1].legend(title=\"replicate\")\n", "            print(results)\n", "\n", "            iA=-1\n", "            for index,row in select2.iterrows():\n", "                iA=iA+1\n", "                if iA<3:\n", "                    try:\n", "                        axTW[iA].set_ylim(-.2,1.1*max(FPvalues))\n", "                    except:\n", "                        pass\n", "                #if iA==0:\n", "                #    ax[0].set_yticklabels(tickl[::-1],fontsize=fontsize)\n", "            ic=-1\n", "            ic2=-1\n", "            for sub in sublist:\n", "                #print(sub)\n", "                ic=ic+1\n", "                if sub in [\"acetate\",\"formate\",\"lactate\",\"propionate\",\"succinate\"]:\n", "                        ic2=ic2+1\n", "                for iR in [0,1,2]:\n", "                    #print(results)\n", "                    if sub in [\"acetate\",\"formate\",\"lactate\",\"propionate\",\"succinate\"]:\n", "                        try:\n", "                            ax[iS,0].bar(ic2+iR*0.3,results[ic][iR],width=0.25,color=colorlist[ic],hatch=hatch[iR])\n", "                        except:\n", "                            #ax[iS,0].bar(ic+iR*0.3,results[ic][iR],width=0.25,color=colorlist[ic])\n", "                            pass\n", "                        #ax[iS,0].bar(ic+iR*0.3,results[ic][iR]\n", "                    if sub in [\"glucose\"]:\n", "                        try:\n", "                            ax2[1,1].bar(iR*0.3,results[ic][iR],width=0.25,color=colorlist[ic],hatch=hatch[iR])\n", "                        except:\n", "                            #ax[iS,0].bar(ic+iR*0.3,results[ic][iR],width=0.25,color=colorlist[ic])\n", "                            pass\n", "                        #ax[iS,0].bar(ic+iR*0.3,results[ic][iR]\n", "                \n", "            \n", "            ax2[1,0].set_ylabel(\"growth rate (1/h)\")\n", "            ax2[1,1].set_ylabel(\"glucose uptake (mM/OD600)\")\n", "            for iR in [0,1,2]:\n", "                    #print(results)\n", "                    try:\n", "                        ax2[1,0].bar(0+iR*0.3,gr[iR],width=0.25,color=\"gray\",hatch=hatch[iR],label=iR+1)\n", "                    except:\n", "                        #ax[iS,0].bar(ic+iR*0.3,results[ic][iR],width=0.25,color=colorlist[ic])\n", "                        pass\n", "            #axGR.legend(title=\"replicate\")\n", "            ax[iS,0].set_xlim(-0.3,5)\n", "            listxt=np.array(range(0,8))+0.3\n", "            ax[iS,0].set_xticks([1,2,3,4,5])\n", "            ax[iS,0].set_xticklabels([\"acetate\",\"formate\",\"lactate\",\"propionate\",\"succinate\"],rotation=45) # sublistshort\n", "            ax[iS,0].set_ylabel(\"excretion (mM/OD600)\")\n", "\n", "            axGR.legend(loc='upper center', bbox_to_anchor=(0.5, 1.5),ncol=3, fancybox=False, shadow=False,title=\"$\"+specieslistcc.replace(\".\",\". \")+\"$\"+\"; replicates\")\n", "\n", "            #ax[iS,1].set_xlim(-0.3,8)\n", "            #ax[iS,1].set_xticks(np.array(range(0,8))+0.3)\n", "            #ax[iS,1].set_xticklabels(sublistshort)\n", "            ax[iS,1].set_ylabel(\"bacterial density (OD600)\")\n", "            ax[iS,1].set_xlabel(\"time (h)\")\n", "        ax[0,0].legend()\n", "        plt.tight_layout()\n", "        fig.savefig(\"plot_output_figure1/illustration_method_Badolescentesexample.pdf\")\n", "        fig2.savefig(\"plot_output_figure1/illustration_method_Badolescentesexample2.pdf\")\n", "        plt.show()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# calibration curve example"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x500 with 10 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(2,5,figsize=(5*3,2*2.5))  #for barplots yields/excretion\n", "    \n", "        \n", "ic=-1\n", "for sub in sublist:\n", "    ic=ic+1\n", "    cfactor=cfactorlist[ic]\n", "    color=colorlist[ic]\n", "    label=sublistshort[ic]\n", "    curdata=pd.read_csv(\"data_hplc/example_calibration/calibrationYCA_Zurich_\"+sub+\"_2308.csv\")\n", "    #display(curdata)\n", "    if sub in [\"maltose\",\"butyrate\"]:\n", "        pass\n", "    else:\n", "        ax[0,0].plot(curdata[\"concentration\"],curdata[\"area\"],color=color,marker='o',ls='')\n", "        ax[0,0].set_xlabel(\"concentration (mm)\")\n", "        ax[0,0].set_ylabel(\"area under the curve \\n(mV min)\")\n", "\n", "        fit=np.polyfit(curdata[\"concentration\"],curdata[\"area\"],1)\n", "        x=np.linspace(0,20,20)\n", "        ax[0,0].plot(x,x*fit[0]+fit[1],ls='--',color=color)\n", "fig.tight_layout()\n", "fig.savefig(\"plot_output_figure1/illustration_method_hplccalibration.pdf\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Illustration for main figure panel 1b\n", "\n", "Plot secretion vs OD for one example"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0.2</th>\n", "      <th>Unnamed: 0.1</th>\n", "      <th>Unnamed: 0</th>\n", "      <th>notes</th>\n", "      <th>strain</th>\n", "      <th>species</th>\n", "      <th>experiment</th>\n", "      <th>experiment_short</th>\n", "      <th>exp_number</th>\n", "      <th>medium</th>\n", "      <th>...</th>\n", "      <th>succinate_std</th>\n", "      <th>lactate_std</th>\n", "      <th>butyrate_std</th>\n", "      <th>formate_std</th>\n", "      <th>maltose_std</th>\n", "      <th>total_ferm</th>\n", "      <th>total_ferm_std</th>\n", "      <th>total_uptake</th>\n", "      <th>total_uptake_std</th>\n", "      <th>growth_rate_std</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>50</td>\n", "      <td>61.0</td>\n", "      <td>39.0</td>\n", "      <td>NaN</td>\n", "      <td>ATCC29148</td>\n", "      <td><PERSON>.theta</td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>analysis_Zur_Newruns_2023_June</td>\n", "      <td>43</td>\n", "      <td>YCA</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 65 columns</p>\n", "</div>"], "text/plain": ["    Unnamed: 0.2  Unnamed: 0.1  Unnamed: 0  notes     strain  species  \\\n", "50            50          61.0        39.0    NaN  ATCC29148  B.theta   \n", "\n", "                        experiment                experiment_short exp_number  \\\n", "50  analysis_Zur_Newruns_2023_June  analysis_Zur_Newruns_2023_June         43   \n", "\n", "   medium  ... succinate_std  lactate_std  butyrate_std formate_std  \\\n", "50    YCA  ...           NaN          NaN           NaN         NaN   \n", "\n", "   maltose_std total_ferm  total_ferm_std total_uptake  total_uptake_std  \\\n", "50         NaN        NaN             NaN          NaN               NaN   \n", "\n", "    growth_rate_std  \n", "50              NaN  \n", "\n", "[1 rows x 65 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 450x200 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time_min</th>\n", "      <th>step</th>\n", "      <th>intensity_mV</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.016667</td>\n", "      <td>1.0</td>\n", "      <td>-0.076500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.033333</td>\n", "      <td>1.0</td>\n", "      <td>-0.077125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.050000</td>\n", "      <td>1.0</td>\n", "      <td>-0.077125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.066667</td>\n", "      <td>1.0</td>\n", "      <td>-0.077125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.083333</td>\n", "      <td>1.0</td>\n", "      <td>-0.076875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2395</th>\n", "      <td>39.933333</td>\n", "      <td>1.0</td>\n", "      <td>0.002125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2396</th>\n", "      <td>39.950000</td>\n", "      <td>1.0</td>\n", "      <td>0.002250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2397</th>\n", "      <td>39.966667</td>\n", "      <td>1.0</td>\n", "      <td>0.003250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2398</th>\n", "      <td>39.983333</td>\n", "      <td>1.0</td>\n", "      <td>0.003875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2399</th>\n", "      <td>40.000000</td>\n", "      <td>1.0</td>\n", "      <td>0.004750</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2400 rows × 3 columns</p>\n", "</div>"], "text/plain": ["       time_min  step  intensity_mV\n", "0      0.016667   1.0     -0.076500\n", "1      0.033333   1.0     -0.077125\n", "2      0.050000   1.0     -0.077125\n", "3      0.066667   1.0     -0.077125\n", "4      0.083333   1.0     -0.076875\n", "...         ...   ...           ...\n", "2395  39.933333   1.0      0.002125\n", "2396  39.950000   1.0      0.002250\n", "2397  39.966667   1.0      0.003250\n", "2398  39.983333   1.0      0.003875\n", "2399  40.000000   1.0      0.004750\n", "\n", "[2400 rows x 3 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAS8AAACRCAYAAACSaT5pAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/bCgiHAAAACXBIWXMAAA9hAAAPYQGoP6dpAAAo/ElEQVR4nO3deVhTV94H8G8Iu0qUNWHHBVQUUZhBLBXf6kBfa6njSGsXtdVadap2pOrUtirWtS64VqkVUNuqfSvodOpW+ypItbiwCBYVFBBQKCKUKDvJ7/2DyX0NYUnIhQQ4n+e5z2Nu7r3nd8D8OPfk3HMERERgGIbpYgx0HQDDMEx7sOTFMEyXxJIXwzBdEkteDMN0SSx5MQzTJRm256T6+noUFxejqqoKNjY2sLS05DsuhmGYVqnd8nr69Cm+/PJLjBs3DiKRCK6urhg6dChsbGzg4uKCOXPm4Nq1ax0ZK8MwDEegzjivbdu2Yd26dXB1dUVISAj+/Oc/w8HBAWZmZigrK8PNmzeRmJiI48ePY/To0di1axcGDRrUGfEzDNNDqZW8QkNDsXLlSgwfPrzV42praxEVFQVjY2O8++67vAXJMAzTlFrJi2EYRt+o3ec1efJk/Pjjj5DL5bwVHh4eDoFAoLSJxeJWz0lISICPjw9MTU3Rv39/REZG8hYPwzBdh9rJq7q6GpMnT4ajoyM+/vhjZGdn8xKAp6cnioqKuC0jI6PFY3NzczFx4kQ8//zzSE1Nxccff4xFixYhNjaWl1gYhuk61E5eZ8+eRV5eHubPn4//+Z//weDBgzF27FgcOnQI1dXV7Q7A0NAQYrGY22xsbFo8NjIyEs7Ozti+fTuGDBmCd999F7NmzcKWLVvaXT7DMF1Tu/u8Lly4gOjoaBw/fhxCoRDTpk3DrFmz4Ofnp/Y1wsPDsXnzZohEIpiYmMDPzw/r169H//79mz1+7NixGDlyJHbs2MHtO378OF599VVUVVXByMhI5Zza2lrU1tZyr+VyOcrKymBlZQWBQKBBjRmG0QYR4cmTJ7C3t4eBAQ/j40lLUqmUIiMjydLSkoRCoUbnnjp1io4dO0bp6el07tw5CgwMJDs7OyotLW32+EGDBtG6deuU9l26dIkA0MOHD5s9Z9WqVQSAbWxjm55sBQUFGuWJlrRrhL1CTk4ODhw4gAMHDqCiogITJkzQ6Pz//u//5v49fPhw+Pv7Y8CAATh48CDCwsKaPadpa4n+03BsqRW1fPlypWtVVFTA2dkZBQUFsLCw0ChehulsVVVVOHPmDAYPHoyhQ4fqOhytSKVSODk5oU+fPrxcT+PkVV1dje+//x4xMTG4ePEinJ2d8e677+Kdd96Bk5OTVsH06tULw4cPb/HLALFYjOLiYqV9JSUlMDQ0hJWVVbPnmJiYwMTERGW/hYUFS16M3nvjjTdw8uRJGBkZISkpCaNGjdJ1SFrjq7tG7RvPy5cvY86cORCLxZg7dy7EYjHOnj2L3NxcrFy5UuvEBTT2T926dQsSiaTZ9/39/XHu3DmlfT/99BN8fX2b7e9imK7s+vXrOHnyJIDG54nXrl2r44j0jLr3lwKBgLy9vWnXrl1UVlbGyz3rhx9+SPHx8ZSTk0NJSUk0adIk6tOnD+Xl5RER0UcffUTTp0/njs/JySFzc3NavHgxZWZmUlRUFBkZGdGxY8fULrOiooIAUEVFBS91YJiOsnTpUgJAnp6eBIBMTEyoqqpK12G1G9+fPbWTV3JyMi8FPuu1114jiURCRkZGZG9vT1OmTKHffvuNe3/mzJkUGBiodE58fDyNHDmSjI2NydXVlfbu3atRmSx5MV2Fj48PAaBvv/2WHBwcCACdO3dO12G1G9+fvXYNlaipqUF6ejpKSkpURtyHhIRo3RrsSFKpFCKRCBUVFazPi9Fb1dXVsLCwQENDA3JzcxEeHo6DBw9i2bJl+Pzzz3UdXrvw/dnTuMP+zJkzmDFjBkpLS1XeEwgEkMlkWgfFMD1damoqGhoaYGdnBxcXF7zwwgs4ePAgLl++rOvQ9IbGI8UWLFiA0NBQFBUVQS6XK20scTEMP65cuQIA8PPzg0AggI+PDwAgLS2N1+eLuzKNk1dJSQnCwsJgZ2fXEfEwDANwz/gqhkZ4eHjAxMQET58+RU5Oji5D0xsaJ6+pU6ciPj6+A0JhGEbh1q1bAIAhQ4YAaHwGWDGfXlpamq7C0isa93nt3r0boaGhSExMxPDhw1XGVy1atIi34BimJyIileQFAN7e3rh+/TrS0tIwdepUXYWnNzROXocPH8bZs2dhZmaG+Ph4pdGyAoGAJS+G0dLvv/+OiooKGBgYKE2nrng86M6dO7oKTa9onLw+/fRTfPbZZ/joo4/4eTKcYRglilaXm5sbTE1Nuf0eHh4AWPJS0Dj71NXV4bXXXmOJi2E6iOLZXkWyUlC8zs7OZt84oh3Ja+bMmfjuu+86IhaGYdA4YzDQ2PJ6lqurK4yNjVFTU4P8/HxdhKZXNL5tlMlk2LRpE86ePQsvLy+VDvuIiAjegmOYnkiRvJpOyikUCjFw4EBkZmbizp07cHV11UF0+kPj5JWRkYGRI0cCAG7evKn0HpuZlGG011LLC2i8dVQkr+Dg4M4OTa9onLwuXLjQEXEwDPMfbSUvAMjKyurUmPQR63VnGD3y9OlTPHr0CEDzycvd3R0A+8YRUDN5zZs3DwUFBWpd8LvvvsO3336rVVAM01MpWl39+vWDSCRSeV+RvPhaerArU+u20cbGBsOGDcOYMWMQEhICX19f2Nvbw9TUFOXl5cjMzMQvv/yCo0ePwsHBAfv27evouBmmW2rtlhEAN2g1Pz8fNTU1SuPAehq1kteaNWuwcOFCREVFITIyUqWjvk+fPpgwYQL279+PoKCgDgmUYXqCtpKXjY0NLCwsIJVKkZOT0+UX5dCG2h32tra2WL58OZYvX44//vgD9+/fR3V1NaytrTFgwAD2TSPD8KCt5CUQCDBo0CAkJycjOzubJS9N9e3bF3379uU5FIZh2kpeAJSSV0/Gvm1kGD2iTvIaOHAgANZpz5IXw+gJIlK75QWw5MWSF8PoicePH+Pp06cAABcXlxaPY8mrEUteDKMnFK0uiUQCMzOzFo9TJK/CwkJUVVV1Smz6SOPkFR4ejvv373dELAzTo6lzywgAVlZW3BdmPXk+e42T17///W8MGDAA48ePx+HDh1FTU9MRcTFMj6Nu8lIMlwB69q2jxskrOTkZKSkp8PLywuLFiyGRSDB//nxcu3atI+JjmB5D3eQFsH4voJ19Xl5eXti2bRsePHiA6OhoPHjwAM899xyGDx+OHTt2oKKigu84Gabb0yR5seESWnbYy+Vy1NXVoba2FkQES0tL7N27F05OTmy2VYbRUF5eHgDW8lJXu5JXcnIyFixYAIlEgsWLF2PkyJG4desWEhIScPv2baxatUqtVYQ2bNiAP/3pT+jTpw9sbW0xefLkNqf6UKxY1HS7fft2e6rCMHpBLpez5KUhjZOXl5cXRo8ejdzcXERFRaGgoAAbN27kmrEAMGPGDG5OotYkJCTg/fffR1JSEs6dO4eGhgYEBQWhsrKyzXPv3LmDoqIibnt2iSiG6WqKiopQV1cHoVAIR0fHNo9X/H9/+PChWp+X7kjjZxtDQ0Mxa9YsODg4tHiMjY2NWqubnDlzRul1TEwMbG1tkZycjLFjx7Z6rq2tLXu+kuk2FP1dzs7OMDRs+2NpaWkJS0tLlJWV4e7duxgxYkRHh6h3NG55ERH69eunsr+6uhqfffaZVsEoOvotLS3bPHbkyJGQSCQYP358q1NT19bWQiqVKm0Mo2806axX6Om3jhonr9WrV3OPMDyrqqoKq1evbncgRISwsDAEBARg2LBhLR4nkUiwb98+xMbGIi4uDh4eHhg/fjwuXrzY7PEbNmyASCTiNicnp3bHyDAdRZG8NFkRSJG87t692xEh6T2NbxuJqNm5u27cuKFWi6klCxYsQHp6On755ZdWj/Pw8FBajNPf3x8FBQXYsmVLs7eay5cvR1hYGPdaKpWyBMbonfa0vBT9zD11MQ61k1e/fv24b/bc3d2VEphMJsPTp08xb968dgWxcOFC/PDDD7h48aJanZVNjR49Gt98802z75mYmMDExKRdcTFMZ1EkIE2+eFJMRPjbb791SEz6Tu3ktX37dhARZs2ahdWrVystDmBsbAxXV1f4+/trVDgRYeHChTh+/Dji4+M1+qvzrNTUVEgkknadyzD6QDFE6Nm7irYMHz4cQOP6qXK5HAYGPWueBbWT18yZMwE0NmvHjBmjslJ2e7z//vs4fPgw/vWvf6FPnz4oLi4GAIhEIu6p+uXLl+PBgwc4dOgQgMYk6urqCk9PT9TV1eGbb75BbGwsYmNjtY6HYXTh8ePHePz4MQDNWl4DBw6EqakpqqqqkJOTozRcqSdQK3lJpVJYWFgAaPyWr7q6GtXV1c0eqzhOHXv37gUAjBs3Tml/TEwM3n77bQCN41/y8/O59+rq6rBkyRI8ePAAZmZm8PT0xMmTJzFx4kS1y2UYfaJodTk7O6NXr15qn2doaIihQ4ciJSUF6enpLHk1p1+/figqKuLGVjXXYa/oyJfJZGoXTkRtHnPgwAGl18uWLcOyZcvULoNh9J3i6RBNbhkVvLy8kJKSgoyMDEyZMoXv0PSaWsnr/Pnz3DeJrY2pYhhGc+3p71JQ9HtlZGTwGlNXoFbyCgwMbPbfDMNoT5vk5eXlBaBxqFJPo/HXE2fOnFEai/XFF1/A29sbb7zxBsrLy3kNjum+Tp8+jZCQEMyePVupT7Mn0iZ5jRo1CkDjQFVFp39PoXHyWrp0KfeITUZGBsLCwjBx4kTk5OQoDQZlmJbExsZi4sSJ+Pe//43o6GgEBASgrKxM12HpRHV1Nfd4j6enp8bnW1packkvKSmJ19j0ncbJKzc3lxscFxsbi5dffhnr16/Hnj17cPr0ad4DZLqXwsJCbtjNtGnTMHDgQBQUFGDhwoU6jkw3bt68CZlMBltb23aPVVSMr/z111/5DE3vaZy8jI2NuRVLfv75ZwQFBQFo/AvAHnpm2rJ06VJUVlbC398fX3/9NY4cOQKBQIDDhw8jPT1d1+F1urS0NACAt7d3s9/iq4MlLzUFBAQgLCwMa9aswdWrV/HSSy8BaHy8oT2P9jA9R3x8PI4ePQoDAwPs2bMHhoaG8PX1RWhoKABg48aNOo6w8z2bvNpLkbyuXr2q0VClrk7j5LV7924YGhri2LFj2Lt3Lzev1+nTp/Hiiy/yHiCjme+//x7BwcF44YUXsG/fPrXmVesMtbW1mD9/PgBg7ty5Sh9Wxbi92NhYtSax7E4UC9dok7yGDh0KCwsLPH36FCkpKTxF1gVQD1NRUUEAqKKiQteh8Eoul9PSpUsJgNL2xhtvUENDg67DoxUrVhAAsrOzo7KyMpX3fX19CQBt2rRJB9HpRmVlJRkaGhIAysvL0+paf/3rXwkArVmzhqfo+Mf3Z69dyUsmk9GdO3coMTGREhISlDZ9112T1969e7mEtWTJElq7di33wfj00087JYbCwkLavn07LVmyhLZt20aJiYkklUpp3759JBAICAB99913zZ67f/9+AkADBgwgmUzWKfHqWkJCAgEge3t7ksvlWl0rMjKSANBzzz3HU3T803ny+vXXX8nNzY0MDAxIIBAobQYGBrwE1ZG6Y/K6c+cOmZmZEQDauHEjt//QoUMEgAwMDOjSpUsdGkNMTAwZGxurtPye3ebNm9fih/Tp06fUp08fAkDnz5/v0Fj1xfr16wkATZ06Vetr5eXlcb/r4uJiHqLjn86T14gRIyg0NJQyMzOpvLyc/vjjD6VN33W35FVfX09+fn4EgCZMmKDSannrrbcIALm7u1NNTQ3v5cvlclqzZg2XoEaPHk3/+Mc/KCQkhCQSCQEgkUhEa9eubfP2de7cuQSAXn/9dd7j1Efjxo0jALRr1y5ervfnP/+ZANDu3bt5uR7fdJ68zM3NKTs7m5fCdaG7Ja9169ZxCSI/P1/l/fLychKLxQSAVq1axWvZ9fX19N5773GJ66OPPlJJno8fP1a7z+3atWsEgIyNjam0tJTXWPWNVCrlbuv5+jxFREQQAPLz8+PlenzTefL6r//6Lzp9+jQvhetCd0peN27cICMjIwJABw8ebPG47777jksKt27d4qXssrIyeumllwgACQQCXv7ay+Vy8vb2JgC0fft2HqLUX7GxsQSABg4cyNs1i4uLuf8P165d4+26fNF58oqLi6OhQ4dSTEwMXb9+nW7cuKG06bvukrykUil5enoSAAoJCWm1w1cul9PEiRMJAAUGBmrVOSyVSunrr78mFxcXAkCmpqYUFxfX7us19cUXXxAA8vT01LoTW59NnTqVANCHH37I63XffPNNAkB/+9vfeL0uH3SevJp20is66ntyh31DQwMlJiZSREQEhYeHU2RkJP3222+8Xb+p2tpamjRpEgEgsVhMRUVFbZ6Tm5tL5ubmBICio6PVLqu6upri4uJo7ty5NHz4cO5bQwDk5uZG169f16YqKsrLy7kvH3755Rder60vysvLycTEhABQamoqr9dOT08nAwMDAkCXL1/m9dra0nnyysvLa3XTd4of4NWrVyk9PZ2Sk5Pp+vXr7fqyoa6ujvbv309ubm7Nfrvm5eVFkZGRVFlZyVv8JSUlNGHCBAJAJiYmlJSUpPa5mzdvJgBkaWlJJSUlrR7b0NBAmzdvJhsbG5V6ubq60rp160gqlWpbnWbNnj2bANCkSZM65Pq6pvg9DBs2rENal4qfn4+PD9XW1vJ+/fbSefLq6hQ/wOY2Hx8f2rx5M92/f7/Va9TX11NMTIxS0hKJRDRlyhR67733aMKECVzfAwCysrKiTz75hB4+fNjuuEtLS2nTpk1kZWVFAKhXr1505swZja5RV1dHI0aM4L6eb2k8VU5ODgUEBHDxOzo60qJFiyguLk6rOqjrzp07XOshJSWlw8vrTNXV1eTo6EgAaP/+/R1SxsOHD6lv374dcluqDb1IXocOHaIxY8aQRCLhWlvbtm2jEydO8BJUR1L8APv27Uu2trbk4OBAdnZ2KoksICCAvvjiC7p37x7317GoqIh27txJHh4e3HF2dnYUERGh0roqKyuj7du3k6urK3eskZERzZw5k65evdrqN3ANDQ1UWFhIFy9epC1bttD48eNJKBQqtejae7tx5coV7lqLFy9W+ssvl8spJiaGG2/Vp08f2r9/P9XX17erLG1MmzaNAND48eO7Vd/Xxo0bCQA5ODhQdXV1h5Vz/Phx7v/Lvn37OqwcTeg8ee3Zs4esra1p7dq1ZGZmRvfu3SOixkGK48aN4yWojtTSD7CoqIj27NlDY8eOVerXAUC9e/cmCwsLpX1WVla0adOmNm8JGxoa6NixYzRmzBiVa3p5eVFgYCCNHTuW/P39ydfXl5ycnJQS1bPbqFGjKCoqiurq6rT6GRw4cIC75ksvvUSnTp2iI0eOUGBgILf/ueeeo5ycHK3K0cbdu3e5fqFvv/1WZ3HwKSMjg0xNTQkAHTp0qMPL+/jjj7lvgw8fPtzh5bVF58lryJAhdPz4cSIi6t27N5e8MjIyyMrKipegOpI6P8DCwkLaunUr+fv7q4wa9/X1pR07drTrF/Drr7/Sq6++Sr169Wrx1lWxGRgYkJOTE02ePJm2bNlCd+/e1abaKr766iulW1vFZmZmRhs3btSL5yEVg19FIhFvQzx0pby8nIYOHUoA6MUXX+yUR6Dkcjk38FcgEFBERESHl9kavpOXgEiNJXyeYWZmhtu3b8PFxQV9+vTBjRs30L9/f2RnZ8PLy6vFJdH0hVQqhUgkQkVFhVrLtNXV1SEvLw9yuRxOTk4aLU3VEplMhtu3b6OwsBB//PEHDAwMYGRkBCMjI1hZWcHR0RFisRiGhmovq9kumZmZ2LJlC5KSkmBkZITg4GD8/e9/h6ura4eWq666ujq88MILuHTpEtzc3HD27FmN1jXUFwUFBXjllVeQmpoKsViMtLQ02NnZdUrZMpkM//jHP7B7924AwKJFi7B169YO/7/VHE0/e23RuAZubm5IS0uDi4uL0v7Tp09zM6x2J8bGxnB3d+f1mkKhEJ6enu2a9pdPQ4cORXR0tE5jaI2xsTHi4uLg7++PnJwc+Pn5Yf369Zg1axaMjY01upZMJoOBgYFGE/49efIEd+/eRVZWFrKzs3H37l3IZDJYWFhAIpHA3d2d28zNzVXOr6ysRHR0NMLDw1FWVgZra2ucPXu20xIX0Ph/befOnXBxccHSpUuxc+dOpKWl4ciRI7C3t++0ODqCxi2vmJgYrFixAlu3bsXs2bOxf/9+3Lt3Dxs2bMD+/fsxbdq0joqVF3xnf6bj/f7773jllVdw5coVAI2z9gYFBWH06NFwd3eHWCyGubk5BAIBysrKUFBQgPv37yM7O5vbCgsLATTeOTg6OmLAgAFwc3ODvb09+vbtCyLCo0ePUFhYiHv37iErK4tbwV0dzs7OGDx4MAYMGACZTIb8/HxcunQJT548AdC4WHNcXJxOW7WxsbF455138OTJE9jY2CAqKgovv/xyp5XP92dP4+QFAF999RXWrl2LgoICAICDgwPCw8Mxe/ZsrQPqaCx5dU319fXYu3cvPv/8czx8+LDTyrW2toa7uzsGDRqEQYMGwcTEBFKpFPn5+bhz5w6ysrJaXTxkwIABWLJkCd59912d3Ko1lZ2djVdffZWbwXXKlClYu3YthgwZ0uFl60XyUigtLYVcLoetra3WgXQWlry6NplMhsTERFy6dAlXrlxBQUEBiouLUVNTAyKCSCSCk5MTnJycMHDgQC7puLm5QSAQoLKyEvfv30dOTg5yc3NRXFzMrb1gZWUFBwcH9O/fnzuvb9++bcZUWlqK27dv486dO8jLy4OJiQmsra3h4+MDHx8fGBhoPGFxh6qpqcHKlSsREREBmUwGgUCAv/71r3j99dcRFBTU7OdCLpejtLQU9+/fR35+PvLz8/Ho0SOUl5ejsrISAoEAQqEQVlZWsLe35zYXFxc4OTlBIBDoPnm98MILiIuLU/mlSqVSTJ48GefPn9c6qI7EkhfDNMrIyMDKlStx4sQJbp+BgQGcnZ1hZ2cHgUAAmUyG33//HUVFRaivr29XOba2tvDz84O3tzfWrFmju+RlYGCA4uJildZWSUkJHBwc2l3BzsKSF8Mou3nzJmJiYnDy5EluAdzmCAQCSCQSuLi4cAmuX79+6N27N4gIMpkMjx49wsOHD7ktLy8PDQ0NStfp9OSlWJbK29sb58+fh6WlJfeeTCbDmTNn8OWXXyIvL0+jAPbs2YPNmzejqKgInp6e2L59O55//vkWj09ISEBYWBh+++032NvbY9myZZg3b57a5bHkxTAtKy4uRnZ2NsrLywE0JiwbGxs4ODjAzs5O4295a2pqkJqaiqSkJCQmJuL48eP8ffbUHhD2n1kjmpv+WSAQkLm5OUVFRWk0yOzo0aNkZGREX331FWVmZtIHH3xAvXr1avHZwpycHDI3N6cPPviAMjMzuYGWx44dU7vM7jIlDsN0NTobpHr//n0QEfr374+rV6/CxsaGe8/Y2Bi2trYQCoUaJU4/Pz+MGjUKe/fu5fYNGTIEkydPxoYNG1SO/+c//4kffvgBt27d4vbNmzcPN27cUHvBTdbyYhjd0NkgVcWgVL7WAayrq0NycjI++ugjpf1BQUG4fPlys+f8+uuv3ArdCsHBwYiKikJ9fT2MjIxUzqmtrUVtbS33uqKiAgDY6t4M08kUnzk120ttatfAk6ysLMTHx6OkpEQlma1cuVKta5SWlkImk6mMNrazs2txcGBxcXGzxzc0NKC0tBQSiUTlnA0bNmD16tUq+52cnNSKk2EYfj1+/BgikUjr62icvL766ivMnz8f1tbWEIvFSo9bCAQCtZPXs+c8i4hafYSjueOb26+wfPlyhIWFca/lcjnKyspgZWWl0aMimpBKpXByckJBQUGXvjXtDvXoDnUAukc9Kioq4OzsrPRlnzY0Tl5r167FunXr8M9//lOrgq2trSEUClVaWSUlJS0++yUWi5s93tDQEFZWVs2eY2JiAhMTE6V96gw85IOFhUWX/Y/2rO5Qj+5QB6B71IOvQbsaX6W8vByhoaFaF2xsbAwfHx+cO3dOaf+5c+cwZsyYZs/x9/dXOf6nn36Cr69vs/1dDMN0Xxonr9DQUPz000+8FB4WFob9+/cjOjoat27dwuLFi5Gfn8+N21q+fDlmzJjBHT9v3jzcv38fYWFhuHXrFqKjoxEVFYUlS5bwEg/DMF2HxreNAwcOxIoVK5CUlIThw4ertHgWLVqk9rVee+01PH78GJ999hmKioowbNgwnDp1ivtms6ioCPn5+dzxbm5uOHXqFBYvXowvvvgC9vb22LlzJ/72t79pWo0OZWJiglWrVqncrnY13aEe3aEOQPeoB9910PjxIDc3t5YvJhAgJydH66AYhmHaotWsEgzDMLqiX3N1MAzDqEmtPq+wsDCsWbMGvXr1Uhoz1ZyIiAheAmMYhmmNWskrNTWVm+omNTW1xeM6atAnwzCMCl4e7+6hEhISaNKkSSSRSAgAtyScwpMnT+j9998nBwcHMjU1pcGDB9OePXt0E2wL1q9fT76+vtS7d2+ysbGhV155hW7fvq10jFwup1WrVpFEIiFTU1MKDAykmzdv6ihiVW3Voa6ujpYtW0bDhg0jc3NzkkgkNH36dHrw4IEOo1alzu/iWe+99x4BoG3btnVekG1Qtw6ZmZn08ssvk4WFBfXu3Zv8/PzaXKm+KdbnpYXKykqMGDGCW1aqqcWLF+PMmTP45ptvuHFsCxcuxL/+9a9OjrRlCQkJeP/995GUlIRz586hoaEBQUFBqKys5I7ZtGkTIiIisHv3bly7dg1isRh/+ctfuMUldK2tOlRVVSElJQUrVqxASkoK4uLikJWVhZCQEB1Hrkyd34XCiRMncOXKFb1bAUidOty7dw8BAQEYPHgw4uPjcePGDaxYsQKmpqaaFcZXxu3p0EzLy9PTkz777DOlfaNGjaJPP/20EyPTTElJCQGghIQEImpsdYnFYtq4cSN3TE1NDYlEIoqMjNRVmK1qWofmXL16lQBo/Ne+M7VUj8LCQnJwcKCbN2+Si4uLXrW8mmquDq+99hq99dZbWl+btbw6UEBAAH744Qc8ePAARIQLFy4gKysLwcHBug6tRYopgxQPzyoWqXh2KiITExMEBga2OHWRrjWtQ0vHCASCTnvOtT2aq4dcLsf06dOxdOlSna/7qY6mdZDL5Th58iTc3d0RHBzMzW//7Dz6atM6/TFE1HzLq7a2lmbMmEEAyNDQkIyNjenQoUO6CVANcrmcXn75ZQoICOD2Xbp0iQCo9A/NmTOHgoKCOjvENjVXh6aqq6vJx8eH3nzzzU6MTDMt1WP9+vX0l7/8heRyORGRXre8mqtDUVERASBzc3OKiIig1NRU2rBhAwkEAoqPj9fo+rpfSK4b27lzJ5KSkvDDDz/AxcUFFy9exN///ndIJBJMmDBB1+GpWLBgAdLT0/HLL7+ovKfp1EW60lodgMb1H6dNmwa5XI49e/Z0cnTqa64eycnJ2LFjB1JSUvTyZ99Uc3VQzP/3yiuvYPHixQAa18W4fPkyIiMjERgYqH4BPCfbHgtNWl5VVVVkZGREP/74o9Jxs2fPpuDg4E6Orm0LFiwgR0dHysnJUdp/7949AkApKSlK+0NCQmjGjBmdGWKbWqqDQl1dHU2ePJm8vLyotLS0k6NTX0v12LZtGwkEAhIKhdwGgAwMDMjFxUU3wbagpTrU1taSoaEhrVmzRmn/smXLaMyYMRqVwfq8Okh9fT3q6+tV5i4SCoW8TaXNByLCggULEBcXh/Pnz6s8u+rm5gaxWKw0FVFdXR0SEhJanLqos7VVB6Dx9/Hqq68iOzsbP//8c4vzv+lSW/WYPn060tPTkZaWxm329vZYunQpzp49q6OolbVVB2NjY/zpT39SWWItKyuLm5BBk8KYdnry5AmlpqZSamoqAeDu4RXfYAUGBpKnpydduHCBcnJyKCYmhkxNTfVqrNf8+fNJJBJRfHw8FRUVcVtVVRV3zMaNG0kkElFcXBxlZGTQ66+/ThKJhKRSqQ4j/39t1aG+vp5CQkLI0dGR0tLSlI6pra3VcfT/T53fRVP61uelTh3i4uLIyMiI9u3bR9nZ2bRr1y4SCoWUmJioUVkseWnhwoULBEBlmzlzJhE1dk6+/fbbZG9vT6ampuTh4UFbt27lOlv1QXPxA6CYmBjuGMUgVbFYTCYmJjR27FjKyMjQXdBNtFWH3NzcFo+5cOGCTmN/ljq/i6b0LXmpW4eoqCgaOHAgmZqa0ogRI+jEiRMal8VmlWAYpktifV4Mw3RJLHkxDNMlseTFMEyXxJIXwzBdEkteDMN0SSx5MQzTJbHkxTBMl8SSF8MwXRJLXozG4uPjIRAI8Mcff+ik/PPnz2Pw4MHcM6Lh4eHw9vbW6pp5eXkQCARIS0tT+5zdu3fr3WysPQkbYc+0aty4cfD29sb27du5fXV1dSgrK4OdnZ1Opmbx9fXFBx98gOnTpwMAnj59itraWq0etpbJZHj06BGsra1haKjeTFG1tbVwdXXF999/j4CAgHaXzbQPa3kxGjM2NoZYLNZJ4rp8+TKys7MRGhrK7evdu7fWs0QIhUKIxWK1ExfQOKPsG2+8gV27dmlVNtM+LHkxLXr77beRkJCAHTt2QCAQQCAQIC8vT+W28cCBA+jbty9+/PFHeHh4wNzcHFOnTkVlZSUOHjwIV1dX9OvXDwsXLoRMJuOuX1dXh2XLlsHBwQG9evWCn58f4uPjW43p6NGjCAoKUlqsoelt49tvv43Jkydj/fr1sLOzQ9++fbF69Wo0NDRg6dKlsLS0hKOjI6Kjo7lzmt42Kur4v//7v/D19YW5uTnGjBmjMpVLSEgITpw4gerq6vb9kJl2Y8mLadGOHTvg7++POXPmoKioCEVFRXBycmr22KqqKuzcuRNHjx7FmTNnEB8fjylTpuDUqVM4deoUvv76a+zbtw/Hjh3jznnnnXdw6dIlHD16FOnp6QgNDcWLL76I7OzsFmO6ePEifH1924z9/PnzePjwIS5evIiIiAiEh4dj0qRJ6NevH65cuYJ58+Zh3rx5KCgoaPU6n3zyCbZu3Yrr16/D0NAQs2bNUnrf19cX9fX1uHr1apsxMTzjYRYMphsLDAykDz74QGmfYiqg8vJyIiKKiYkhAHT37l3umLlz55K5uTk9efKE2xccHExz584lIqK7d++SQCBQmRt//PjxtHz58hbjEYlEKusArFq1ikaMGMG9njlzJrm4uJBMJuP2eXh40PPPP8+9bmhooF69etGRI0eI6P+nzUlNTVWq488//8ydc/LkSQJA1dXVSuX369ePDhw40GLMTMdgc9gzvDA3N8eAAQO413Z2dnB1dUXv3r2V9pWUlAAAUlJSQERwd3dXuk5bHe/V1dVqre/n6empNIutnZ0dhg0bxr0WCoWwsrLi4mmJl5cX92+JRAIAKCkpgbOzM7ffzMwMVVVVbcbE8IslL4YXRkZGSq8FAkGz+xTDG+RyOYRCIZKTkyEUCpWOezbhNWVtbY3y8nLe41HnOoovKJqeU1ZWBhsbmzZjYvjFkhfTKmNjY6VOdr6MHDkSMpkMJSUleP755zU6LzMzk/d42uvevXuoqanByJEjdR1Kj8M67JlWubq64sqVK8jLy0NpaSlvi4e4u7vjzTffxIwZMxAXF4fc3Fxcu3YNn3/+OU6dOtXiecHBwS0ua6YLiYmJ6N+/v9ItM9M5WPJiWrVkyRIIhUIMHToUNjY2yM/P5+3aMTExmDFjBj788EN4eHggJCQEV65cafEbTQB46623kJmZqTJkQVeOHDmCOXPm6DqMHomNsGe6nGXLlqGiogJffvmlTuO4efMmxo8fj6ysLIhEIp3G0hOxlhfT5XzyySdwcXHpkL44TTx8+BCHDh1iiUtHWMuLYZguibW8GIbpkljyYhimS2LJi2GYLoklL4ZhuiSWvBiG6ZJY8mIYpktiyYthmC6JJS+GYboklrwYhumS/g/5JEWhOOWF5gAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 300x150 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot secrection vs OD - one curve for Fig. 2B.\n", "\n", "#plot growth curve and OD\n", "fig, ax = plt.subplots(1,2,figsize=(4.5,2))  #for barplots yields/excretion\n", "\n", "######################\n", "#plot growth rate\n", "######################\n", "\n", "datagr=pd.read_csv(\"data_HPLC/growthcurve_example.csv\",skiprows=1)\n", "datagr.columns=[\"time\",\"OD\",\"OD2\"]\n", "\n", "ax[0].set_xlabel(\"time (h)\")\n", "ax[0].set_ylabel(\"bacterial density (OD600)\")\n", "ax[0].plot(datagr[\"time\"],datagr[\"OD2\"],marker=\"o\",color='k',ls=\"\")\n", "\n", "fitgr=np.polyfit(datagr[\"time\"],np.log(datagr[\"OD2\"]),1)\n", "grv=np.linspace(0,4,10)\n", "ax[0].plot(grv,np.exp(fitgr[0]*grv+fitgr[1]),ls='--',color='k')\n", "ax[0].text(0.5,0.5,fitgr[0])#\n", "\n", "\n", "#display(select)\n", "#for iter,row in select.iterrows():\n", "#    ax[0].scatter(row[\"time\"],row[\"OD2\"],marker=\"o\",color='r')\n", "\n", "ax[0].set_yscale(\"log\")\n", "ax[0].minorticks_off()\n", "ax[0].set_yticks([0.04,0.08,0.16,0.32,0.64])\n", "ax[0].set_yticklabels([0.04,0.08,0.16,0.32,0.64])\n", "\n", "####################\n", "#plot concentration vs OD\n", "####################\n", "\n", "##!!! ADJUST EXPENETIAL NUMBER\n", "\n", "#B. theta runs in YCA: 35,43,53\n", "select=dataout_av.loc[dataout_av[\"exp_number\"]==\"43\"]\n", "select=select.sort_values(by=[\"strain\",\"medium\"])\n", "#for index,row in select.iterrows():\n", "display(select)\n", "fontsize=12\n", " \n", "\n", "\n", "#plot averages\n", "iA=-1\n", "for index,row in select.iterrows():\n", "    axTW=ax[1].twinx()\n", "    #print(index)\n", "    iA=iA+1\n", "    axTW.set_ylabel(\"ferm. product (mmol)\",fontsize=fontsize)\n", "    ax[1].set_ylabel(\"glucose (mmol)\",fontsize=fontsize,color='b')\n", "    ax[1].set_xlabel(\"bacterial density (OD600)\",fontsize=fontsize)\n", "    #energycontent=np.array([0.68,0.21,0.37,.36,.33,0.52,0,0.33]) #kcal/mmol #the energy per mm for different fermentation products3\n", "    ic=-1\n", "    for sub in sublist:\n", "        #print(sub)\n", "        ic=ic+1\n", "        cfactor=cfactorlist[ic]\n", "        color=colorlist[ic]\n", "        label=sublistshort[ic]\n", "        conversionfactor=1/0.5\n", "        #print(row[sub+\"_OD\"])\n", "        #print(row[sub+\"_conc\"])\n", "        if row[sub+\"_OD\"] in [\"nan\",np.nan]:\n", "            pass\n", "        else:\n", "            odc=row[sub+\"_OD\"].replace(\"[\",\"\").replace(\"]\",\"\").replace(\"  \",\" \").replace(\"  \",\" \").strip().split(\" \")\n", "            concc=row[sub+\"_conc\"].replace(\"[\",\"\").replace(\"]\",\"\").replace(\"  \",\" \").replace(\"  \",\" \").strip().split(\" \")\n", "            #print(concc)\n", "            #print(odc)\n", "            odc=np.array(odc,dtype=float)\n", "            concc=np.array(concc,dtype=float)\n", "            odcval=np.linspace(0,0.7,10)\n", "            m=row[sub]\n", "            y0=row[sub+\"_y0\"]\n", "            ax[1].set_title(row['species']+\" \"+row['strain']+\"; \"+row['medium']+\"; exp: \"+str(row['exp_number'])+\" (\"+str(row['exp_number_usedonly'])+\")\")\n", "            \n", "            if sub in [\"glucose\",\"maltose\"]:\n", "                offsetc=0\n", "                ax[1].plot(odc,concc-offsetc,color=color,marker=markerlist[ic],ls='',label=label, fillstyle='none')\n", "                ax[1].plot(odcval,odcval*m+y0-offsetc,color=color,ls='--')\n", "            else:\n", "                offsetc=y0\n", "                axTW.plot(odc,concc-offsetc,color=color,marker=markerlist[ic],ls='',label=label, fillstyle='none')\n", "                axTW.plot(odcval,odcval*m+y0-offsetc,color=color,ls='-')\n", "    #if iA==0:\n", "    #    ax[0].set_yticklabels(tickl[::-1],fontsize=fontsize)\n", "             \n", "ax[1].set_ylim(15,25)\n", "#axTW.legend()\n", "plt.tight_layout()\n", "fig.savefig(\"plot_output_figure1/illustration_method_hplccurves.pdf\")\n", "plt.show()\n", "\n", "###############\n", "#HPLC chromatogram example\n", "##############\n", "\n", "chrom=pd.read_csv(\"data_HPLC/Erec_3_FP09_chromatogram.csv\",skiprows=0)\n", "#chrom.columns=[\"time\",\"intensity\"]\n", "display(chrom)\n", "\n", "fig, ax = plt.subplots(1,1,figsize=(3,1.5))  #for barplots yields/excretion\n", "\n", "ax.plot(chrom[\"time_min\"].to_numpy(),chrom[\"intensity_mV\"],color='k')\n", "\n", "\n", "ax.set_xlabel(\"time (imin)\")\n", "ax.set_ylabel(\"intensity (mV)\")\n", "ax.set_xlim(17,26)\n", "#ax.set_xlim(15,36)\n", "ax.set_ylim(0,5)\n", "#ax.set_yscale(\"log\")\n", "\n", "plt.tight_layout()\n", "fig.savefig(\"plot_output_figure1/illustration_method_chromatogram.pdf\")\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 4}