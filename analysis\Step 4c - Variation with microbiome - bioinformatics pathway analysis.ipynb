{"cells": [{"cell_type": "markdown", "id": "2c3bdf0f-ba68-463b-add7-ec0100978f89", "metadata": {}, "source": ["# Analysis of abundance of propionate and butyrate producing fermentation pathways"]}, {"cell_type": "code", "execution_count": 1, "id": "09c503fa-2db8-429e-b86f-ea359ca54154", "metadata": {}, "outputs": [], "source": ["#load required packages\n", "import pandas as pd\n", "import subprocess\n", "import numpy as np\n", "import json\n", "import os, sys\n", "#import maptlotlib\n", "#matplotlib.rcParams['pdf.fonttype'] = 42\n", "#matplotlib.rcParams['ps.fonttype'] = 42\n", "\n", "import met_brewer\n", "from datetime import datetime\n", "import numpy as np\n", "import json\n", "import time\n", "from matplotlib import rc_file\n", "from pylab import *\n", "import json\n", "\n", "#read in basic characteristics of British reference diet\n", "with open('data_analysisresults/BRD_characteristics.json', 'r') as fp:\n", "        BRD = json.load(fp)"]}, {"cell_type": "code", "execution_count": 2, "id": "b35cabf5-e356-42b4-a7c2-a4ef28d9ec38", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>strain</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Butyricimonas-virosa-DSM-23226-MAF-2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bacteroides-plebeius-DSM-17135-MAF-2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Eubacterium eligens</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Bacteroides thetaiotaomicron</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Parabacteroides-merdae-ATCC-43184-MAF-2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1097</th>\n", "      <td>Aggregatibacter segnis</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1098</th>\n", "      <td>Aeromonas punctata</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1099</th>\n", "      <td>Acidovorax wa<PERSON>ii</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1100</th>\n", "      <td>Achromobacter spanius</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1101</th>\n", "      <td>Achromobacter insuavis</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1102 rows × 1 columns</p>\n", "</div>"], "text/plain": ["                                       strain\n", "0        Butyricimonas-virosa-DSM-23226-MAF-2\n", "1        Bacteroides-plebeius-DSM-17135-MAF-2\n", "2                         Eubacterium eligens\n", "3                Bacteroides thetaiotaomicron\n", "4     Parabacteroides-merdae-ATCC-43184-MAF-2\n", "...                                       ...\n", "1097                   Aggregatibacter segnis\n", "1098                       Aeromonas punctata\n", "1099                     Acidovorax <PERSON>ii\n", "1100                    Achromobacter spanius\n", "1101                   Achromobacter insuavis\n", "\n", "[1102 rows x 1 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>strain</th>\n", "      <th>pathway</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Anaerofust<PERSON> stercorihomi<PERSON></td>\n", "      <td>4 Ami</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Anaerostipes caccae DSM 14662 MAF 2</td>\n", "      <td>4 Ami</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Butyricicoccus pullicaecorum</td>\n", "      <td>4 Ami</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Clostridioides difficile</td>\n", "      <td>4 Ami</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Clostridium be<PERSON>i</td>\n", "      <td>4 Ami</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>367</th>\n", "      <td>Propionibacterium acnes</td>\n", "      <td>WWC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>368</th>\n", "      <td>Propionibacterium avidum</td>\n", "      <td>WWC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>369</th>\n", "      <td>Propionibacterium freudenreichii</td>\n", "      <td>WWC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>370</th>\n", "      <td>Propionibacterium jense<PERSON>i</td>\n", "      <td>WWC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>371</th>\n", "      <td>Propionimicrobium lymphophilum</td>\n", "      <td>WWC</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>372 rows × 2 columns</p>\n", "</div>"], "text/plain": ["                                  strain pathway\n", "0           Anaerofustis stercorihominis   4 Ami\n", "1    Anaerostipes caccae DSM 14662 MAF 2   4 Ami\n", "2           Butyricicoccus pullicaecorum   4 Ami\n", "3               Clostridioides difficile   4 Ami\n", "4               Clostridium beijerinckii   4 Ami\n", "..                                   ...     ...\n", "367              Propionibacterium acnes     WWC\n", "368             Propionibacterium avidum     WWC\n", "369     Propionibacterium freudenreichii     WWC\n", "370           Propionibacterium jensenii     WWC\n", "371       Propionimicrobium lymphophilum     WWC\n", "\n", "[372 rows x 2 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>strain</th>\n", "      <th>pathway</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>662</th>\n", "      <td>Abiotrophia defectiva</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>580</th>\n", "      <td>Acetanaerobacterium elongatum</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>404</th>\n", "      <td>Achromobacter denitrificans</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1141</th>\n", "      <td>Achromobacter insuavis</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1140</th>\n", "      <td>Achromobacter spanius</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>302</th>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Pro</td>\n", "    </tr>\n", "    <tr>\n", "      <th>303</th>\n", "      <td><PERSON><PERSON><PERSON> pseudotuberculosis</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>304</th>\n", "      <td><PERSON><PERSON><PERSON> r<PERSON></td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>305</th>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>917</th>\n", "      <td>Yonghaparkia alkaliphila</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1197 rows × 2 columns</p>\n", "</div>"], "text/plain": ["                             strain pathway\n", "662           Abiotrophia defectiva     NaN\n", "580   Acetanaerobacterium elongatum     NaN\n", "404     Achromobacter denitrificans     NaN\n", "1141         Achromobacter insuavis     NaN\n", "1140          Achromobacter spanius     NaN\n", "...                             ...     ...\n", "302           Yersinia kristensenii     Pro\n", "303     Yersinia pseudotuberculosis     NaN\n", "304                 <PERSON><PERSON><PERSON> rohdei     NaN\n", "305          Yokenella regensburgei     NaN\n", "917        Yonghaparkia alkaliphila     NaN\n", "\n", "[1197 rows x 2 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["strain     1197\n", "pathway     372\n", "dtype: int64"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["array(['Abiotrophia', 'Acetanaerobacterium', 'Achromobacter',\n", "       'Acidaminococcus', 'Acidovorax', 'Acinetobacter', 'Actinobacillus',\n", "       'Actinomyces', 'Adlercreutzia', 'Aerococcus', 'Aeromicrobium',\n", "       'Aeromonas', 'Afipia', 'Aggregatibacter', 'Agrobacterium',\n", "       'Agrococcus', 'Akkermansia', 'Alcaligenes', 'Aliagarivorans',\n", "       '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Alloprevotella', 'Alloscardov<PERSON>',\n", "       'Anaerobiospirillum', 'Anaerobutyricum', 'Anaerococcus',\n", "       '<PERSON><PERSON><PERSON>st<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>stip<PERSON>', 'Anaerotignum',\n", "       'Anaerotr<PERSON><PERSON>', 'Ancylobacter', 'Aneurinibacillus',\n", "       'Aquabacterium', 'Aquincola', 'Arcanobacterium', 'Arcobacter',\n", "       'Arthrobacter', 'Atopobium', 'Aurantimonas', '<PERSON>reimonas',\n", "       '<PERSON><PERSON><PERSON>', 'Bacteroides', '<PERSON><PERSON><PERSON>', 'Bavariicoccus',\n", "       'Bifidobacterium', 'Bilophila', 'Blastococcus', 'Blastomonas',\n", "       '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>la', 'Brachybacterium', 'Brachymonas',\n", "       'Brachyspira', 'Bradyr<PERSON><PERSON>bium', 'Brevibacillus', 'Brevibacterium',\n", "       'Brevundimonas', 'Brochoth<PERSON>', 'Bulleidia', 'Burkholderia',\n", "       'Butyricicoccus', 'Butyricimonas', 'Butyrivibrio',\n", "       'Caldicoprobacter', 'Campylobacter', 'Capnocytophaga',\n", "       'Cardiobacterium', 'Carnobacterium', 'Catabacter',\n", "       'Catenibacterium', 'Caulobacter', 'Cedecea', 'Cellulomonas',\n", "       'Cellulosilyticum', 'Cellulosimicrobium', 'Cetobacterium',\n", "       'Christensen<PERSON>', 'Chryseobacterium', 'Citrobacter',\n", "       'Cloacibacillus', 'Cloacibacterium', 'Clostridioides',\n", "       'Clostri<PERSON>', 'Collins<PERSON>', 'Comamonas', 'Cop<PERSON>bacillus',\n", "       'Coprobacter', 'Coprococcus', 'Corynebacterium', 'Cronobacter',\n", "       'Cryptobacterium', 'Cupriavidus', 'Curtobacterium',\n", "       'Cutibacterium', 'Cytophaga', 'Deinococcus', 'Delftia',\n", "       'Dermabacter', 'Dermacoccus', 'Desemzia', 'Desulfitobacterium',\n", "       '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',\n", "       'Do<PERSON>', 'Dyadobacter', 'Dysgonomonas', 'Edwards<PERSON><PERSON>',\n", "       '<PERSON><PERSON><PERSON><PERSON>', 'Egg<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n", "       'Empedobacter', 'Enhydrobacter', 'Ensifer', 'Enterobacter',\n", "       'Enteroclos<PERSON>', 'Enterococcus', 'Enterovibrio',\n", "       'Erysipelatoclostridium', 'Escherichia', 'Eubacterium',\n", "       'Exiguobacterium', 'Facklamia', 'Faecalibacterium', 'Faecalitalea',\n", "       'Ferrimonas', '<PERSON>li<PERSON>ctor', 'Finegoldia', 'Flavobacterium',\n", "       'Flavonifractor', 'Fusicatenibacter', 'Fusobacterium',\n", "       'Gallibacterium', 'Gem<PERSON>', 'Gemmiger', 'Geobacillus',\n", "       'Georgenia', 'Glutamicibacter', 'Gordonia', 'Gordonibacter',\n", "       '<PERSON>uli<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Haemophilus', 'Hafnia',\n", "       'Helicobacter', 'Herb<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',\n", "       '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Hungatella',\n", "       'Hydrogenoanaerobacterium', 'Hymenobacter', 'Hyphomicrobium',\n", "       'Intestinibacter', 'Intestinimonas', 'Janibacter',\n", "       'Jeotgalicoccus', '<PERSON><PERSON>py<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',\n", "       '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>',\n", "       '<PERSON>yt<PERSON><PERSON><PERSON>', 'Lachnoanaerobaculum', 'Lachnobacterium',\n", "       '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>if<PERSON>',\n", "       'Laribacter', 'Lautropia', 'Leminorella', 'Leptotrichia',\n", "       'Leucob<PERSON><PERSON>', '<PERSON><PERSON><PERSON>sto<PERSON>', 'Listeria', 'Lysinibacillus',\n", "       'Lysob<PERSON><PERSON>', 'Macrococcus', 'Mannheim<PERSON>', 'Marvinb<PERSON><PERSON>',\n", "       'Mass<PERSON>', 'Megamonas', 'Megasphaera', 'Mesorhizobium',\n", "       'Methylobacterium', 'Methyloversatilis', 'Microbacterium',\n", "       'Micrococcus', 'Microlunatus', 'Micromonospora', 'Microvirga',\n", "       '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>ller<PERSON>',\n", "       'Mogibacterium', 'Moraxella', 'Morganella', 'Murimonas',\n", "       'Mycobacterium', 'Mycoplasma', 'Negativicoccus', 'Neisseria',\n", "       '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Novosphingobium', 'Obesumbacterium',\n", "       'Oceanobacillus', 'Ochrobactrum', 'Odoribacter', 'Olsenella',\n", "       'Oribacterium', 'Ornithobacterium', 'Oscillibacter', 'Oxalobacter',\n", "       'Paeniba<PERSON><PERSON>', 'Pantoea', 'Papillibacter', 'Parabacteroides',\n", "       'Paraburkholderia', 'Paracoccus', 'Paraeggerthella',\n", "       'Paraprevotella', 'Parasporobacterium', 'Parasutterella',\n", "       'Parvimonas', 'Pediococcus', 'Pelomonas', 'Peptococcus',\n", "       '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Peptostreptococ<PERSON>', 'Phascolarctobacterium',\n", "       'Phenylobacterium', 'Phocaeicola', 'Phyllobacterium',\n", "       'Planococcus', 'Planomicrobium', 'Plesiomonas', 'Porphyromonas',\n", "       'Prevo<PERSON><PERSON>', 'Prochlorococcus', 'Promicromonospora',\n", "       'Propionibacterium', 'Propionimicrobium', 'Proteus', 'Providencia',\n", "       'Pseudarthrobacter', 'Pseudocitrobacter', 'Pseudoclavibacter',\n", "       'Pseudo<PERSON>lavonifra<PERSON>', 'Pseudomonas', 'Pseudoramibacter',\n", "       'Pseudoxanthomonas', 'Psychrobacter', 'Pyramidobacter', 'Ra<PERSON>ella',\n", "       '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Rhizobium',\n", "       'Rhodococcus', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',\n", "       '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Ruminococcus',\n", "       '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Se<PERSON>omonas',\n", "       '<PERSON><PERSON><PERSON><PERSON>', 'Senegalemassilia', 'Serratia', 'Shi<PERSON>la',\n", "       'Silanimonas', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>thi<PERSON>', 'Soleaferrea',\n", "       'Solobacterium', 'Sphingobacterium', 'Sphingomonas',\n", "       '<PERSON>phi<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Staphylococcus',\n", "       'Stenotrophomonas', 'Stoquefic<PERSON>', 'Streptococcus',\n", "       'Streptomyces', 'Subdoligranulum', 'Succinatimonas',\n", "       '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>',\n", "       'Tetragenococcus', '<PERSON><PERSON><PERSON><PERSON>', 'Thermomonas', 'Thermus',\n", "       '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>ne<PERSON>',\n", "       '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n", "       'Turicibacter', '<PERSON><PERSON><PERSON><PERSON>', 'Ureaplasma', '<PERSON>reiba<PERSON><PERSON>',\n", "       '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Varibacu<PERSON>', 'Variovorax', '<PERSON><PERSON><PERSON><PERSON>',\n", "       '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n", "       '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Yonghaparkia'], dtype=object)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["336"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[nan 'G<PERSON>' 'SP' 'Lys' 'Ace' '4Ami' 'Acr' 'Pro' 'WWC']\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>strain</th>\n", "      <th>pathway</th>\n", "      <th>genus</th>\n", "      <th>Unnamed: 0</th>\n", "      <th>phylum</th>\n", "      <th>class</th>\n", "      <th>order</th>\n", "      <th>family</th>\n", "      <th>BUT</th>\n", "      <th>PRO</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Abiotrophia defectiva</td>\n", "      <td>NaN</td>\n", "      <td>Abiotrophia</td>\n", "      <td>227.0</td>\n", "      <td>Bacillota</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Lactobacillales</td>\n", "      <td>Aerococcaceae</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Acetanaerobacterium elongatum</td>\n", "      <td>NaN</td>\n", "      <td>Acetanaerobacterium</td>\n", "      <td>221.0</td>\n", "      <td>Bacillota</td>\n", "      <td>Clostridia</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Oscillospiraceae</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Achromobacter denitrificans</td>\n", "      <td>NaN</td>\n", "      <td>Achromobacter</td>\n", "      <td>163.0</td>\n", "      <td>Pseudomonadota</td>\n", "      <td>Betaproteobacteria</td>\n", "      <td>Burkholderiales</td>\n", "      <td>Alcaligenaceae</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Achromobacter insuavis</td>\n", "      <td>NaN</td>\n", "      <td>Achromobacter</td>\n", "      <td>163.0</td>\n", "      <td>Pseudomonadota</td>\n", "      <td>Betaproteobacteria</td>\n", "      <td>Burkholderiales</td>\n", "      <td>Alcaligenaceae</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Achromobacter spanius</td>\n", "      <td>NaN</td>\n", "      <td>Achromobacter</td>\n", "      <td>163.0</td>\n", "      <td>Pseudomonadota</td>\n", "      <td>Betaproteobacteria</td>\n", "      <td>Burkholderiales</td>\n", "      <td>Alcaligenaceae</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1193</th>\n", "      <td><PERSON><PERSON><PERSON> pseudotuberculosis</td>\n", "      <td>NaN</td>\n", "      <td>Yersinia</td>\n", "      <td>126.0</td>\n", "      <td>Pseudomonadota</td>\n", "      <td>Gammaproteobacteria</td>\n", "      <td>Enterobacterales</td>\n", "      <td>Yersiniaceae</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1194</th>\n", "      <td><PERSON><PERSON><PERSON> r<PERSON></td>\n", "      <td>NaN</td>\n", "      <td>Yersinia</td>\n", "      <td>126.0</td>\n", "      <td>Pseudomonadota</td>\n", "      <td>Gammaproteobacteria</td>\n", "      <td>Enterobacterales</td>\n", "      <td>Yersiniaceae</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1195</th>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>NaN</td>\n", "      <td>Yo<PERSON><PERSON></td>\n", "      <td>127.0</td>\n", "      <td>Pseudomonadota</td>\n", "      <td>Gammaproteobacteria</td>\n", "      <td>Enterobacterales</td>\n", "      <td>Enterobacteriaceae</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1196</th>\n", "      <td>Yonghaparkia alkaliphila</td>\n", "      <td>NaN</td>\n", "      <td>Yonghaparkia</td>\n", "      <td>285.0</td>\n", "      <td>Actinomycetota</td>\n", "      <td>Actinomycetes</td>\n", "      <td>Micrococcales</td>\n", "      <td>Microbacteriaceae</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1197</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Senegalimassilia</td>\n", "      <td>260.0</td>\n", "      <td>Actinomycetota</td>\n", "      <td>Coriobacteriia</td>\n", "      <td>Coriobacteriales</td>\n", "      <td>Coriobacteriaceae</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1198 rows × 10 columns</p>\n", "</div>"], "text/plain": ["                             strain pathway                genus  Unnamed: 0  \\\n", "0             Abiotrophia defectiva     NaN          Abiotrophia       227.0   \n", "1     Acetanaerobacterium elongatum     NaN  Acetanaerobacterium       221.0   \n", "2       Achromobacter denitrificans     NaN        Achromobacter       163.0   \n", "3            Achromobacter insuavis     NaN        Achromobacter       163.0   \n", "4             Achromobacter spanius     NaN        Achromobacter       163.0   \n", "...                             ...     ...                  ...         ...   \n", "1193    Yersinia pseudotuberculosis     NaN             Yersinia       126.0   \n", "1194                Yersinia rohdei     NaN             Yersinia       126.0   \n", "1195         Yokenella regensburgei     NaN            Yokenella       127.0   \n", "1196       Yonghaparkia alkaliphila     NaN         Yonghaparkia       285.0   \n", "1197                            NaN     NaN     Senegalimassilia       260.0   \n", "\n", "              phylum                class             order  \\\n", "0          Bacillota              Bacilli   Lactobacillales   \n", "1          Bacillota           Clostridia     Eubacteriales   \n", "2     Pseudomonadota   Betaproteobacteria   Burkholderiales   \n", "3     Pseudomonadota   Betaproteobacteria   Burkholderiales   \n", "4     Pseudomonadota   Betaproteobacteria   Burkholderiales   \n", "...              ...                  ...               ...   \n", "1193  Pseudomonadota  Gammaproteobacteria  Enterobacterales   \n", "1194  Pseudomonadota  Gammaproteobacteria  Enterobacterales   \n", "1195  Pseudomonadota  Gammaproteobacteria  Enterobacterales   \n", "1196  Actinomycetota        Actinomycetes     Micrococcales   \n", "1197  Actinomycetota       Coriobacteriia  Coriobacteriales   \n", "\n", "                  family  BUT  PRO  \n", "0          Aerococcaceae    0    0  \n", "1       Oscillospiraceae    0    0  \n", "2         Alcaligenaceae    0    0  \n", "3         Alcaligenaceae    0    0  \n", "4         Alcaligenaceae    0    0  \n", "...                  ...  ...  ...  \n", "1193        Yersiniaceae    0    0  \n", "1194        Yersiniaceae    0    0  \n", "1195  Enterobacteriaceae    0    0  \n", "1196   Microbacteriaceae    0    0  \n", "1197   Coriobacteriaceae    0    0  \n", "\n", "[1198 rows x 10 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["phylum\n"]}, {"ename": "TypeError", "evalue": "can only concatenate str (not \"int\") to str", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNotImplementedError\u001b[0m                       Traceback (most recent call last)", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/groupby/groupby.py:1490\u001b[0m, in \u001b[0;36mGroupBy._cython_agg_general.<locals>.array_func\u001b[0;34m(values)\u001b[0m\n\u001b[1;32m   1489\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1490\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgrouper\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_cython_operation\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1491\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43maggregate\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1492\u001b[0m \u001b[43m        \u001b[49m\u001b[43mvalues\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1493\u001b[0m \u001b[43m        \u001b[49m\u001b[43mhow\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1494\u001b[0m \u001b[43m        \u001b[49m\u001b[43maxis\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdata\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mndim\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m-\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1495\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmin_count\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmin_count\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1496\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1497\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1498\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mNotImplementedError\u001b[39;00m:\n\u001b[1;32m   1499\u001b[0m     \u001b[38;5;66;03m# generally if we have numeric_only=False\u001b[39;00m\n\u001b[1;32m   1500\u001b[0m     \u001b[38;5;66;03m# and non-applicable functions\u001b[39;00m\n\u001b[1;32m   1501\u001b[0m     \u001b[38;5;66;03m# try to python agg\u001b[39;00m\n\u001b[1;32m   1502\u001b[0m     \u001b[38;5;66;03m# TODO: shouldn't min_count matter?\u001b[39;00m\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/groupby/ops.py:959\u001b[0m, in \u001b[0;36mBaseGrouper._cython_operation\u001b[0;34m(self, kind, values, how, axis, min_count, **kwargs)\u001b[0m\n\u001b[1;32m    958\u001b[0m ngroups \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mngroups\n\u001b[0;32m--> 959\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mcy_op\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcython_operation\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    960\u001b[0m \u001b[43m    \u001b[49m\u001b[43mvalues\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mvalues\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    961\u001b[0m \u001b[43m    \u001b[49m\u001b[43maxis\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maxis\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    962\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmin_count\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmin_count\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    963\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcomp_ids\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mids\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    964\u001b[0m \u001b[43m    \u001b[49m\u001b[43mngroups\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mngroups\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    965\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    966\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/groupby/ops.py:657\u001b[0m, in \u001b[0;36mWrappedCythonOp.cython_operation\u001b[0;34m(self, values, axis, min_count, comp_ids, ngroups, **kwargs)\u001b[0m\n\u001b[1;32m    649\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_ea_wrap_cython_operation(\n\u001b[1;32m    650\u001b[0m         values,\n\u001b[1;32m    651\u001b[0m         min_count\u001b[38;5;241m=\u001b[39mmin_count,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    654\u001b[0m         \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs,\n\u001b[1;32m    655\u001b[0m     )\n\u001b[0;32m--> 657\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_cython_op_ndim_compat\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    658\u001b[0m \u001b[43m    \u001b[49m\u001b[43mvalues\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    659\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmin_count\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmin_count\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    660\u001b[0m \u001b[43m    \u001b[49m\u001b[43mngroups\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mngroups\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    661\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcomp_ids\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcomp_ids\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    662\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmask\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    663\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    664\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/groupby/ops.py:497\u001b[0m, in \u001b[0;36mWrappedCythonOp._cython_op_ndim_compat\u001b[0;34m(self, values, min_count, ngroups, comp_ids, mask, result_mask, **kwargs)\u001b[0m\n\u001b[1;32m    495\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m res\u001b[38;5;241m.\u001b[39mT\n\u001b[0;32m--> 497\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_call_cython_op\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    498\u001b[0m \u001b[43m    \u001b[49m\u001b[43mvalues\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    499\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmin_count\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmin_count\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    500\u001b[0m \u001b[43m    \u001b[49m\u001b[43mngroups\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mngroups\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    501\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcomp_ids\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcomp_ids\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    502\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmask\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmask\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    503\u001b[0m \u001b[43m    \u001b[49m\u001b[43mresult_mask\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mresult_mask\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    504\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    505\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/groupby/ops.py:541\u001b[0m, in \u001b[0;36mWrappedCythonOp._call_cython_op\u001b[0;34m(self, values, min_count, ngroups, comp_ids, mask, result_mask, **kwargs)\u001b[0m\n\u001b[1;32m    540\u001b[0m out_shape \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_output_shape(ngroups, values)\n\u001b[0;32m--> 541\u001b[0m func \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_cython_function\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mkind\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhow\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvalues\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdtype\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mis_numeric\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    542\u001b[0m values \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_cython_vals(values)\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/groupby/ops.py:173\u001b[0m, in \u001b[0;36mWrappedCythonOp._get_cython_function\u001b[0;34m(cls, kind, how, dtype, is_numeric)\u001b[0m\n\u001b[1;32m    171\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mobject\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m f\u001b[38;5;241m.\u001b[39m__signatures__:\n\u001b[1;32m    172\u001b[0m     \u001b[38;5;66;03m# raise NotImplementedError here rather than TypeError later\u001b[39;00m\n\u001b[0;32m--> 173\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mNotImplementedError\u001b[39;00m(\n\u001b[1;32m    174\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfunction is not implemented for this dtype: \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    175\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m[how->\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mhow\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m,dtype->\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mdtype_str\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m]\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    176\u001b[0m     )\n\u001b[1;32m    177\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m f\n", "\u001b[0;31mNotImplementedError\u001b[0m: function is not implemented for this dtype: [how->mean,dtype->object]", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[2], line 61\u001b[0m\n\u001b[1;32m     59\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m level \u001b[38;5;129;01min\u001b[39;00m [\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mphylum\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mclass\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124morder\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfamily\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mgenus\u001b[39m\u001b[38;5;124m\"\u001b[39m]:\n\u001b[1;32m     60\u001b[0m     \u001b[38;5;28mprint\u001b[39m(level)\n\u001b[0;32m---> 61\u001b[0m     relpathabundance_list\u001b[38;5;241m.\u001b[39mappend(\u001b[43mdata\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgroupby\u001b[49m\u001b[43m(\u001b[49m\u001b[43mlevel\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmean\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m)\n\u001b[1;32m     62\u001b[0m     display(relpathabundance_list[\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m])\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/groupby/groupby.py:1855\u001b[0m, in \u001b[0;36mGroupBy.mean\u001b[0;34m(self, numeric_only, engine, engine_kwargs)\u001b[0m\n\u001b[1;32m   1853\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_numba_agg_general(sliding_mean, engine_kwargs)\n\u001b[1;32m   1854\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1855\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_cython_agg_general\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1856\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmean\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1857\u001b[0m \u001b[43m        \u001b[49m\u001b[43malt\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01m<PERSON>bda\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mx\u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mSeries\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmean\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnumeric_only\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mnumeric_only\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1858\u001b[0m \u001b[43m        \u001b[49m\u001b[43mnumeric_only\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mnumeric_only\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1859\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1860\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m result\u001b[38;5;241m.\u001b[39m__finalize__(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj, method\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mgroupby\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/groupby/groupby.py:1507\u001b[0m, in \u001b[0;36mGroupBy._cython_agg_general\u001b[0;34m(self, how, alt, numeric_only, min_count, **kwargs)\u001b[0m\n\u001b[1;32m   1503\u001b[0m         result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_agg_py_fallback(values, ndim\u001b[38;5;241m=\u001b[39mdata\u001b[38;5;241m.\u001b[39mndim, alt\u001b[38;5;241m=\u001b[39malt)\n\u001b[1;32m   1505\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m result\n\u001b[0;32m-> 1507\u001b[0m new_mgr \u001b[38;5;241m=\u001b[39m \u001b[43mdata\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgrouped_reduce\u001b[49m\u001b[43m(\u001b[49m\u001b[43marray_func\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1508\u001b[0m res \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_wrap_agged_manager(new_mgr)\n\u001b[1;32m   1509\u001b[0m out \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_wrap_aggregated_output(res)\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/internals/managers.py:1503\u001b[0m, in \u001b[0;36mBlockManager.grouped_reduce\u001b[0;34m(self, func)\u001b[0m\n\u001b[1;32m   1499\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m blk\u001b[38;5;241m.\u001b[39mis_object:\n\u001b[1;32m   1500\u001b[0m     \u001b[38;5;66;03m# split on object-dtype blocks bc some columns may raise\u001b[39;00m\n\u001b[1;32m   1501\u001b[0m     \u001b[38;5;66;03m#  while others do not.\u001b[39;00m\n\u001b[1;32m   1502\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m sb \u001b[38;5;129;01min\u001b[39;00m blk\u001b[38;5;241m.\u001b[39m_split():\n\u001b[0;32m-> 1503\u001b[0m         applied \u001b[38;5;241m=\u001b[39m \u001b[43msb\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mapply\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfunc\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1504\u001b[0m         result_blocks \u001b[38;5;241m=\u001b[39m extend_blocks(applied, result_blocks)\n\u001b[1;32m   1505\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/internals/blocks.py:329\u001b[0m, in \u001b[0;36mBlock.apply\u001b[0;34m(self, func, **kwargs)\u001b[0m\n\u001b[1;32m    323\u001b[0m \u001b[38;5;129m@final\u001b[39m\n\u001b[1;32m    324\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mapply\u001b[39m(\u001b[38;5;28mself\u001b[39m, func, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28mlist\u001b[39m[Block]:\n\u001b[1;32m    325\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    326\u001b[0m \u001b[38;5;124;03m    apply the function to my values; return a block if we are not\u001b[39;00m\n\u001b[1;32m    327\u001b[0m \u001b[38;5;124;03m    one\u001b[39;00m\n\u001b[1;32m    328\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 329\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvalues\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    331\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_split_op_result(result)\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/groupby/groupby.py:1503\u001b[0m, in \u001b[0;36mGroupBy._cython_agg_general.<locals>.array_func\u001b[0;34m(values)\u001b[0m\n\u001b[1;32m   1490\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mgrouper\u001b[38;5;241m.\u001b[39m_cython_operation(\n\u001b[1;32m   1491\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124maggregate\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m   1492\u001b[0m         values,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1496\u001b[0m         \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs,\n\u001b[1;32m   1497\u001b[0m     )\n\u001b[1;32m   1498\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mNotImplementedError\u001b[39;00m:\n\u001b[1;32m   1499\u001b[0m     \u001b[38;5;66;03m# generally if we have numeric_only=False\u001b[39;00m\n\u001b[1;32m   1500\u001b[0m     \u001b[38;5;66;03m# and non-applicable functions\u001b[39;00m\n\u001b[1;32m   1501\u001b[0m     \u001b[38;5;66;03m# try to python agg\u001b[39;00m\n\u001b[1;32m   1502\u001b[0m     \u001b[38;5;66;03m# TODO: shouldn't min_count matter?\u001b[39;00m\n\u001b[0;32m-> 1503\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_agg_py_fallback\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvalues\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mndim\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdata\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mndim\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43malt\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43malt\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1505\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m result\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/groupby/groupby.py:1457\u001b[0m, in \u001b[0;36mGroupBy._agg_py_fallback\u001b[0;34m(self, values, ndim, alt)\u001b[0m\n\u001b[1;32m   1452\u001b[0m     ser \u001b[38;5;241m=\u001b[39m df\u001b[38;5;241m.\u001b[39miloc[:, \u001b[38;5;241m0\u001b[39m]\n\u001b[1;32m   1454\u001b[0m \u001b[38;5;66;03m# We do not get here with UDFs, so we know that our dtype\u001b[39;00m\n\u001b[1;32m   1455\u001b[0m \u001b[38;5;66;03m#  should always be preserved by the implemented aggregations\u001b[39;00m\n\u001b[1;32m   1456\u001b[0m \u001b[38;5;66;03m# TODO: Is this exactly right; see WrappedCythonOp get_result_dtype?\u001b[39;00m\n\u001b[0;32m-> 1457\u001b[0m res_values \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgrouper\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43magg_series\u001b[49m\u001b[43m(\u001b[49m\u001b[43mser\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43malt\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpreserve_dtype\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m   1459\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(values, Categorical):\n\u001b[1;32m   1460\u001b[0m     \u001b[38;5;66;03m# Because we only get here with known dtype-preserving\u001b[39;00m\n\u001b[1;32m   1461\u001b[0m     \u001b[38;5;66;03m#  reductions, we cast back to Categorical.\u001b[39;00m\n\u001b[1;32m   1462\u001b[0m     \u001b[38;5;66;03m# TODO: if we ever get \"rank\" working, exclude it here.\u001b[39;00m\n\u001b[1;32m   1463\u001b[0m     res_values \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mtype\u001b[39m(values)\u001b[38;5;241m.\u001b[39m_from_sequence(res_values, dtype\u001b[38;5;241m=\u001b[39mvalues\u001b[38;5;241m.\u001b[39mdtype)\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/groupby/ops.py:994\u001b[0m, in \u001b[0;36mBaseGrouper.agg_series\u001b[0;34m(self, obj, func, preserve_dtype)\u001b[0m\n\u001b[1;32m    987\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(obj) \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m0\u001b[39m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(obj\u001b[38;5;241m.\u001b[39m_values, np\u001b[38;5;241m.\u001b[39mndarray):\n\u001b[1;32m    988\u001b[0m     \u001b[38;5;66;03m# we can preserve a little bit more aggressively with EA dtype\u001b[39;00m\n\u001b[1;32m    989\u001b[0m     \u001b[38;5;66;03m#  because maybe_cast_pointwise_result will do a try/except\u001b[39;00m\n\u001b[1;32m    990\u001b[0m     \u001b[38;5;66;03m#  with _from_sequence.  NB we are assuming here that _from_sequence\u001b[39;00m\n\u001b[1;32m    991\u001b[0m     \u001b[38;5;66;03m#  is sufficiently strict that it casts appropriately.\u001b[39;00m\n\u001b[1;32m    992\u001b[0m     preserve_dtype \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[0;32m--> 994\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_aggregate_series_pure_python\u001b[49m\u001b[43m(\u001b[49m\u001b[43mobj\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunc\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    996\u001b[0m npvalues \u001b[38;5;241m=\u001b[39m lib\u001b[38;5;241m.\u001b[39mmaybe_convert_objects(result, try_float\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m)\n\u001b[1;32m    997\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m preserve_dtype:\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/groupby/ops.py:1015\u001b[0m, in \u001b[0;36mBaseGrouper._aggregate_series_pure_python\u001b[0;34m(self, obj, func)\u001b[0m\n\u001b[1;32m   1012\u001b[0m splitter \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_splitter(obj, axis\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0\u001b[39m)\n\u001b[1;32m   1014\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m i, group \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(splitter):\n\u001b[0;32m-> 1015\u001b[0m     res \u001b[38;5;241m=\u001b[39m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mgroup\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1016\u001b[0m     res \u001b[38;5;241m=\u001b[39m libreduction\u001b[38;5;241m.\u001b[39mextract_result(res)\n\u001b[1;32m   1018\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m initialized:\n\u001b[1;32m   1019\u001b[0m         \u001b[38;5;66;03m# We only do this validation on the first iteration\u001b[39;00m\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/groupby/groupby.py:1857\u001b[0m, in \u001b[0;36mGroupBy.mean.<locals>.<lambda>\u001b[0;34m(x)\u001b[0m\n\u001b[1;32m   1853\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_numba_agg_general(sliding_mean, engine_kwargs)\n\u001b[1;32m   1854\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1855\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_cython_agg_general(\n\u001b[1;32m   1856\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmean\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m-> 1857\u001b[0m         alt\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mlambda\u001b[39;00m x: \u001b[43mSeries\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmean\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnumeric_only\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mnumeric_only\u001b[49m\u001b[43m)\u001b[49m,\n\u001b[1;32m   1858\u001b[0m         numeric_only\u001b[38;5;241m=\u001b[39mnumeric_only,\n\u001b[1;32m   1859\u001b[0m     )\n\u001b[1;32m   1860\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m result\u001b[38;5;241m.\u001b[39m__finalize__(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj, method\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mgroupby\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/generic.py:11556\u001b[0m, in \u001b[0;36mNDFrame._add_numeric_operations.<locals>.mean\u001b[0;34m(self, axis, skipna, numeric_only, **kwargs)\u001b[0m\n\u001b[1;32m  11539\u001b[0m \u001b[38;5;129m@doc\u001b[39m(\n\u001b[1;32m  11540\u001b[0m     _num_doc,\n\u001b[1;32m  11541\u001b[0m     desc\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mReturn the mean of the values over the requested axis.\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m  11554\u001b[0m     \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs,\n\u001b[1;32m  11555\u001b[0m ):\n\u001b[0;32m> 11556\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mNDFrame\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmean\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mskipna\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnumeric_only\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/generic.py:11201\u001b[0m, in \u001b[0;36mNDFrame.mean\u001b[0;34m(self, axis, skipna, numeric_only, **kwargs)\u001b[0m\n\u001b[1;32m  11194\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mmean\u001b[39m(\n\u001b[1;32m  11195\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m  11196\u001b[0m     axis: Axis \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m  11199\u001b[0m     \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs,\n\u001b[1;32m  11200\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Series \u001b[38;5;241m|\u001b[39m \u001b[38;5;28mfloat\u001b[39m:\n\u001b[0;32m> 11201\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_stat_function\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m  11202\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmean\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnanops\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mnanmean\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mskipna\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnumeric_only\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\n\u001b[1;32m  11203\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/generic.py:11158\u001b[0m, in \u001b[0;36mNDFrame._stat_function\u001b[0;34m(self, name, func, axis, skipna, numeric_only, **kwargs)\u001b[0m\n\u001b[1;32m  11154\u001b[0m     nv\u001b[38;5;241m.\u001b[39mvalidate_stat_func((), kwargs, fname\u001b[38;5;241m=\u001b[39mname)\n\u001b[1;32m  11156\u001b[0m validate_bool_kwarg(skipna, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mskipna\u001b[39m\u001b[38;5;124m\"\u001b[39m, none_allowed\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m)\n\u001b[0;32m> 11158\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_reduce\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m  11159\u001b[0m \u001b[43m    \u001b[49m\u001b[43mfunc\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mname\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mname\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maxis\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mskipna\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mskipna\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnumeric_only\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mnumeric_only\u001b[49m\n\u001b[1;32m  11160\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/series.py:4670\u001b[0m, in \u001b[0;36mSeries._reduce\u001b[0;34m(self, op, name, axis, skipna, numeric_only, filter_type, **kwds)\u001b[0m\n\u001b[1;32m   4665\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(\n\u001b[1;32m   4666\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mSeries.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mname\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m does not allow \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mkwd_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnumeric_only\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   4667\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mwith non-numeric dtypes.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   4668\u001b[0m     )\n\u001b[1;32m   4669\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m np\u001b[38;5;241m.\u001b[39merrstate(\u001b[38;5;28mall\u001b[39m\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mignore\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n\u001b[0;32m-> 4670\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mop\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdelegate\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mskipna\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mskipna\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwds\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/nanops.py:96\u001b[0m, in \u001b[0;36mdisallow.__call__.<locals>._f\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m     94\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m     95\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m np\u001b[38;5;241m.\u001b[39merrstate(invalid\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mignore\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n\u001b[0;32m---> 96\u001b[0m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[43mf\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     97\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mV<PERSON><PERSON>Error\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m     98\u001b[0m     \u001b[38;5;66;03m# we want to transform an object array\u001b[39;00m\n\u001b[1;32m     99\u001b[0m     \u001b[38;5;66;03m# ValueError message to the more typical TypeError\u001b[39;00m\n\u001b[1;32m    100\u001b[0m     \u001b[38;5;66;03m# e.g. this is normally a disallowed function on\u001b[39;00m\n\u001b[1;32m    101\u001b[0m     \u001b[38;5;66;03m# object arrays that contain strings\u001b[39;00m\n\u001b[1;32m    102\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m is_object_dtype(args[\u001b[38;5;241m0\u001b[39m]):\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/nanops.py:158\u001b[0m, in \u001b[0;36mbottleneck_switch.__call__.<locals>.f\u001b[0;34m(values, axis, skipna, **kwds)\u001b[0m\n\u001b[1;32m    156\u001b[0m         result \u001b[38;5;241m=\u001b[39m alt(values, axis\u001b[38;5;241m=\u001b[39maxis, skipna\u001b[38;5;241m=\u001b[39mskipna, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwds)\n\u001b[1;32m    157\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 158\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[43malt\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvalues\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maxis\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mskipna\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mskipna\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwds\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    160\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m result\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/nanops.py:421\u001b[0m, in \u001b[0;36m_datetimelike_compat.<locals>.new_func\u001b[0;34m(values, axis, skipna, mask, **kwargs)\u001b[0m\n\u001b[1;32m    418\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m datetimelike \u001b[38;5;129;01mand\u001b[39;00m mask \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    419\u001b[0m     mask \u001b[38;5;241m=\u001b[39m isna(values)\n\u001b[0;32m--> 421\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvalues\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maxis\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mskipna\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mskipna\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmask\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmask\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    423\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m datetimelike:\n\u001b[1;32m    424\u001b[0m     result \u001b[38;5;241m=\u001b[39m _wrap_results(result, orig_values\u001b[38;5;241m.\u001b[39mdtype, fill_value\u001b[38;5;241m=\u001b[39miNaT)\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/nanops.py:727\u001b[0m, in \u001b[0;36mnanmean\u001b[0;34m(values, axis, skipna, mask)\u001b[0m\n\u001b[1;32m    724\u001b[0m     dtype_count \u001b[38;5;241m=\u001b[39m dtype\n\u001b[1;32m    726\u001b[0m count \u001b[38;5;241m=\u001b[39m _get_counts(values\u001b[38;5;241m.\u001b[39mshape, mask, axis, dtype\u001b[38;5;241m=\u001b[39mdtype_count)\n\u001b[0;32m--> 727\u001b[0m the_sum \u001b[38;5;241m=\u001b[39m _ensure_numeric(\u001b[43mvalues\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msum\u001b[49m\u001b[43m(\u001b[49m\u001b[43maxis\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdtype\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdtype_sum\u001b[49m\u001b[43m)\u001b[49m)\n\u001b[1;32m    729\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m axis \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mgetattr\u001b[39m(the_sum, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mndim\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mFalse\u001b[39;00m):\n\u001b[1;32m    730\u001b[0m     count \u001b[38;5;241m=\u001b[39m cast(np\u001b[38;5;241m.\u001b[39mndarray, count)\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/numpy/core/_methods.py:49\u001b[0m, in \u001b[0;36m_sum\u001b[0;34m(a, axis, dtype, out, keepdims, initial, where)\u001b[0m\n\u001b[1;32m     47\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_sum\u001b[39m(a, axis\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, dtype\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, out\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, keepdims\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[1;32m     48\u001b[0m          initial\u001b[38;5;241m=\u001b[39m_NoValue, where\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m):\n\u001b[0;32m---> 49\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mumr_sum\u001b[49m\u001b[43m(\u001b[49m\u001b[43ma\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdtype\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mout\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkeepdims\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minitial\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mwhere\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[0;31mTypeError\u001b[0m: can only concatenate str (not \"int\") to str"]}], "source": ["# load data and put together\n", "\n", "data=pd.read_csv(\"data_pathwaypipeline/strains_tested.csv\")\n", "\n", "data['strain'] = data['strain'].str.replace('_',' ')\n", "\n", "#add data on pathway abundance\n", "data_probut=pd.read_excel(\"data_pathwaypipeline/strains_carryingpathways.xlsx\")\n", "display(data)\n", "display(data_probut)\n", "\n", "data=data.merge(data_probut,on=\"strain\",how='outer').sort_values(by='strain')\n", "display(data)\n", "display(data.count())\n", "\n", "def get_genus(row):\n", "    try:\n", "        curgen=row['strain'].replace(\"-\",\" \").split(' ')[0]\n", "    except:\n", "        curgen=row['strain']\n", "    row[\"genus\"]=curgen\n", "    return row\n", "data=data.apply(get_genus, axis=1)\n", "display(data[\"genus\"].unique())\n", "display(data[\"genus\"].nunique())\n", "\n", "#add genus data\n", "data_taxonomic=pd.read_csv(\"data_pathwaypipeline/strains_taxonomicinfo.csv\")\n", "\n", "data=data.merge(data_taxonomic,on=\"genus\",how=\"outer\")\n", "data['pathway'] = data['pathway'].str.replace(' ','') \n", "print(data[\"pathway\"].unique())\n", "def add_pathwayinformation(row):\n", "    if row['pathway'] in ['4Ami','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>']:\n", "        row[\"BUT\"]=1\n", "    else: \n", "        row[\"BUT\"]=0\n", "        \n", "    if row['pathway'] in ['SP','Acr','Pro','WWC']:\n", "        row[\"PRO\"]=1\n", "    else: \n", "        row[\"PRO\"]=0\n", "    #for pathway in ['<PERSON><PERSON>','<PERSON>',' <PERSON><PERSON>','Ace','4Ami','Acr','Pro','WWC']:\n", "    #    if row[\"pathway\"]==pathway:\n", "    #        row[pathway]=1\n", "    #    else:\n", "    #        row[pathway]=0\n", "    return row\n", "data=data.apply(add_pathwayinformation, axis=1)\n", "\n", "display(data)\n", "\n", "data=data.drop(\"Unnamed: 0\", axis=1)\n", "\n", "#df.drop('column_name', axis=1)\n", "data.to_csv(\"data_pathwaypipeline/combined_info.csv\")\n", "pd.set_option('display.max_rows', 500)\n", "relpathabundance_list=[]\n", "for level in [\"phylum\",\"class\",\"order\",\"family\",\"genus\"]:\n", "    print(level)\n", "    relpathabundance_list.append(data.groupby(level).mean())\n", "    display(relpathabundance_list[-1])\n"]}, {"cell_type": "markdown", "id": "577c39bd-2e40-4c81-9517-32902144081a", "metadata": {}, "source": ["# Load measured yield data"]}, {"cell_type": "code", "execution_count": 3, "id": "6900def4-b21a-4543-aabb-7ecf366157f1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0               Bacteroides uniformis\n", "1                Bacteroides fragilis\n", "2                  Bacteroides ovatus\n", "3        Bacteroides thetaiotaomicron\n", "4              Bacteroides <PERSON>\n", "5                Phocaeicola vulgatus\n", "6                    Prevotella copri\n", "7          Parabacteroides distasonis\n", "8              Roseburia intestinalis\n", "9                 Eubacterium rectale\n", "10                Lachnospira eligens\n", "11                  Dorea longicatena\n", "12    Fusicatenibacter saccharivorans\n", "13                   <PERSON><PERSON><PERSON>ae\n", "14          <PERSON>lautia hydrogenotrophica\n", "15       Faecalibacterium <PERSON>ii\n", "16                Eubacterium siraeum\n", "17                Ruminococcus bromii\n", "18             Bifidobacterium longum\n", "19       Bifidobacterium adolescentis\n", "20            Collinsella aerofaciens\n", "21                   Escherichia coli\n", "Name: species, dtype: object\n"]}, {"data": {"text/plain": ["Index(['species_HPLCname', 'species_x', 'new_species', 'species.1',\n", "       'species_short', 'new_genus', 'genus', 'new_family', 'family',\n", "       'new_order', 'order', 'new_class', 'class', 'new_phylum', 'phylum',\n", "       'Unnamed: 0', 'species_y', 'glucose', 'acetate', 'propionate',\n", "       'succinate', 'lactate', 'butyrate', 'formate', 'maltose', 'medium',\n", "       'species'],\n", "      dtype='object')"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>species_HPLCname</th>\n", "      <th>species_x</th>\n", "      <th>new_species</th>\n", "      <th>species.1</th>\n", "      <th>species_short</th>\n", "      <th>new_genus</th>\n", "      <th>genus</th>\n", "      <th>new_family</th>\n", "      <th>family</th>\n", "      <th>new_order</th>\n", "      <th>...</th>\n", "      <th>glucose</th>\n", "      <th>acetate</th>\n", "      <th>propionate</th>\n", "      <th>succinate</th>\n", "      <th>lactate</th>\n", "      <th>butyrate</th>\n", "      <th>formate</th>\n", "      <th>maltose</th>\n", "      <th>medium</th>\n", "      <th>species</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON>.uniformis</td>\n", "      <td>Bacteroides uniformis</td>\n", "      <td>Bacteroides uniformis</td>\n", "      <td>Bacteroides uniformis</td>\n", "      <td><PERSON>. uniformis</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>...</td>\n", "      <td>-6.650701</td>\n", "      <td>3.610049</td>\n", "      <td>0.292439</td>\n", "      <td>3.280870e-16</td>\n", "      <td>10.845330</td>\n", "      <td>0.009401</td>\n", "      <td>4.765271</td>\n", "      <td>0.000000</td>\n", "      <td>YCA</td>\n", "      <td>Bacteroides uniformis</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON><PERSON>fragilis</td>\n", "      <td>Bacteroides fragilis</td>\n", "      <td>Bacteroides fragilis</td>\n", "      <td>Bacteroides fragilis</td>\n", "      <td><PERSON><PERSON> fragilis</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>...</td>\n", "      <td>-4.832237</td>\n", "      <td>3.496001</td>\n", "      <td>0.380547</td>\n", "      <td>2.658095e+00</td>\n", "      <td>0.499204</td>\n", "      <td>0.000000</td>\n", "      <td>2.187005</td>\n", "      <td>0.000000</td>\n", "      <td>YCA</td>\n", "      <td>Bacteroides fragilis</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON><PERSON>ovatus</td>\n", "      <td>Bacteroides ovatus</td>\n", "      <td>Bacteroides ovatus</td>\n", "      <td>Bacteroides ovatus</td>\n", "      <td><PERSON><PERSON> ovatus</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>...</td>\n", "      <td>-4.210851</td>\n", "      <td>8.802434</td>\n", "      <td>0.306027</td>\n", "      <td>1.523860e+00</td>\n", "      <td>0.307526</td>\n", "      <td>0.014638</td>\n", "      <td>10.289419</td>\n", "      <td>0.000000</td>\n", "      <td>YCA</td>\n", "      <td>Bacteroides ovatus</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON>.theta</td>\n", "      <td>Bacteroides thetaiotaomicron</td>\n", "      <td>Bacteroides thetaiotaomicron</td>\n", "      <td>Bacteroides thetaiotaomicron</td>\n", "      <td><PERSON>. theta</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>...</td>\n", "      <td>-6.248500</td>\n", "      <td>4.504513</td>\n", "      <td>1.518513</td>\n", "      <td>2.332290e+00</td>\n", "      <td>0.728622</td>\n", "      <td>0.000000</td>\n", "      <td>0.925419</td>\n", "      <td>0.000000</td>\n", "      <td>YCA</td>\n", "      <td>Bacteroides thetaiotaomicron</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Bacteroides <PERSON>ii</td>\n", "      <td>Bacteroides <PERSON>ii</td>\n", "      <td>Bacteroides <PERSON>ii</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>...</td>\n", "      <td>-4.654721</td>\n", "      <td>3.666919</td>\n", "      <td>0.999781</td>\n", "      <td>2.024829e+00</td>\n", "      <td>0.110719</td>\n", "      <td>0.014567</td>\n", "      <td>1.813101</td>\n", "      <td>0.000000</td>\n", "      <td>YCA</td>\n", "      <td>Bacteroides <PERSON>ii</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td><PERSON><PERSON>vulgatus</td>\n", "      <td>Phocaeicola vulgatus</td>\n", "      <td>Phocaeicola vulgatus</td>\n", "      <td>Bacteroides vulgatus</td>\n", "      <td><PERSON><PERSON> vulgatus</td>\n", "      <td>Phocaeicola</td>\n", "      <td>Phocaeicola</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>...</td>\n", "      <td>-5.365080</td>\n", "      <td>4.885603</td>\n", "      <td>0.627039</td>\n", "      <td>3.538685e+00</td>\n", "      <td>0.087761</td>\n", "      <td>0.000000</td>\n", "      <td>2.497726</td>\n", "      <td>0.000000</td>\n", "      <td>YCA</td>\n", "      <td>Phocaeicola vulgatus</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td><PERSON><PERSON>copri</td>\n", "      <td>Prevotella copri</td>\n", "      <td>Prevotella copri</td>\n", "      <td>Prevotella copri</td>\n", "      <td><PERSON><PERSON> copri</td>\n", "      <td>Prevotella</td>\n", "      <td>Prevotella</td>\n", "      <td>Prevotellaceae</td>\n", "      <td>Prevotellaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>...</td>\n", "      <td>-8.366688</td>\n", "      <td>4.894400</td>\n", "      <td>0.156131</td>\n", "      <td>2.300822e-16</td>\n", "      <td>12.054598</td>\n", "      <td>0.037772</td>\n", "      <td>5.919670</td>\n", "      <td>0.000000</td>\n", "      <td>YCA</td>\n", "      <td>Prevotella copri</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td><PERSON><PERSON>di<PERSON></td>\n", "      <td>Parabacteroides distasonis</td>\n", "      <td>Parabacteroides distasonis</td>\n", "      <td>Bacteroides distasonis</td>\n", "      <td><PERSON><PERSON> di<PERSON></td>\n", "      <td>Parabacteroides</td>\n", "      <td>Parabacteroides</td>\n", "      <td>Tannerellaceae</td>\n", "      <td>Tannerellaceae</td>\n", "      <td>Bacteroidales</td>\n", "      <td>...</td>\n", "      <td>-5.080898</td>\n", "      <td>2.790486</td>\n", "      <td>1.174451</td>\n", "      <td>2.204980e+00</td>\n", "      <td>0.188702</td>\n", "      <td>0.033736</td>\n", "      <td>1.513597</td>\n", "      <td>0.000000</td>\n", "      <td>YCA</td>\n", "      <td>Parabacteroides distasonis</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td><PERSON><PERSON>intestinalis</td>\n", "      <td><PERSON><PERSON><PERSON> intestinalis</td>\n", "      <td><PERSON><PERSON><PERSON> intestinalis</td>\n", "      <td><PERSON><PERSON><PERSON> intestinalis</td>\n", "      <td><PERSON><PERSON> intestinalis</td>\n", "      <td>Roseburia</td>\n", "      <td>Roseburia</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Eubacteriales</td>\n", "      <td>...</td>\n", "      <td>-5.271650</td>\n", "      <td>3.204623</td>\n", "      <td>1.135440</td>\n", "      <td>1.358149e-01</td>\n", "      <td>0.003852</td>\n", "      <td>2.923401</td>\n", "      <td>1.280115</td>\n", "      <td>0.000000</td>\n", "      <td>YCA</td>\n", "      <td><PERSON><PERSON><PERSON> intestinalis</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td><PERSON><PERSON>rectale</td>\n", "      <td>Eubacterium rectale</td>\n", "      <td>Agathobacter rectalis</td>\n", "      <td>Eubacterium rectale</td>\n", "      <td><PERSON><PERSON> rectale</td>\n", "      <td>Agathobacter</td>\n", "      <td>Lachnospiraceae_NA</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Eubacteriales</td>\n", "      <td>...</td>\n", "      <td>-6.572609</td>\n", "      <td>2.868648</td>\n", "      <td>0.729635</td>\n", "      <td>5.716495e-02</td>\n", "      <td>0.069866</td>\n", "      <td>3.887095</td>\n", "      <td>1.014707</td>\n", "      <td>0.000000</td>\n", "      <td>YCA</td>\n", "      <td>Eubacterium rectale</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td><PERSON><PERSON>eligens</td>\n", "      <td>Lachnospira eligens</td>\n", "      <td>Lachnospira eligens</td>\n", "      <td>Lachnospira eligens</td>\n", "      <td><PERSON><PERSON> eligens</td>\n", "      <td>Lachnospira</td>\n", "      <td>Lachnospira</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Eubacteriales</td>\n", "      <td>...</td>\n", "      <td>-12.382019</td>\n", "      <td>2.201643</td>\n", "      <td>0.719664</td>\n", "      <td>1.147577e-01</td>\n", "      <td>1.088904</td>\n", "      <td>0.206544</td>\n", "      <td>5.097098</td>\n", "      <td>0.000000</td>\n", "      <td>YCA</td>\n", "      <td>Lachnospira eligens</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td><PERSON><PERSON>long<PERSON>a</td>\n", "      <td><PERSON><PERSON> long<PERSON>a</td>\n", "      <td><PERSON><PERSON> long<PERSON>a</td>\n", "      <td><PERSON><PERSON> long<PERSON>a</td>\n", "      <td><PERSON><PERSON> long<PERSON>a</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Eubacteriales</td>\n", "      <td>...</td>\n", "      <td>-5.729152</td>\n", "      <td>8.183766</td>\n", "      <td>0.441760</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.019365</td>\n", "      <td>0.052896</td>\n", "      <td>8.208101</td>\n", "      <td>0.000000</td>\n", "      <td>YCA</td>\n", "      <td><PERSON><PERSON> long<PERSON>a</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>F.saccharivorans</td>\n", "      <td>Fusicatenibacter saccharivorans</td>\n", "      <td>Fusicatenibacter saccharivorans</td>\n", "      <td>Fusicatenibacter saccharivorans</td>\n", "      <td>F. saccharivorans</td>\n", "      <td>Fusicatenibacter</td>\n", "      <td>Fusicatenibacter</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Eubacteriales</td>\n", "      <td>...</td>\n", "      <td>-6.417189</td>\n", "      <td>3.962337</td>\n", "      <td>6.785044</td>\n", "      <td>2.011451e+00</td>\n", "      <td>5.687592</td>\n", "      <td>0.044358</td>\n", "      <td>5.587348</td>\n", "      <td>0.000000</td>\n", "      <td>YCA</td>\n", "      <td>Fusicatenibacter saccharivorans</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td><PERSON><PERSON>we<PERSON></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Eubacteriales</td>\n", "      <td>...</td>\n", "      <td>-7.015797</td>\n", "      <td>12.418256</td>\n", "      <td>0.105014</td>\n", "      <td>2.170431e+00</td>\n", "      <td>0.666261</td>\n", "      <td>0.194774</td>\n", "      <td>1.113223</td>\n", "      <td>0.000000</td>\n", "      <td>YCA</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td><PERSON><PERSON>hydrogenotrophica</td>\n", "      <td><PERSON><PERSON><PERSON> hydrogenotrophica</td>\n", "      <td><PERSON><PERSON><PERSON> hydrogenotrophica</td>\n", "      <td><PERSON><PERSON><PERSON> hydrogenotrophica</td>\n", "      <td><PERSON><PERSON> hydrogenotrophica</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Eubacteriales</td>\n", "      <td>...</td>\n", "      <td>-3.001147</td>\n", "      <td>12.131892</td>\n", "      <td>0.515252</td>\n", "      <td>6.664595e-03</td>\n", "      <td>0.804467</td>\n", "      <td>0.296809</td>\n", "      <td>0.671142</td>\n", "      <td>0.000000</td>\n", "      <td>YCA</td>\n", "      <td><PERSON><PERSON><PERSON> hydrogenotrophica</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Faecalibacterium <PERSON>ii</td>\n", "      <td>Faecalibacterium <PERSON>ii</td>\n", "      <td>Faecalibacterium <PERSON>ii</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Faecalibacterium</td>\n", "      <td>Faecalibacterium</td>\n", "      <td>Oscillospiraceae</td>\n", "      <td>Oscillospiraceae</td>\n", "      <td>Eubacteriales</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>0.050004</td>\n", "      <td>0.695780</td>\n", "      <td>5.799652e-02</td>\n", "      <td>0.000000</td>\n", "      <td>4.366987</td>\n", "      <td>6.256170</td>\n", "      <td>-2.670849</td>\n", "      <td>YCA</td>\n", "      <td>Faecalibacterium <PERSON>ii</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td><PERSON><PERSON>sir<PERSON></td>\n", "      <td>Eubacterium siraeum</td>\n", "      <td>Eubacterium siraeum</td>\n", "      <td>Eubacterium siraeum</td>\n", "      <td><PERSON><PERSON> sir<PERSON></td>\n", "      <td>[Eubacterium]</td>\n", "      <td>Oscillospiraceae_NA</td>\n", "      <td>Oscillospiraceae</td>\n", "      <td>Oscillospiraceae</td>\n", "      <td>Eubacteriales</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>12.611399</td>\n", "      <td>0.311797</td>\n", "      <td>1.159698e-02</td>\n", "      <td>0.082335</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>-13.671067</td>\n", "      <td>YCA</td>\n", "      <td>Eubacterium siraeum</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td><PERSON><PERSON>br<PERSON></td>\n", "      <td>Ruminococcus bromii</td>\n", "      <td>Ruminococcus bromii</td>\n", "      <td>Ruminococcus bromii</td>\n", "      <td><PERSON><PERSON> br<PERSON></td>\n", "      <td>Ruminococcus</td>\n", "      <td>Ruminococcus</td>\n", "      <td>Oscillospiraceae</td>\n", "      <td>Oscillospiraceae</td>\n", "      <td>Eubacteriales</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>7.657412</td>\n", "      <td>2.915839</td>\n", "      <td>6.841248e-02</td>\n", "      <td>3.961092</td>\n", "      <td>0.354454</td>\n", "      <td>3.434732</td>\n", "      <td>-1.526372</td>\n", "      <td>YCA</td>\n", "      <td>Ruminococcus bromii</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td><PERSON><PERSON>longum</td>\n", "      <td>Bifidobacterium longum</td>\n", "      <td>Bifidobacterium longum</td>\n", "      <td>Bifidobacterium longum</td>\n", "      <td><PERSON><PERSON> longum</td>\n", "      <td>Bifidobacterium</td>\n", "      <td>Bifidobacterium</td>\n", "      <td>Bifidobacteriaceae</td>\n", "      <td>Bifidobacteriaceae</td>\n", "      <td>Bifidobacteriales</td>\n", "      <td>...</td>\n", "      <td>-9.243031</td>\n", "      <td>13.612177</td>\n", "      <td>1.578880</td>\n", "      <td>4.546007e-01</td>\n", "      <td>3.149715</td>\n", "      <td>0.000000</td>\n", "      <td>1.157540</td>\n", "      <td>0.000000</td>\n", "      <td>YCA</td>\n", "      <td>Bifidobacterium longum</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td><PERSON><PERSON>adolescent<PERSON></td>\n", "      <td>Bifidobacterium adolescentis</td>\n", "      <td>Bifidobacterium adolescentis</td>\n", "      <td>Bifidobacterium adolescentis</td>\n", "      <td><PERSON><PERSON> adolescent<PERSON></td>\n", "      <td>Bifidobacterium</td>\n", "      <td>Bifidobacterium</td>\n", "      <td>Bifidobacteriaceae</td>\n", "      <td>Bifidobacteriaceae</td>\n", "      <td>Bifidobacteriales</td>\n", "      <td>...</td>\n", "      <td>-8.238885</td>\n", "      <td>12.114113</td>\n", "      <td>2.233406</td>\n", "      <td>5.445836e-01</td>\n", "      <td>3.221920</td>\n", "      <td>0.000000</td>\n", "      <td>0.998964</td>\n", "      <td>0.000000</td>\n", "      <td>YCA</td>\n", "      <td>Bifidobacterium adolescentis</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>C.aerofaciens</td>\n", "      <td>Collinsella aerofaciens</td>\n", "      <td>Collinsella aerofaciens</td>\n", "      <td>Collinsella aerofaciens</td>\n", "      <td>C. aerofaciens</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Coriobacteriaceae</td>\n", "      <td>Coriobacteriaceae</td>\n", "      <td>Coriobacteriales</td>\n", "      <td>...</td>\n", "      <td>-6.349981</td>\n", "      <td>4.440344</td>\n", "      <td>1.756295</td>\n", "      <td>2.722113e-01</td>\n", "      <td>2.050443</td>\n", "      <td>0.387110</td>\n", "      <td>5.122145</td>\n", "      <td>0.000000</td>\n", "      <td>YCA</td>\n", "      <td>Collinsella aerofaciens</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td><PERSON><PERSON>coli</td>\n", "      <td>Escherichia coli</td>\n", "      <td>Escherichia coli</td>\n", "      <td>Escherichia coli</td>\n", "      <td><PERSON><PERSON> coli</td>\n", "      <td>Escherichia</td>\n", "      <td>Escherichia</td>\n", "      <td>Enterobacteriaceae</td>\n", "      <td>Enterobacteriaceae</td>\n", "      <td>Enterobacterales</td>\n", "      <td>...</td>\n", "      <td>-3.814797</td>\n", "      <td>9.180978</td>\n", "      <td>0.045235</td>\n", "      <td>1.626422e+00</td>\n", "      <td>0.404698</td>\n", "      <td>0.055980</td>\n", "      <td>10.117315</td>\n", "      <td>0.000000</td>\n", "      <td>YCA</td>\n", "      <td>Escherichia coli</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>22 rows × 27 columns</p>\n", "</div>"], "text/plain": ["       species_HPLCname                        species_x  \\\n", "0           B.uniformis            Bacteroides uniformis   \n", "1            B.fragilis             Bacteroides fragilis   \n", "2              B.ovatus               Bacteroides ovatus   \n", "3               B.theta     Bacteroides thetaiotaomicron   \n", "4          <PERSON><PERSON>finegoldii           Bacteroides finegoldii   \n", "5            B.vulgatus             Phocaeicola vulgatus   \n", "6               P.copri                 Prevotella copri   \n", "7         P.distastonis       Parabacteroides distasonis   \n", "8        R.intestinalis           Roseburia intestinalis   \n", "9             E.rectale              Eubacterium rectale   \n", "10            L.eligens              Lachnospira eligens   \n", "11        <PERSON>.longicatena                Dorea longicatena   \n", "12     F.saccharivorans  Fusicatenibacter saccharivorans   \n", "13            <PERSON><PERSON>we<PERSON><PERSON>i                 <PERSON> wexlerae   \n", "14  B.hydrogenotrophica        Blautia hydrogenotrophica   \n", "15        F.<PERSON>ii     Faecalibacterium pra<PERSON>ii   \n", "16            E.siraeum              Eubacterium siraeum   \n", "17             R.bromii              Ruminococcus bromii   \n", "18             B.longum           Bifidobacterium longum   \n", "19       B.adolescentis     Bifidobacterium adolescentis   \n", "20        C.aerofaciens          Collinsella aerofaciens   \n", "21               E.coli                 Escherichia coli   \n", "\n", "                        new_species                        species.1  \\\n", "0             Bacteroides uniformis            Bacteroides uniformis   \n", "1              Bacteroides fragilis             Bacteroides fragilis   \n", "2                Bacteroides ovatus               Bacteroides ovatus   \n", "3      Bacteroides thetaiotaomicron     Bacteroides thetaiotaomicron   \n", "4            Bacteroides finegoldii           Bacteroides finegoldii   \n", "5              Phocaeicola vulgatus             Bacteroides vulgatus   \n", "6                  Prevotella copri                 Prevotella copri   \n", "7        Parabacteroides distasonis           Bacteroides distasonis   \n", "8            Roseburia intestinalis           Roseburia intestinalis   \n", "9             Agathobacter rectalis              Eubacterium rectale   \n", "10              Lachnospira eligens              Lachnospira eligens   \n", "11                Dorea longicatena                Dorea longicatena   \n", "12  Fusicatenibacter saccharivorans  Fusicatenibacter saccharivorans   \n", "13                 <PERSON><PERSON><PERSON> wexlerae                 B<PERSON><PERSON> wexlerae   \n", "14        Blautia hydrogenotrophica        Blautia hydrogenotrophica   \n", "15     Faecalibacterium pra<PERSON>ii     Faecalibacterium pra<PERSON>ii   \n", "16              Eubacterium siraeum              Eubacterium siraeum   \n", "17              Ruminococcus bromii              Ruminococcus bromii   \n", "18           Bifidobacterium longum           Bifidobacterium longum   \n", "19     Bifidobacterium adolescentis     Bifidobacterium adolescentis   \n", "20          Collinsella aerofaciens          Collinsella aerofaciens   \n", "21                 Escherichia coli                 Escherichia coli   \n", "\n", "           species_short         new_genus                genus  \\\n", "0           B. uniformis       Bacteroides          Bacteroides   \n", "1            B. fragilis       Bacteroides          Bacteroides   \n", "2              B. ovatus       Bacteroides          Bacteroides   \n", "3               B. theta       Bacteroides          Bacteroides   \n", "4          <PERSON><PERSON>ii       Bacteroides          Bacteroides   \n", "5            <PERSON><PERSON> vulgatus       Phocaeicola          Phocaeicola   \n", "6               P. copri        Prevotella           Prevotella   \n", "7         P. distastonis   Parabacteroides      Parabacteroides   \n", "8        <PERSON>. intestinalis         <PERSON>buria            Roseburia   \n", "9             E. rectale      Agathobacter   Lachnospiraceae_NA   \n", "10            L. eligens       Lachnospira          Lachnospira   \n", "11        <PERSON>. longicatena             Dorea                Dorea   \n", "12     F. saccharivorans  Fusicatenibacter     Fusicatenibacter   \n", "13           <PERSON><PERSON> <PERSON><PERSON><PERSON>ae           <PERSON>lau<PERSON>   \n", "14  <PERSON>. hydrogenotrophica           <PERSON>   \n", "15        <PERSON><PERSON>ii  Faecalibacterium     Faecalibacterium   \n", "16            E. siraeum     [Eubacterium]  Oscillospiraceae_NA   \n", "17             <PERSON>. bromii      Ruminococcus         Ruminococcus   \n", "18             B. longum   Bifidobacterium      Bifidobacterium   \n", "19       B. adolescentis   Bifidobacterium      Bifidobacterium   \n", "20        C. aerofaciens       Collins<PERSON>   \n", "21               E. coli       Escherichia          Escherichia   \n", "\n", "            new_family              family          new_order  ...    glucose  \\\n", "0       Bacteroidaceae      Bacteroidaceae      Bacteroidales  ...  -6.650701   \n", "1       Bacteroidaceae      Bacteroidaceae      Bacteroidales  ...  -4.832237   \n", "2       Bacteroidaceae      Bacteroidaceae      Bacteroidales  ...  -4.210851   \n", "3       Bacteroidaceae      Bacteroidaceae      Bacteroidales  ...  -6.248500   \n", "4       Bacteroidaceae      Bacteroidaceae      Bacteroidales  ...  -4.654721   \n", "5       Bacteroidaceae      Bacteroidaceae      Bacteroidales  ...  -5.365080   \n", "6       Prevotellaceae      Prevotellaceae      Bacteroidales  ...  -8.366688   \n", "7       Tannerellaceae      Tannerellaceae      Bacteroidales  ...  -5.080898   \n", "8      Lachnospiraceae     Lachnospiraceae      Eubacteriales  ...  -5.271650   \n", "9      Lachnospiraceae     Lachnospiraceae      Eubacteriales  ...  -6.572609   \n", "10     Lachnospiraceae     Lachnospiraceae      Eubacteriales  ... -12.382019   \n", "11     Lachnospiraceae     Lachnospiraceae      Eubacteriales  ...  -5.729152   \n", "12     Lachnospiraceae     Lachnospiraceae      Eubacteriales  ...  -6.417189   \n", "13     Lachnospiraceae     Lachnospiraceae      Eubacteriales  ...  -7.015797   \n", "14     Lachnospiraceae     Lachnospiraceae      Eubacteriales  ...  -3.001147   \n", "15    Oscillospiraceae    Oscillospiraceae      Eubacteriales  ...   0.000000   \n", "16    Oscillospiraceae    Oscillospiraceae      Eubacteriales  ...   0.000000   \n", "17    Oscillospiraceae    Oscillospiraceae      Eubacteriales  ...   0.000000   \n", "18  Bifidobacteriaceae  Bifidobacteriaceae  Bifidobacteriales  ...  -9.243031   \n", "19  Bifidobacteriaceae  Bifidobacteriaceae  Bifidobacteriales  ...  -8.238885   \n", "20   Coriobacteriaceae   Coriobacteriaceae   Coriobacteriales  ...  -6.349981   \n", "21  Enterobacteriaceae  Enterobacteriaceae   Enterobacterales  ...  -3.814797   \n", "\n", "      acetate propionate     succinate    lactate  butyrate    formate  \\\n", "0    3.610049   0.292439  3.280870e-16  10.845330  0.009401   4.765271   \n", "1    3.496001   0.380547  2.658095e+00   0.499204  0.000000   2.187005   \n", "2    8.802434   0.306027  1.523860e+00   0.307526  0.014638  10.289419   \n", "3    4.504513   1.518513  2.332290e+00   0.728622  0.000000   0.925419   \n", "4    3.666919   0.999781  2.024829e+00   0.110719  0.014567   1.813101   \n", "5    4.885603   0.627039  3.538685e+00   0.087761  0.000000   2.497726   \n", "6    4.894400   0.156131  2.300822e-16  12.054598  0.037772   5.919670   \n", "7    2.790486   1.174451  2.204980e+00   0.188702  0.033736   1.513597   \n", "8    3.204623   1.135440  1.358149e-01   0.003852  2.923401   1.280115   \n", "9    2.868648   0.729635  5.716495e-02   0.069866  3.887095   1.014707   \n", "10   2.201643   0.719664  1.147577e-01   1.088904  0.206544   5.097098   \n", "11   8.183766   0.441760  0.000000e+00   0.019365  0.052896   8.208101   \n", "12   3.962337   6.785044  2.011451e+00   5.687592  0.044358   5.587348   \n", "13  12.418256   0.105014  2.170431e+00   0.666261  0.194774   1.113223   \n", "14  12.131892   0.515252  6.664595e-03   0.804467  0.296809   0.671142   \n", "15   0.050004   0.695780  5.799652e-02   0.000000  4.366987   6.256170   \n", "16  12.611399   0.311797  1.159698e-02   0.082335  0.000000   0.000000   \n", "17   7.657412   2.915839  6.841248e-02   3.961092  0.354454   3.434732   \n", "18  13.612177   1.578880  4.546007e-01   3.149715  0.000000   1.157540   \n", "19  12.114113   2.233406  5.445836e-01   3.221920  0.000000   0.998964   \n", "20   4.440344   1.756295  2.722113e-01   2.050443  0.387110   5.122145   \n", "21   9.180978   0.045235  1.626422e+00   0.404698  0.055980  10.117315   \n", "\n", "      maltose  medium                          species  \n", "0    0.000000     YCA            Bacteroides uniformis  \n", "1    0.000000     YCA             Bacteroides fragilis  \n", "2    0.000000     YCA               Bacteroides ovatus  \n", "3    0.000000     YCA     Bacteroides thetaiotaomicron  \n", "4    0.000000     YCA           Bacteroides <PERSON>ii  \n", "5    0.000000     YCA             Phocaeicola vulgatus  \n", "6    0.000000     YCA                 Prevotella copri  \n", "7    0.000000     YCA       Parabacteroides distasonis  \n", "8    0.000000     YCA           Roseburia intestinalis  \n", "9    0.000000     YCA              Eubacterium rectale  \n", "10   0.000000     YCA              Lachnospira eligens  \n", "11   0.000000     YCA                Dorea longicatena  \n", "12   0.000000     YCA  Fusicatenibacter saccharivorans  \n", "13   0.000000     YCA                 Blautia wexlerae  \n", "14   0.000000     YCA        Blautia hydrogenotrophica  \n", "15  -2.670849     YCA     Faecalibacterium pra<PERSON>ii  \n", "16 -13.671067     YCA              Eubacterium siraeum  \n", "17  -1.526372     YCA              Ruminococcus bromii  \n", "18   0.000000     YCA           Bifidobacterium longum  \n", "19   0.000000     YCA     Bifidobacterium adolescentis  \n", "20   0.000000     YCA          Collinsella aerofaciens  \n", "21   0.000000     YCA                 Escherichia coli  \n", "\n", "[22 rows x 27 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["species\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0</th>\n", "      <th>glucose</th>\n", "      <th>acetate</th>\n", "      <th>propionate</th>\n", "      <th>succinate</th>\n", "      <th>lactate</th>\n", "      <th>butyrate</th>\n", "      <th>formate</th>\n", "      <th>maltose</th>\n", "    </tr>\n", "    <tr>\n", "      <th>species</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Bacteroides <PERSON>ii</th>\n", "      <td>165.0</td>\n", "      <td>-4.654721</td>\n", "      <td>3.666919</td>\n", "      <td>0.999781</td>\n", "      <td>2.024829e+00</td>\n", "      <td>0.110719</td>\n", "      <td>0.014567</td>\n", "      <td>1.813101</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Bacteroides fragilis</th>\n", "      <td>167.0</td>\n", "      <td>-4.832237</td>\n", "      <td>3.496001</td>\n", "      <td>0.380547</td>\n", "      <td>2.658095e+00</td>\n", "      <td>0.499204</td>\n", "      <td>0.000000</td>\n", "      <td>2.187005</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Bacteroides ovatus</th>\n", "      <td>175.0</td>\n", "      <td>-4.210851</td>\n", "      <td>8.802434</td>\n", "      <td>0.306027</td>\n", "      <td>1.523860e+00</td>\n", "      <td>0.307526</td>\n", "      <td>0.014638</td>\n", "      <td>10.289419</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Bacteroides thetaiotaomicron</th>\n", "      <td>178.0</td>\n", "      <td>-6.248500</td>\n", "      <td>4.504513</td>\n", "      <td>1.518513</td>\n", "      <td>2.332290e+00</td>\n", "      <td>0.728622</td>\n", "      <td>0.000000</td>\n", "      <td>0.925419</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Bacteroides uniformis</th>\n", "      <td>182.0</td>\n", "      <td>-6.650701</td>\n", "      <td>3.610049</td>\n", "      <td>0.292439</td>\n", "      <td>3.280870e-16</td>\n", "      <td>10.845330</td>\n", "      <td>0.009401</td>\n", "      <td>4.765271</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Bifidobacterium adolescentis</th>\n", "      <td>163.0</td>\n", "      <td>-8.238885</td>\n", "      <td>12.114113</td>\n", "      <td>2.233406</td>\n", "      <td>5.445836e-01</td>\n", "      <td>3.221920</td>\n", "      <td>0.000000</td>\n", "      <td>0.998964</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Bifidobacterium longum</th>\n", "      <td>172.0</td>\n", "      <td>-9.243031</td>\n", "      <td>13.612177</td>\n", "      <td>1.578880</td>\n", "      <td>4.546007e-01</td>\n", "      <td>3.149715</td>\n", "      <td>0.000000</td>\n", "      <td>1.157540</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON> hydrogenotrophica</th>\n", "      <td>171.0</td>\n", "      <td>-3.001147</td>\n", "      <td>12.131892</td>\n", "      <td>0.515252</td>\n", "      <td>6.664595e-03</td>\n", "      <td>0.804467</td>\n", "      <td>0.296809</td>\n", "      <td>0.671142</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <td>186.0</td>\n", "      <td>-7.015797</td>\n", "      <td>12.418256</td>\n", "      <td>0.105014</td>\n", "      <td>2.170431e+00</td>\n", "      <td>0.666261</td>\n", "      <td>0.194774</td>\n", "      <td>1.113223</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Collinsella aerofaciens</th>\n", "      <td>187.0</td>\n", "      <td>-6.349981</td>\n", "      <td>4.440344</td>\n", "      <td>1.756295</td>\n", "      <td>2.722113e-01</td>\n", "      <td>2.050443</td>\n", "      <td>0.387110</td>\n", "      <td>5.122145</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON> longicatena</th>\n", "      <td>190.0</td>\n", "      <td>-5.729152</td>\n", "      <td>8.183766</td>\n", "      <td>0.441760</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.019365</td>\n", "      <td>0.052896</td>\n", "      <td>8.208101</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Escherichia coli</th>\n", "      <td>191.0</td>\n", "      <td>-3.814797</td>\n", "      <td>9.180978</td>\n", "      <td>0.045235</td>\n", "      <td>1.626422e+00</td>\n", "      <td>0.404698</td>\n", "      <td>0.055980</td>\n", "      <td>10.117315</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Eubacterium rectale</th>\n", "      <td>195.0</td>\n", "      <td>-6.572609</td>\n", "      <td>2.868648</td>\n", "      <td>0.729635</td>\n", "      <td>5.716495e-02</td>\n", "      <td>0.069866</td>\n", "      <td>3.887095</td>\n", "      <td>1.014707</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Eubacterium siraeum</th>\n", "      <td>198.0</td>\n", "      <td>0.000000</td>\n", "      <td>12.611399</td>\n", "      <td>0.311797</td>\n", "      <td>1.159698e-02</td>\n", "      <td>0.082335</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>-13.671067</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Faecalibacterium <PERSON>ii</th>\n", "      <td>199.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.050004</td>\n", "      <td>0.695780</td>\n", "      <td>5.799652e-02</td>\n", "      <td>0.000000</td>\n", "      <td>4.366987</td>\n", "      <td>6.256170</td>\n", "      <td>-2.670849</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Fusicatenibacter saccharivorans</th>\n", "      <td>202.0</td>\n", "      <td>-6.417189</td>\n", "      <td>3.962337</td>\n", "      <td>6.785044</td>\n", "      <td>2.011451e+00</td>\n", "      <td>5.687592</td>\n", "      <td>0.044358</td>\n", "      <td>5.587348</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Lachnospira eligens</th>\n", "      <td>203.0</td>\n", "      <td>-12.382019</td>\n", "      <td>2.201643</td>\n", "      <td>0.719664</td>\n", "      <td>1.147577e-01</td>\n", "      <td>1.088904</td>\n", "      <td>0.206544</td>\n", "      <td>5.097098</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Parabacteroides distasonis</th>\n", "      <td>205.0</td>\n", "      <td>-5.080898</td>\n", "      <td>2.790486</td>\n", "      <td>1.174451</td>\n", "      <td>2.204980e+00</td>\n", "      <td>0.188702</td>\n", "      <td>0.033736</td>\n", "      <td>1.513597</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Phocaeicola vulgatus</th>\n", "      <td>183.0</td>\n", "      <td>-5.365080</td>\n", "      <td>4.885603</td>\n", "      <td>0.627039</td>\n", "      <td>3.538685e+00</td>\n", "      <td>0.087761</td>\n", "      <td>0.000000</td>\n", "      <td>2.497726</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Prevotella copri</th>\n", "      <td>204.0</td>\n", "      <td>-8.366688</td>\n", "      <td>4.894400</td>\n", "      <td>0.156131</td>\n", "      <td>2.300822e-16</td>\n", "      <td>12.054598</td>\n", "      <td>0.037772</td>\n", "      <td>5.919670</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON> intestinalis</th>\n", "      <td>209.0</td>\n", "      <td>-5.271650</td>\n", "      <td>3.204623</td>\n", "      <td>1.135440</td>\n", "      <td>1.358149e-01</td>\n", "      <td>0.003852</td>\n", "      <td>2.923401</td>\n", "      <td>1.280115</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Ruminococcus bromii</th>\n", "      <td>207.0</td>\n", "      <td>0.000000</td>\n", "      <td>7.657412</td>\n", "      <td>2.915839</td>\n", "      <td>6.841248e-02</td>\n", "      <td>3.961092</td>\n", "      <td>0.354454</td>\n", "      <td>3.434732</td>\n", "      <td>-1.526372</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                 Unnamed: 0    glucose    acetate  propionate  \\\n", "species                                                                         \n", "Bacteroides <PERSON>                165.0  -4.654721   3.666919    0.999781   \n", "Bacteroides fragilis                  167.0  -4.832237   3.496001    0.380547   \n", "Bacteroides ovatus                    175.0  -4.210851   8.802434    0.306027   \n", "Bacteroides thetaiotaomicron          178.0  -6.248500   4.504513    1.518513   \n", "Bacteroides uniformis                 182.0  -6.650701   3.610049    0.292439   \n", "Bifidobacterium adolescentis          163.0  -8.238885  12.114113    2.233406   \n", "Bifidobacterium longum                172.0  -9.243031  13.612177    1.578880   \n", "<PERSON>lautia hydrogenotrophica             171.0  -3.001147  12.131892    0.515252   \n", "<PERSON><PERSON><PERSON>                      186.0  -7.015797  12.418256    0.105014   \n", "Collinsella aerofaciens               187.0  -6.349981   4.440344    1.756295   \n", "<PERSON><PERSON> longicatena                     190.0  -5.729152   8.183766    0.441760   \n", "Escherichia coli                      191.0  -3.814797   9.180978    0.045235   \n", "Eubacterium rectale                   195.0  -6.572609   2.868648    0.729635   \n", "Eubacterium siraeum                   198.0   0.000000  12.611399    0.311797   \n", "Faecalibacterium <PERSON>ii          199.0   0.000000   0.050004    0.695780   \n", "Fusicatenibacter saccharivorans       202.0  -6.417189   3.962337    6.785044   \n", "Lachnospira eligens                   203.0 -12.382019   2.201643    0.719664   \n", "Parabacteroides distasonis            205.0  -5.080898   2.790486    1.174451   \n", "Phocaeicola vulgatus                  183.0  -5.365080   4.885603    0.627039   \n", "Prevotella copri                      204.0  -8.366688   4.894400    0.156131   \n", "Roseburia intestinalis                209.0  -5.271650   3.204623    1.135440   \n", "Ruminococcus bromii                   207.0   0.000000   7.657412    2.915839   \n", "\n", "                                    succinate    lactate  butyrate    formate  \\\n", "species                                                                         \n", "Bacteroides <PERSON>ii           2.024829e+00   0.110719  0.014567   1.813101   \n", "Bacteroides fragilis             2.658095e+00   0.499204  0.000000   2.187005   \n", "Bacteroides ovatus               1.523860e+00   0.307526  0.014638  10.289419   \n", "Bacteroides thetaiotaomicron     2.332290e+00   0.728622  0.000000   0.925419   \n", "Bacteroides uniformis            3.280870e-16  10.845330  0.009401   4.765271   \n", "Bifidobacterium adolescentis     5.445836e-01   3.221920  0.000000   0.998964   \n", "Bifidobacterium longum           4.546007e-01   3.149715  0.000000   1.157540   \n", "Blautia hydrogenotrophica        6.664595e-03   0.804467  0.296809   0.671142   \n", "<PERSON><PERSON><PERSON>                 2.170431e+00   0.666261  0.194774   1.113223   \n", "Collinsella aerofaciens          2.722113e-01   2.050443  0.387110   5.122145   \n", "<PERSON><PERSON> longicatena                0.000000e+00   0.019365  0.052896   8.208101   \n", "Escherichia coli                 1.626422e+00   0.404698  0.055980  10.117315   \n", "Eubacterium rectale              5.716495e-02   0.069866  3.887095   1.014707   \n", "Eubacterium siraeum              1.159698e-02   0.082335  0.000000   0.000000   \n", "Faecalibacterium p<PERSON>ii     5.799652e-02   0.000000  4.366987   6.256170   \n", "Fusicatenibacter saccharivorans  2.011451e+00   5.687592  0.044358   5.587348   \n", "Lachnospira eligens              1.147577e-01   1.088904  0.206544   5.097098   \n", "Parabacteroides distasonis       2.204980e+00   0.188702  0.033736   1.513597   \n", "Phocaeicola vulgatus             3.538685e+00   0.087761  0.000000   2.497726   \n", "Prevotella copri                 2.300822e-16  12.054598  0.037772   5.919670   \n", "Roseburia intestinalis           1.358149e-01   0.003852  2.923401   1.280115   \n", "Ruminococcus bromii              6.841248e-02   3.961092  0.354454   3.434732   \n", "\n", "                                   maltose  \n", "species                                     \n", "Bacteroides <PERSON>            0.000000  \n", "Bacteroides fragilis              0.000000  \n", "Bacteroides ovatus                0.000000  \n", "Bacteroides thetaiotaomicron      0.000000  \n", "Bacteroides uniformis             0.000000  \n", "Bifidobacterium adolescentis      0.000000  \n", "Bifidobacterium longum            0.000000  \n", "Blautia hydrogenotrophica         0.000000  \n", "<PERSON><PERSON><PERSON> we<PERSON>ae                  0.000000  \n", "Collinsella aerofaciens           0.000000  \n", "Dorea longicatena                 0.000000  \n", "Escherichia coli                  0.000000  \n", "Eubacterium rectale               0.000000  \n", "Eubacterium siraeum             -13.671067  \n", "Faecalibacterium <PERSON>ii     -2.670849  \n", "Fusicatenibacter saccharivorans   0.000000  \n", "Lachnospira eligens               0.000000  \n", "Parabacteroides distasonis        0.000000  \n", "Phocaeicola vulgatus              0.000000  \n", "Prevotella copri                  0.000000  \n", "Roseburia intestinalis            0.000000  \n", "Ruminococcus bromii              -1.526372  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["(22, 9)\n", "genus\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0</th>\n", "      <th>glucose</th>\n", "      <th>acetate</th>\n", "      <th>propionate</th>\n", "      <th>succinate</th>\n", "      <th>lactate</th>\n", "      <th>butyrate</th>\n", "      <th>formate</th>\n", "      <th>maltose</th>\n", "    </tr>\n", "    <tr>\n", "      <th>genus</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Bacteroides</th>\n", "      <td>173.4</td>\n", "      <td>-5.319402</td>\n", "      <td>4.815983</td>\n", "      <td>0.699462</td>\n", "      <td>1.707815e+00</td>\n", "      <td>2.498280</td>\n", "      <td>0.007721</td>\n", "      <td>3.996043</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Bifidobacterium</th>\n", "      <td>167.5</td>\n", "      <td>-8.740958</td>\n", "      <td>12.863145</td>\n", "      <td>1.906143</td>\n", "      <td>4.995921e-01</td>\n", "      <td>3.185817</td>\n", "      <td>0.000000</td>\n", "      <td>1.078252</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <td>178.5</td>\n", "      <td>-5.008472</td>\n", "      <td>12.275074</td>\n", "      <td>0.310133</td>\n", "      <td>1.088548e+00</td>\n", "      <td>0.735364</td>\n", "      <td>0.245791</td>\n", "      <td>0.892182</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON></th>\n", "      <td>187.0</td>\n", "      <td>-6.349981</td>\n", "      <td>4.440344</td>\n", "      <td>1.756295</td>\n", "      <td>2.722113e-01</td>\n", "      <td>2.050443</td>\n", "      <td>0.387110</td>\n", "      <td>5.122145</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON></th>\n", "      <td>190.0</td>\n", "      <td>-5.729152</td>\n", "      <td>8.183766</td>\n", "      <td>0.441760</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.019365</td>\n", "      <td>0.052896</td>\n", "      <td>8.208101</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Escherichia</th>\n", "      <td>191.0</td>\n", "      <td>-3.814797</td>\n", "      <td>9.180978</td>\n", "      <td>0.045235</td>\n", "      <td>1.626422e+00</td>\n", "      <td>0.404698</td>\n", "      <td>0.055980</td>\n", "      <td>10.117315</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Faecalibacterium</th>\n", "      <td>199.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.050004</td>\n", "      <td>0.695780</td>\n", "      <td>5.799652e-02</td>\n", "      <td>0.000000</td>\n", "      <td>4.366987</td>\n", "      <td>6.256170</td>\n", "      <td>-2.670849</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Fusicatenibacter</th>\n", "      <td>202.0</td>\n", "      <td>-6.417189</td>\n", "      <td>3.962337</td>\n", "      <td>6.785044</td>\n", "      <td>2.011451e+00</td>\n", "      <td>5.687592</td>\n", "      <td>0.044358</td>\n", "      <td>5.587348</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Lachnospira</th>\n", "      <td>203.0</td>\n", "      <td>-12.382019</td>\n", "      <td>2.201643</td>\n", "      <td>0.719664</td>\n", "      <td>1.147577e-01</td>\n", "      <td>1.088904</td>\n", "      <td>0.206544</td>\n", "      <td>5.097098</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Lachnospiraceae_NA</th>\n", "      <td>195.0</td>\n", "      <td>-6.572609</td>\n", "      <td>2.868648</td>\n", "      <td>0.729635</td>\n", "      <td>5.716495e-02</td>\n", "      <td>0.069866</td>\n", "      <td>3.887095</td>\n", "      <td>1.014707</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Oscillospiraceae_NA</th>\n", "      <td>198.0</td>\n", "      <td>0.000000</td>\n", "      <td>12.611399</td>\n", "      <td>0.311797</td>\n", "      <td>1.159698e-02</td>\n", "      <td>0.082335</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>-13.671067</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Parabacteroides</th>\n", "      <td>205.0</td>\n", "      <td>-5.080898</td>\n", "      <td>2.790486</td>\n", "      <td>1.174451</td>\n", "      <td>2.204980e+00</td>\n", "      <td>0.188702</td>\n", "      <td>0.033736</td>\n", "      <td>1.513597</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Phocaeicola</th>\n", "      <td>183.0</td>\n", "      <td>-5.365080</td>\n", "      <td>4.885603</td>\n", "      <td>0.627039</td>\n", "      <td>3.538685e+00</td>\n", "      <td>0.087761</td>\n", "      <td>0.000000</td>\n", "      <td>2.497726</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <td>204.0</td>\n", "      <td>-8.366688</td>\n", "      <td>4.894400</td>\n", "      <td>0.156131</td>\n", "      <td>2.300822e-16</td>\n", "      <td>12.054598</td>\n", "      <td>0.037772</td>\n", "      <td>5.919670</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Roseburia</th>\n", "      <td>209.0</td>\n", "      <td>-5.271650</td>\n", "      <td>3.204623</td>\n", "      <td>1.135440</td>\n", "      <td>1.358149e-01</td>\n", "      <td>0.003852</td>\n", "      <td>2.923401</td>\n", "      <td>1.280115</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Ruminococcus</th>\n", "      <td>207.0</td>\n", "      <td>0.000000</td>\n", "      <td>7.657412</td>\n", "      <td>2.915839</td>\n", "      <td>6.841248e-02</td>\n", "      <td>3.961092</td>\n", "      <td>0.354454</td>\n", "      <td>3.434732</td>\n", "      <td>-1.526372</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     Unnamed: 0    glucose    acetate  propionate  \\\n", "genus                                                               \n", "Bacteroides               173.4  -5.319402   4.815983    0.699462   \n", "Bifidobacterium           167.5  -8.740958  12.863145    1.906143   \n", "<PERSON><PERSON><PERSON>                   178.5  -5.008472  12.275074    0.310133   \n", "Collinsella               187.0  -6.349981   4.440344    1.756295   \n", "Dorea                     190.0  -5.729152   8.183766    0.441760   \n", "Escherichia               191.0  -3.814797   9.180978    0.045235   \n", "Faecalibacterium          199.0   0.000000   0.050004    0.695780   \n", "Fusicatenibacter          202.0  -6.417189   3.962337    6.785044   \n", "Lachnospira               203.0 -12.382019   2.201643    0.719664   \n", "Lachnospiraceae_NA        195.0  -6.572609   2.868648    0.729635   \n", "Oscillospiraceae_NA       198.0   0.000000  12.611399    0.311797   \n", "Parabacteroides           205.0  -5.080898   2.790486    1.174451   \n", "Phocaeicola               183.0  -5.365080   4.885603    0.627039   \n", "Prevotella                204.0  -8.366688   4.894400    0.156131   \n", "Roseburia                 209.0  -5.271650   3.204623    1.135440   \n", "Ruminococcus              207.0   0.000000   7.657412    2.915839   \n", "\n", "                        succinate    lactate  butyrate    formate    maltose  \n", "genus                                                                         \n", "Bacteroides          1.707815e+00   2.498280  0.007721   3.996043   0.000000  \n", "Bifidobacterium      4.995921e-01   3.185817  0.000000   1.078252   0.000000  \n", "Blautia              1.088548e+00   0.735364  0.245791   0.892182   0.000000  \n", "Collinsella          2.722113e-01   2.050443  0.387110   5.122145   0.000000  \n", "Dorea                0.000000e+00   0.019365  0.052896   8.208101   0.000000  \n", "Escherichia          1.626422e+00   0.404698  0.055980  10.117315   0.000000  \n", "Faecalibacterium     5.799652e-02   0.000000  4.366987   6.256170  -2.670849  \n", "Fusicatenibacter     2.011451e+00   5.687592  0.044358   5.587348   0.000000  \n", "Lachnospira          1.147577e-01   1.088904  0.206544   5.097098   0.000000  \n", "Lachnospiraceae_NA   5.716495e-02   0.069866  3.887095   1.014707   0.000000  \n", "Oscillospiraceae_NA  1.159698e-02   0.082335  0.000000   0.000000 -13.671067  \n", "Parabacteroides      2.204980e+00   0.188702  0.033736   1.513597   0.000000  \n", "Phocaeicola          3.538685e+00   0.087761  0.000000   2.497726   0.000000  \n", "Prevotella           2.300822e-16  12.054598  0.037772   5.919670   0.000000  \n", "Roseburia            1.358149e-01   0.003852  2.923401   1.280115   0.000000  \n", "Ruminococcus         6.841248e-02   3.961092  0.354454   3.434732  -1.526372  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["(16, 9)\n", "family\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0</th>\n", "      <th>glucose</th>\n", "      <th>acetate</th>\n", "      <th>propionate</th>\n", "      <th>succinate</th>\n", "      <th>lactate</th>\n", "      <th>butyrate</th>\n", "      <th>formate</th>\n", "      <th>maltose</th>\n", "    </tr>\n", "    <tr>\n", "      <th>family</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Bacteroidaceae</th>\n", "      <td>175.000000</td>\n", "      <td>-5.327015</td>\n", "      <td>4.827586</td>\n", "      <td>0.687391</td>\n", "      <td>2.012960e+00</td>\n", "      <td>2.096527</td>\n", "      <td>0.006434</td>\n", "      <td>3.746324</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Bifidobacteriaceae</th>\n", "      <td>167.500000</td>\n", "      <td>-8.740958</td>\n", "      <td>12.863145</td>\n", "      <td>1.906143</td>\n", "      <td>4.995921e-01</td>\n", "      <td>3.185817</td>\n", "      <td>0.000000</td>\n", "      <td>1.078252</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Coriobacteriaceae</th>\n", "      <td>187.000000</td>\n", "      <td>-6.349981</td>\n", "      <td>4.440344</td>\n", "      <td>1.756295</td>\n", "      <td>2.722113e-01</td>\n", "      <td>2.050443</td>\n", "      <td>0.387110</td>\n", "      <td>5.122145</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Enterobacteriaceae</th>\n", "      <td>191.000000</td>\n", "      <td>-3.814797</td>\n", "      <td>9.180978</td>\n", "      <td>0.045235</td>\n", "      <td>1.626422e+00</td>\n", "      <td>0.404698</td>\n", "      <td>0.055980</td>\n", "      <td>10.117315</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Lachnospiraceae</th>\n", "      <td>193.714286</td>\n", "      <td>-6.627081</td>\n", "      <td>6.424452</td>\n", "      <td>1.490258</td>\n", "      <td>6.423264e-01</td>\n", "      <td>1.191472</td>\n", "      <td>1.086554</td>\n", "      <td>3.281676</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Oscillospiraceae</th>\n", "      <td>201.333333</td>\n", "      <td>0.000000</td>\n", "      <td>6.772938</td>\n", "      <td>1.307806</td>\n", "      <td>4.600199e-02</td>\n", "      <td>1.347809</td>\n", "      <td>1.573814</td>\n", "      <td>3.230301</td>\n", "      <td>-5.956096</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Prevotellaceae</th>\n", "      <td>204.000000</td>\n", "      <td>-8.366688</td>\n", "      <td>4.894400</td>\n", "      <td>0.156131</td>\n", "      <td>2.300822e-16</td>\n", "      <td>12.054598</td>\n", "      <td>0.037772</td>\n", "      <td>5.919670</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Tannerellaceae</th>\n", "      <td>205.000000</td>\n", "      <td>-5.080898</td>\n", "      <td>2.790486</td>\n", "      <td>1.174451</td>\n", "      <td>2.204980e+00</td>\n", "      <td>0.188702</td>\n", "      <td>0.033736</td>\n", "      <td>1.513597</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    Unnamed: 0   glucose    acetate  propionate     succinate  \\\n", "family                                                                          \n", "Bacteroidaceae      175.000000 -5.327015   4.827586    0.687391  2.012960e+00   \n", "Bifidobacteriaceae  167.500000 -8.740958  12.863145    1.906143  4.995921e-01   \n", "Coriobacteriaceae   187.000000 -6.349981   4.440344    1.756295  2.722113e-01   \n", "Enterobacteriaceae  191.000000 -3.814797   9.180978    0.045235  1.626422e+00   \n", "Lachnospiraceae     193.714286 -6.627081   6.424452    1.490258  6.423264e-01   \n", "Oscillospiraceae    201.333333  0.000000   6.772938    1.307806  4.600199e-02   \n", "Prevotellaceae      204.000000 -8.366688   4.894400    0.156131  2.300822e-16   \n", "Tannerellaceae      205.000000 -5.080898   2.790486    1.174451  2.204980e+00   \n", "\n", "                      lactate  butyrate    formate   maltose  \n", "family                                                        \n", "Bacteroidaceae       2.096527  0.006434   3.746324  0.000000  \n", "Bifidobacteriaceae   3.185817  0.000000   1.078252  0.000000  \n", "Coriobacteriaceae    2.050443  0.387110   5.122145  0.000000  \n", "Enterobacteriaceae   0.404698  0.055980  10.117315  0.000000  \n", "Lachnospiraceae      1.191472  1.086554   3.281676  0.000000  \n", "Oscillospiraceae     1.347809  1.573814   3.230301 -5.956096  \n", "Prevotellaceae      12.054598  0.037772   5.919670  0.000000  \n", "Tannerellaceae       0.188702  0.033736   1.513597  0.000000  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["(8, 9)\n", "order\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0</th>\n", "      <th>glucose</th>\n", "      <th>acetate</th>\n", "      <th>propionate</th>\n", "      <th>succinate</th>\n", "      <th>lactate</th>\n", "      <th>butyrate</th>\n", "      <th>formate</th>\n", "      <th>maltose</th>\n", "    </tr>\n", "    <tr>\n", "      <th>order</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Bacteroidales</th>\n", "      <td>182.375</td>\n", "      <td>-5.676209</td>\n", "      <td>4.581301</td>\n", "      <td>0.681866</td>\n", "      <td>1.785342</td>\n", "      <td>3.102808</td>\n", "      <td>0.013764</td>\n", "      <td>3.738901</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Bifidobacteriales</th>\n", "      <td>167.500</td>\n", "      <td>-8.740958</td>\n", "      <td>12.863145</td>\n", "      <td>1.906143</td>\n", "      <td>0.499592</td>\n", "      <td>3.185817</td>\n", "      <td>0.000000</td>\n", "      <td>1.078252</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Coriobacteriales</th>\n", "      <td>187.000</td>\n", "      <td>-6.349981</td>\n", "      <td>4.440344</td>\n", "      <td>1.756295</td>\n", "      <td>0.272211</td>\n", "      <td>2.050443</td>\n", "      <td>0.387110</td>\n", "      <td>5.122145</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Enterobacterales</th>\n", "      <td>191.000</td>\n", "      <td>-3.814797</td>\n", "      <td>9.180978</td>\n", "      <td>0.045235</td>\n", "      <td>1.626422</td>\n", "      <td>0.404698</td>\n", "      <td>0.055980</td>\n", "      <td>10.117315</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Eubacteriales</th>\n", "      <td>196.000</td>\n", "      <td>-4.638956</td>\n", "      <td>6.528998</td>\n", "      <td>1.435523</td>\n", "      <td>0.463429</td>\n", "      <td>1.238373</td>\n", "      <td>1.232732</td>\n", "      <td>3.266264</td>\n", "      <td>-1.786829</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   Unnamed: 0   glucose    acetate  propionate  succinate  \\\n", "order                                                                       \n", "Bacteroidales         182.375 -5.676209   4.581301    0.681866   1.785342   \n", "Bifidobacteriales     167.500 -8.740958  12.863145    1.906143   0.499592   \n", "Coriobacteriales      187.000 -6.349981   4.440344    1.756295   0.272211   \n", "Enterobacterales      191.000 -3.814797   9.180978    0.045235   1.626422   \n", "Eubacteriales         196.000 -4.638956   6.528998    1.435523   0.463429   \n", "\n", "                    lactate  butyrate    formate   maltose  \n", "order                                                       \n", "Bacteroidales      3.102808  0.013764   3.738901  0.000000  \n", "Bifidobacteriales  3.185817  0.000000   1.078252  0.000000  \n", "Coriobacteriales   2.050443  0.387110   5.122145  0.000000  \n", "Enterobacterales   0.404698  0.055980  10.117315  0.000000  \n", "Eubacteriales      1.238373  1.232732   3.266264 -1.786829  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["(5, 9)\n", "class\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0</th>\n", "      <th>glucose</th>\n", "      <th>acetate</th>\n", "      <th>propionate</th>\n", "      <th>succinate</th>\n", "      <th>lactate</th>\n", "      <th>butyrate</th>\n", "      <th>formate</th>\n", "      <th>maltose</th>\n", "    </tr>\n", "    <tr>\n", "      <th>class</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Actinomycetia</th>\n", "      <td>167.500</td>\n", "      <td>-8.740958</td>\n", "      <td>12.863145</td>\n", "      <td>1.906143</td>\n", "      <td>0.499592</td>\n", "      <td>3.185817</td>\n", "      <td>0.000000</td>\n", "      <td>1.078252</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Bacteroidia</th>\n", "      <td>182.375</td>\n", "      <td>-5.676209</td>\n", "      <td>4.581301</td>\n", "      <td>0.681866</td>\n", "      <td>1.785342</td>\n", "      <td>3.102808</td>\n", "      <td>0.013764</td>\n", "      <td>3.738901</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <td>196.000</td>\n", "      <td>-4.638956</td>\n", "      <td>6.528998</td>\n", "      <td>1.435523</td>\n", "      <td>0.463429</td>\n", "      <td>1.238373</td>\n", "      <td>1.232732</td>\n", "      <td>3.266264</td>\n", "      <td>-1.786829</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Coriobacteriia</th>\n", "      <td>187.000</td>\n", "      <td>-6.349981</td>\n", "      <td>4.440344</td>\n", "      <td>1.756295</td>\n", "      <td>0.272211</td>\n", "      <td>2.050443</td>\n", "      <td>0.387110</td>\n", "      <td>5.122145</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Gammaproteobacteria</th>\n", "      <td>191.000</td>\n", "      <td>-3.814797</td>\n", "      <td>9.180978</td>\n", "      <td>0.045235</td>\n", "      <td>1.626422</td>\n", "      <td>0.404698</td>\n", "      <td>0.055980</td>\n", "      <td>10.117315</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     Unnamed: 0   glucose    acetate  propionate  succinate  \\\n", "class                                                                         \n", "Actinomycetia           167.500 -8.740958  12.863145    1.906143   0.499592   \n", "Bacteroidia             182.375 -5.676209   4.581301    0.681866   1.785342   \n", "Clostridia              196.000 -4.638956   6.528998    1.435523   0.463429   \n", "Coriobacteriia          187.000 -6.349981   4.440344    1.756295   0.272211   \n", "Gammaproteobacteria     191.000 -3.814797   9.180978    0.045235   1.626422   \n", "\n", "                      lactate  butyrate    formate   maltose  \n", "class                                                         \n", "Actinomycetia        3.185817  0.000000   1.078252  0.000000  \n", "Bacteroidia          3.102808  0.013764   3.738901  0.000000  \n", "Clostridia           1.238373  1.232732   3.266264 -1.786829  \n", "Coriobacteriia       2.050443  0.387110   5.122145  0.000000  \n", "Gammaproteobacteria  0.404698  0.055980  10.117315  0.000000  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["(5, 9)\n", "phylum\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0</th>\n", "      <th>glucose</th>\n", "      <th>acetate</th>\n", "      <th>propionate</th>\n", "      <th>succinate</th>\n", "      <th>lactate</th>\n", "      <th>butyrate</th>\n", "      <th>formate</th>\n", "      <th>maltose</th>\n", "    </tr>\n", "    <tr>\n", "      <th>phylum</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Actinobacteria</th>\n", "      <td>174.000</td>\n", "      <td>-7.943966</td>\n", "      <td>10.055545</td>\n", "      <td>1.856194</td>\n", "      <td>0.423799</td>\n", "      <td>2.807359</td>\n", "      <td>0.129037</td>\n", "      <td>2.426216</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Bacteroidetes</th>\n", "      <td>182.375</td>\n", "      <td>-5.676209</td>\n", "      <td>4.581301</td>\n", "      <td>0.681866</td>\n", "      <td>1.785342</td>\n", "      <td>3.102808</td>\n", "      <td>0.013764</td>\n", "      <td>3.738901</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Firmicutes</th>\n", "      <td>196.000</td>\n", "      <td>-4.638956</td>\n", "      <td>6.528998</td>\n", "      <td>1.435523</td>\n", "      <td>0.463429</td>\n", "      <td>1.238373</td>\n", "      <td>1.232732</td>\n", "      <td>3.266264</td>\n", "      <td>-1.786829</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Proteobacteria</th>\n", "      <td>191.000</td>\n", "      <td>-3.814797</td>\n", "      <td>9.180978</td>\n", "      <td>0.045235</td>\n", "      <td>1.626422</td>\n", "      <td>0.404698</td>\n", "      <td>0.055980</td>\n", "      <td>10.117315</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                Unnamed: 0   glucose    acetate  propionate  succinate  \\\n", "phylum                                                                   \n", "Actinobacteria     174.000 -7.943966  10.055545    1.856194   0.423799   \n", "Bacteroidetes      182.375 -5.676209   4.581301    0.681866   1.785342   \n", "Firmicutes         196.000 -4.638956   6.528998    1.435523   0.463429   \n", "Proteobacteria     191.000 -3.814797   9.180978    0.045235   1.626422   \n", "\n", "                 lactate  butyrate    formate   maltose  \n", "phylum                                                   \n", "Actinobacteria  2.807359  0.129037   2.426216  0.000000  \n", "Bacteroidetes   3.102808  0.013764   3.738901  0.000000  \n", "Firmicutes      1.238373  1.232732   3.266264 -1.786829  \n", "Proteobacteria  0.404698  0.055980  10.117315  0.000000  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["(4, 9)\n"]}], "source": ["#read information on experimentally characterized species\n", "speciesinformation=pd.read_csv(\"data_hplc/species_properties.csv\",skiprows=1)\n", "characterized_species=speciesinformation[\"species\"].tolist()\n", "\n", "#print(df = df.rename(columns={'oldName1': 'newName1', 'oldName2': 'newName2'})\n", "print(speciesinformation[\"species\"])\n", "\n", "#yieldresults (yields and excretion, here still in unites of mmol/OD l)\n", "yieldresults=pd.read_csv(\"data_hplc/hplc_majorresults.csv\")\n", "#select data for which medium to use\n", "yieldresults=yieldresults.loc[yieldresults[\"medium\"]==\"YCA\"]\n", "\n", "#merge yield information to species information\n", "speciesinformation=speciesinformation.merge(yieldresults,left_on=\"species_HPLCname\",right_on=\"species\")\n", "speciesinformation[\"species\"] = speciesinformation['species_x']\n", "\n", "\n", "display(speciesinformation.columns)\n", "display(speciesinformation)\n", "\n", "speciesinformation_taxonomiclevel=[]\n", "taxlevels=[\"species\",\"genus\",\"family\",\"order\",\"class\",\"phylum\"]\n", "taxlevels_names_characterized=[]\n", "for taxlevel in taxlevels:\n", "    speciesinformation_taxonomiclevel.append(speciesinformation.groupby(taxlevel).mean(numeric_only=True))\n", "    print(taxlevel)\n", "    display(speciesinformation_taxonomiclevel[-1])\n", "    print(speciesinformation_taxonomiclevel[-1].shape)\n", "    taxlevels_names_characterized.append(speciesinformation_taxonomiclevel[-1].index)\n", "\n", "#plotting settings\n", "sublist=['glucose','maltose','acetate','butyrate','formate','lactate','propionate','succinate']\n", "sublist_secretion=sublist[2:]\n", "#sublist_color=['b','b','#1b9e77','#66a61e','#a6761d','#e7298a','#d95f02','#7570b3']\n", "sublist_color = met_brewer.met_brew(name=\"Egypt\", n=8, brew_type=\"continuous\")\n", "sublist_color=['#dd5129', '#85635d', '#2c7591', '#34a28d', '#fab255','#5db27d', '#1e8b99','#acb269']\n", "sublist_color_secretion=sublist_color[2:]\n", "sublist_energy=[0.68,1.36,0.21,0.52,0.,.33,0.37,0.36]\n", "sublist_energy_secretion=sublist_energy[2:]\n", "\n", "#conversion factor \n", "conversionfactorOD=0.5 #to go from mM/OD to mmol/g (1 OD l = 0.5 g)\n", "\n", "\n"]}, {"cell_type": "markdown", "id": "744d0da9-1238-4d82-883c-8aef<PERSON>aa1d5", "metadata": {}, "source": ["# Load microbiome abundance data\n", "\n", "The original data from the currated metagenomics dataset is provided in R. Here, we use only the abundance data, provided in easily to handle csv files (subfolder data_curated_microbiome). \n", "\n", "# Double check: license of that dataset (can we just copy it here)\n"]}, {"cell_type": "code", "execution_count": 4, "id": "4fe7ee15-54a3-4f03-9774-6c2fcd8f32e6", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tax_identifier</th>\n", "      <th>MV_FEI1_t1Q14</th>\n", "      <th>MV_FEI2_t1Q14</th>\n", "      <th>MV_FEI3_t1Q14</th>\n", "      <th>MV_FEI4_t1Q14</th>\n", "      <th>MV_FEI4_t2Q15</th>\n", "      <th>MV_FEI5_t1Q14</th>\n", "      <th>MV_FEI5_t2Q14</th>\n", "      <th>MV_FEI5_t3Q15</th>\n", "      <th>MV_FEM1_t1Q14</th>\n", "      <th>...</th>\n", "      <th>wHAXPI034926-15</th>\n", "      <th>wHAXPI037144-8</th>\n", "      <th>wHAXPI037145-9</th>\n", "      <th>wHAXPI037146-11</th>\n", "      <th>wHAXPI037147-12</th>\n", "      <th>wHAXPI043592-8</th>\n", "      <th>wHAXPI043593-9</th>\n", "      <th>wHAXPI043594-11</th>\n", "      <th>wHAXPI047830-11</th>\n", "      <th>wHAXPI048670-90</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Escherichia coli</td>\n", "      <td>59.35010</td>\n", "      <td>0.00000</td>\n", "      <td>85.42333</td>\n", "      <td>46.70438</td>\n", "      <td>0.54770</td>\n", "      <td>0.0</td>\n", "      <td>0.02243</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>...</td>\n", "      <td>0.02811</td>\n", "      <td>0.41802</td>\n", "      <td>3.13657</td>\n", "      <td>1.26867</td>\n", "      <td>2.79116</td>\n", "      <td>0.07465</td>\n", "      <td>0.10713</td>\n", "      <td>0.40001</td>\n", "      <td>0.45324</td>\n", "      <td>6.05846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bifidobacterium bifidum</td>\n", "      <td>16.16243</td>\n", "      <td>5.61882</td>\n", "      <td>0.20192</td>\n", "      <td>0.00000</td>\n", "      <td>26.63938</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "      <td>19.20307</td>\n", "      <td>0.13643</td>\n", "      <td>...</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>2.37212</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Bifidobacterium longum</td>\n", "      <td>7.79189</td>\n", "      <td>60.54102</td>\n", "      <td>0.49524</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "      <td>28.51001</td>\n", "      <td>0.05094</td>\n", "      <td>...</td>\n", "      <td>0.33410</td>\n", "      <td>0.39197</td>\n", "      <td>0.09752</td>\n", "      <td>3.19180</td>\n", "      <td>0.18976</td>\n", "      <td>0.00330</td>\n", "      <td>0.55654</td>\n", "      <td>0.69706</td>\n", "      <td>2.61017</td>\n", "      <td>0.03886</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3 rows × 18726 columns</p>\n", "</div>"], "text/plain": ["            tax_identifier  MV_FEI1_t1Q14  MV_FEI2_t1Q14  MV_FEI3_t1Q14  \\\n", "0         Escherichia coli       59.35010        0.00000       85.42333   \n", "1  Bifidobacterium bifidum       16.16243        5.61882        0.20192   \n", "2   Bifidobacterium longum        7.79189       60.54102        0.49524   \n", "\n", "   MV_FEI4_t1Q14  MV_FEI4_t2Q15  MV_FEI5_t1Q14  MV_FEI5_t2Q14  MV_FEI5_t3Q15  \\\n", "0       46.70438        0.54770            0.0        0.02243        0.00000   \n", "1        0.00000       26.63938            0.0        0.00000       19.20307   \n", "2        0.00000        0.00000            0.0        0.00000       28.51001   \n", "\n", "   MV_FEM1_t1Q14  ...  wHAXPI034926-15  wHAXPI037144-8  wHAXPI037145-9  \\\n", "0        0.00000  ...          0.02811         0.41802         3.13657   \n", "1        0.13643  ...          0.00000         0.00000         0.00000   \n", "2        0.05094  ...          0.33410         0.39197         0.09752   \n", "\n", "   wHAXPI037146-11  wHAXPI037147-12  wHAXPI043592-8  wHAXPI043593-9  \\\n", "0          1.26867          2.79116         0.07465         0.10713   \n", "1          2.37212          0.00000         0.00000         0.00000   \n", "2          3.19180          0.18976         0.00330         0.55654   \n", "\n", "   wHAXPI043594-11  wHAXPI047830-11  wHAXPI048670-90  \n", "0          0.40001          0.45324          6.05846  \n", "1          0.00000          0.00000          0.00000  \n", "2          0.69706          2.61017          0.03886  \n", "\n", "[3 rows x 18726 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["(1627, 18726)"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/4b/8dffvbfs5qs93rh77f2zxs340000gn/T/ipykernel_37295/2564990880.py:8: DtypeWarning: Columns (21,22,27,29,30,33,36,43,50,52,53,54,55,56,57,62,63,70,72,76,77,87,90,98,99,102,106,107,108,109,111,114,115,116,123) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  colnames=pd.read_csv(\"data_curated_microbiome/relabundance_colData.csv\")\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sample</th>\n", "      <th>study_name</th>\n", "      <th>subject_id</th>\n", "      <th>body_site</th>\n", "      <th>antibiotics_current_use</th>\n", "      <th>study_condition</th>\n", "      <th>disease</th>\n", "      <th>age</th>\n", "      <th>infant_age</th>\n", "      <th>age_category</th>\n", "      <th>...</th>\n", "      <th>hla_drb11</th>\n", "      <th>birth_order</th>\n", "      <th>age_twins_started_to_live_apart</th>\n", "      <th>zigosity</th>\n", "      <th>brinkman_index</th>\n", "      <th>alcohol_numeric</th>\n", "      <th>breastfeeding_duration</th>\n", "      <th>formula_first_day</th>\n", "      <th>ALT</th>\n", "      <th>eGFR</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MV_FEI1_t1Q14</td>\n", "      <td>AsnicarF_2017</td>\n", "      <td>MV_FEI1</td>\n", "      <td>stool</td>\n", "      <td>NaN</td>\n", "      <td>control</td>\n", "      <td>healthy</td>\n", "      <td>0.0</td>\n", "      <td>90.0</td>\n", "      <td>newborn</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>MV_FEI2_t1Q14</td>\n", "      <td>AsnicarF_2017</td>\n", "      <td>MV_FEI2</td>\n", "      <td>stool</td>\n", "      <td>NaN</td>\n", "      <td>control</td>\n", "      <td>healthy</td>\n", "      <td>0.0</td>\n", "      <td>90.0</td>\n", "      <td>newborn</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>MV_FEI3_t1Q14</td>\n", "      <td>AsnicarF_2017</td>\n", "      <td>MV_FEI3</td>\n", "      <td>stool</td>\n", "      <td>NaN</td>\n", "      <td>control</td>\n", "      <td>healthy</td>\n", "      <td>0.0</td>\n", "      <td>90.0</td>\n", "      <td>newborn</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3 rows × 130 columns</p>\n", "</div>"], "text/plain": ["          sample     study_name subject_id body_site antibiotics_current_use  \\\n", "0  MV_FEI1_t1Q14  AsnicarF_2017    MV_FEI1     stool                     NaN   \n", "1  MV_FEI2_t1Q14  AsnicarF_2017    MV_FEI2     stool                     NaN   \n", "2  MV_FEI3_t1Q14  AsnicarF_2017    MV_FEI3     stool                     NaN   \n", "\n", "  study_condition  disease  age  infant_age age_category  ... hla_drb11  \\\n", "0         control  healthy  0.0        90.0      newborn  ...       NaN   \n", "1         control  healthy  0.0        90.0      newborn  ...       NaN   \n", "2         control  healthy  0.0        90.0      newborn  ...       NaN   \n", "\n", "  birth_order age_twins_started_to_live_apart zigosity brinkman_index  \\\n", "0         NaN                             NaN      NaN            NaN   \n", "1         NaN                             NaN      NaN            NaN   \n", "2         NaN                             NaN      NaN            NaN   \n", "\n", "   alcohol_numeric  breastfeeding_duration  formula_first_day  ALT  eGFR  \n", "0              NaN                     NaN                NaN  NaN   NaN  \n", "1              NaN                     NaN                NaN  NaN   NaN  \n", "2              NaN                     NaN                NaN  NaN   NaN  \n", "\n", "[3 rows x 130 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["(18725, 130)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tax_identifier</th>\n", "      <th>superkingdom</th>\n", "      <th>phylum</th>\n", "      <th>class</th>\n", "      <th>order</th>\n", "      <th>family</th>\n", "      <th>genus</th>\n", "      <th>species</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Escherichia coli</td>\n", "      <td>Bacteria</td>\n", "      <td>Proteobacteria</td>\n", "      <td>Gammaproteobacteria</td>\n", "      <td>Enterobacterales</td>\n", "      <td>Enterobacteriaceae</td>\n", "      <td>Escherichia</td>\n", "      <td>Escherichia coli</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bifidobacterium bifidum</td>\n", "      <td>Bacteria</td>\n", "      <td>Actinobacteria</td>\n", "      <td>Actinomycetia</td>\n", "      <td>Bifidobacteriales</td>\n", "      <td>Bifidobacteriaceae</td>\n", "      <td>Bifidobacterium</td>\n", "      <td>Bifidobacterium bifidum</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Bifidobacterium longum</td>\n", "      <td>Bacteria</td>\n", "      <td>Actinobacteria</td>\n", "      <td>Actinomycetia</td>\n", "      <td>Bifidobacteriales</td>\n", "      <td>Bifidobacteriaceae</td>\n", "      <td>Bifidobacterium</td>\n", "      <td>Bifidobacterium longum</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            tax_identifier superkingdom          phylum                class  \\\n", "0         Escherichia coli     Bacteria  Proteobacteria  Gammaproteobacteria   \n", "1  Bifidobacterium bifidum     Bacteria  Actinobacteria        Actinomycetia   \n", "2   Bifidobacterium longum     Bacteria  Actinobacteria        Actinomycetia   \n", "\n", "               order              family            genus  \\\n", "0   Enterobacterales  Enterobacteriaceae      Escherichia   \n", "1  Bifidobacteriales  Bifidobacteriaceae  Bifidobacterium   \n", "2  Bifidobacteriales  Bifidobacteriaceae  Bifidobacterium   \n", "\n", "                   species  \n", "0         Escherichia coli  \n", "1  Bifidobacterium bifidum  \n", "2   Bifidobacterium longum  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["(1627, 8)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tax_identifier</th>\n", "      <th>MV_FEI1_t1Q14</th>\n", "      <th>MV_FEI2_t1Q14</th>\n", "      <th>MV_FEI3_t1Q14</th>\n", "      <th>MV_FEI4_t1Q14</th>\n", "      <th>MV_FEI4_t2Q15</th>\n", "      <th>MV_FEI5_t1Q14</th>\n", "      <th>MV_FEI5_t2Q14</th>\n", "      <th>MV_FEI5_t3Q15</th>\n", "      <th>MV_FEM1_t1Q14</th>\n", "      <th>...</th>\n", "      <th>wHAXPI043594-11</th>\n", "      <th>wHAXPI047830-11</th>\n", "      <th>wHAXPI048670-90</th>\n", "      <th>superkingdom</th>\n", "      <th>phylum</th>\n", "      <th>class</th>\n", "      <th>order</th>\n", "      <th>family</th>\n", "      <th>genus</th>\n", "      <th>species</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Escherichia coli</td>\n", "      <td>59.35010</td>\n", "      <td>0.00000</td>\n", "      <td>85.42333</td>\n", "      <td>46.70438</td>\n", "      <td>0.54770</td>\n", "      <td>0.0</td>\n", "      <td>0.02243</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>...</td>\n", "      <td>0.40001</td>\n", "      <td>0.45324</td>\n", "      <td>6.05846</td>\n", "      <td>Bacteria</td>\n", "      <td>Proteobacteria</td>\n", "      <td>Gammaproteobacteria</td>\n", "      <td>Enterobacterales</td>\n", "      <td>Enterobacteriaceae</td>\n", "      <td>Escherichia</td>\n", "      <td>Escherichia coli</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bifidobacterium bifidum</td>\n", "      <td>16.16243</td>\n", "      <td>5.61882</td>\n", "      <td>0.20192</td>\n", "      <td>0.00000</td>\n", "      <td>26.63938</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "      <td>19.20307</td>\n", "      <td>0.13643</td>\n", "      <td>...</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>Bacteria</td>\n", "      <td>Actinobacteria</td>\n", "      <td>Actinomycetia</td>\n", "      <td>Bifidobacteriales</td>\n", "      <td>Bifidobacteriaceae</td>\n", "      <td>Bifidobacterium</td>\n", "      <td>Bifidobacterium bifidum</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Bifidobacterium longum</td>\n", "      <td>7.79189</td>\n", "      <td>60.54102</td>\n", "      <td>0.49524</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "      <td>28.51001</td>\n", "      <td>0.05094</td>\n", "      <td>...</td>\n", "      <td>0.69706</td>\n", "      <td>2.61017</td>\n", "      <td>0.03886</td>\n", "      <td>Bacteria</td>\n", "      <td>Actinobacteria</td>\n", "      <td>Actinomycetia</td>\n", "      <td>Bifidobacteriales</td>\n", "      <td>Bifidobacteriaceae</td>\n", "      <td>Bifidobacterium</td>\n", "      <td>Bifidobacterium longum</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3 rows × 18733 columns</p>\n", "</div>"], "text/plain": ["            tax_identifier  MV_FEI1_t1Q14  MV_FEI2_t1Q14  MV_FEI3_t1Q14  \\\n", "0         Escherichia coli       59.35010        0.00000       85.42333   \n", "1  Bifidobacterium bifidum       16.16243        5.61882        0.20192   \n", "2   Bifidobacterium longum        7.79189       60.54102        0.49524   \n", "\n", "   MV_FEI4_t1Q14  MV_FEI4_t2Q15  MV_FEI5_t1Q14  MV_FEI5_t2Q14  MV_FEI5_t3Q15  \\\n", "0       46.70438        0.54770            0.0        0.02243        0.00000   \n", "1        0.00000       26.63938            0.0        0.00000       19.20307   \n", "2        0.00000        0.00000            0.0        0.00000       28.51001   \n", "\n", "   MV_FEM1_t1Q14  ...  wHAXPI043594-11  wHAXPI047830-11  wHAXPI048670-90  \\\n", "0        0.00000  ...          0.40001          0.45324          6.05846   \n", "1        0.13643  ...          0.00000          0.00000          0.00000   \n", "2        0.05094  ...          0.69706          2.61017          0.03886   \n", "\n", "   superkingdom          phylum                class              order  \\\n", "0      Bacteria  Proteobacteria  Gammaproteobacteria   Enterobacterales   \n", "1      Bacteria  Actinobacteria        Actinomycetia  Bifidobacteriales   \n", "2      Bacteria  Actinobacteria        Actinomycetia  Bifidobacteriales   \n", "\n", "               family            genus                  species  \n", "0  Enterobacteriaceae      Escherichia         Escherichia coli  \n", "1  Bifidobacteriaceae  Bifidobacterium  Bifidobacterium bifidum  \n", "2  Bifidobacteriaceae  Bifidobacterium   Bifidobacterium longum  \n", "\n", "[3 rows x 18733 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tax_identifier</th>\n", "      <th>MV_FEI1_t1Q14</th>\n", "      <th>MV_FEI2_t1Q14</th>\n", "      <th>MV_FEI3_t1Q14</th>\n", "      <th>MV_FEI4_t1Q14</th>\n", "      <th>MV_FEI4_t2Q15</th>\n", "      <th>MV_FEI5_t1Q14</th>\n", "      <th>MV_FEI5_t2Q14</th>\n", "      <th>MV_FEI5_t3Q15</th>\n", "      <th>MV_FEM1_t1Q14</th>\n", "      <th>...</th>\n", "      <th>wHAXPI043594-11</th>\n", "      <th>wHAXPI047830-11</th>\n", "      <th>wHAXPI048670-90</th>\n", "      <th>superkingdom</th>\n", "      <th>phylum</th>\n", "      <th>class</th>\n", "      <th>order</th>\n", "      <th>family</th>\n", "      <th>genus</th>\n", "      <th>species</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Escherichia coli</td>\n", "      <td>59.35010</td>\n", "      <td>0.00000</td>\n", "      <td>85.42333</td>\n", "      <td>46.70438</td>\n", "      <td>0.54770</td>\n", "      <td>0.0</td>\n", "      <td>0.02243</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>...</td>\n", "      <td>0.40001</td>\n", "      <td>0.45324</td>\n", "      <td>6.05846</td>\n", "      <td>Bacteria</td>\n", "      <td>Proteobacteria</td>\n", "      <td>Gammaproteobacteria</td>\n", "      <td>Enterobacterales</td>\n", "      <td>Enterobacteriaceae</td>\n", "      <td>Escherichia</td>\n", "      <td>Escherichia coli</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Bifidobacterium longum</td>\n", "      <td>7.79189</td>\n", "      <td>60.54102</td>\n", "      <td>0.49524</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "      <td>28.51001</td>\n", "      <td>0.05094</td>\n", "      <td>...</td>\n", "      <td>0.69706</td>\n", "      <td>2.61017</td>\n", "      <td>0.03886</td>\n", "      <td>Bacteria</td>\n", "      <td>Actinobacteria</td>\n", "      <td>Actinomycetia</td>\n", "      <td>Bifidobacteriales</td>\n", "      <td>Bifidobacteriaceae</td>\n", "      <td>Bifidobacterium</td>\n", "      <td>Bifidobacterium longum</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55</th>\n", "      <td>Phocaeicola vulgatus</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>39.42854</td>\n", "      <td>15.72732</td>\n", "      <td>0.0</td>\n", "      <td>1.66320</td>\n", "      <td>0.00000</td>\n", "      <td>1.65638</td>\n", "      <td>...</td>\n", "      <td>9.87256</td>\n", "      <td>34.63441</td>\n", "      <td>11.67750</td>\n", "      <td>Bacteria</td>\n", "      <td>Bacteroidetes</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Phocaeicola</td>\n", "      <td>Phocaeicola vulgatus</td>\n", "    </tr>\n", "    <tr>\n", "      <th>86</th>\n", "      <td>Prevotella copri</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>24.69258</td>\n", "      <td>0.00000</td>\n", "      <td>77.93173</td>\n", "      <td>...</td>\n", "      <td>0.12733</td>\n", "      <td>0.00401</td>\n", "      <td>0.00000</td>\n", "      <td>Bacteria</td>\n", "      <td>Bacteroidetes</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Prevotellaceae</td>\n", "      <td>Prevotella</td>\n", "      <td>Prevotella copri</td>\n", "    </tr>\n", "    <tr>\n", "      <th>88</th>\n", "      <td>Bacteroides uniformis</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>10.65525</td>\n", "      <td>0.14548</td>\n", "      <td>0.26326</td>\n", "      <td>...</td>\n", "      <td>5.78249</td>\n", "      <td>7.68644</td>\n", "      <td>0.06681</td>\n", "      <td>Bacteria</td>\n", "      <td>Bacteroidetes</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides uniformis</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>Ruminococcus bromii</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>1.52691</td>\n", "      <td>1.47188</td>\n", "      <td>0.00000</td>\n", "      <td>...</td>\n", "      <td>0.01980</td>\n", "      <td>0.00000</td>\n", "      <td>0.03721</td>\n", "      <td>Bacteria</td>\n", "      <td>Firmicutes</td>\n", "      <td>Clostridia</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Oscillospiraceae</td>\n", "      <td>Ruminococcus</td>\n", "      <td>Ruminococcus bromii</td>\n", "    </tr>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>Eubacterium rectale</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>1.24209</td>\n", "      <td>0.46164</td>\n", "      <td>0.95505</td>\n", "      <td>...</td>\n", "      <td>1.74946</td>\n", "      <td>1.20044</td>\n", "      <td>0.06421</td>\n", "      <td>Bacteria</td>\n", "      <td>Firmicutes</td>\n", "      <td>Clostridia</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Lachnospiraceae_NA</td>\n", "      <td>Eubacterium rectale</td>\n", "    </tr>\n", "    <tr>\n", "      <th>101</th>\n", "      <td>Bacteroides ovatus</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>1.08811</td>\n", "      <td>0.00000</td>\n", "      <td>0.20600</td>\n", "      <td>...</td>\n", "      <td>10.86053</td>\n", "      <td>2.32125</td>\n", "      <td>0.09462</td>\n", "      <td>Bacteria</td>\n", "      <td>Bacteroidetes</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides ovatus</td>\n", "    </tr>\n", "    <tr>\n", "      <th>103</th>\n", "      <td>Faecalibacterium <PERSON>ii</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.89957</td>\n", "      <td>5.81342</td>\n", "      <td>0.79970</td>\n", "      <td>...</td>\n", "      <td>0.76597</td>\n", "      <td>0.45920</td>\n", "      <td>6.23166</td>\n", "      <td>Bacteria</td>\n", "      <td>Firmicutes</td>\n", "      <td>Clostridia</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Oscillospiraceae</td>\n", "      <td>Faecalibacterium</td>\n", "      <td>Faecalibacterium <PERSON>ii</td>\n", "    </tr>\n", "    <tr>\n", "      <th>117</th>\n", "      <td><PERSON><PERSON><PERSON> intestinalis</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.17343</td>\n", "      <td>1.15057</td>\n", "      <td>0.00000</td>\n", "      <td>...</td>\n", "      <td>0.00395</td>\n", "      <td>0.00000</td>\n", "      <td>5.36433</td>\n", "      <td>Bacteria</td>\n", "      <td>Firmicutes</td>\n", "      <td>Clostridia</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Roseburia</td>\n", "      <td><PERSON><PERSON><PERSON> intestinalis</td>\n", "    </tr>\n", "    <tr>\n", "      <th>120</th>\n", "      <td>Lachnospira eligens</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.11092</td>\n", "      <td>0.26718</td>\n", "      <td>0.31868</td>\n", "      <td>...</td>\n", "      <td>0.59743</td>\n", "      <td>0.45380</td>\n", "      <td>0.26122</td>\n", "      <td>Bacteria</td>\n", "      <td>Firmicutes</td>\n", "      <td>Clostridia</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Lachnospira</td>\n", "      <td>Lachnospira eligens</td>\n", "    </tr>\n", "    <tr>\n", "      <th>126</th>\n", "      <td>Bifidobacterium adolescentis</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.06512</td>\n", "      <td>13.31134</td>\n", "      <td>0.45910</td>\n", "      <td>...</td>\n", "      <td>0.31081</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>Bacteria</td>\n", "      <td>Actinobacteria</td>\n", "      <td>Actinomycetia</td>\n", "      <td>Bifidobacteriales</td>\n", "      <td>Bifidobacteriaceae</td>\n", "      <td>Bifidobacterium</td>\n", "      <td>Bifidobacterium adolescentis</td>\n", "    </tr>\n", "    <tr>\n", "      <th>134</th>\n", "      <td>Fusicatenibacter saccharivorans</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.04148</td>\n", "      <td>2.21698</td>\n", "      <td>0.09111</td>\n", "      <td>...</td>\n", "      <td>0.38462</td>\n", "      <td>0.81829</td>\n", "      <td>0.39666</td>\n", "      <td>Bacteria</td>\n", "      <td>Firmicutes</td>\n", "      <td>Clostridia</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Fusicatenibacter</td>\n", "      <td>Fusicatenibacter saccharivorans</td>\n", "    </tr>\n", "    <tr>\n", "      <th>139</th>\n", "      <td><PERSON><PERSON> long<PERSON>a</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.02598</td>\n", "      <td>1.03387</td>\n", "      <td>0.04662</td>\n", "      <td>...</td>\n", "      <td>0.08931</td>\n", "      <td>0.30618</td>\n", "      <td>0.21417</td>\n", "      <td>Bacteria</td>\n", "      <td>Firmicutes</td>\n", "      <td>Clostridia</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON> long<PERSON>a</td>\n", "    </tr>\n", "    <tr>\n", "      <th>150</th>\n", "      <td>Eubacterium siraeum</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.00249</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>...</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>Bacteria</td>\n", "      <td>Firmicutes</td>\n", "      <td>Clostridia</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Oscillospiraceae</td>\n", "      <td>Oscillospiraceae_NA</td>\n", "      <td>Eubacterium siraeum</td>\n", "    </tr>\n", "    <tr>\n", "      <th>152</th>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "      <td>3.95483</td>\n", "      <td>0.00000</td>\n", "      <td>...</td>\n", "      <td>0.03602</td>\n", "      <td>0.00318</td>\n", "      <td>0.04264</td>\n", "      <td>Bacteria</td>\n", "      <td>Firmicutes</td>\n", "      <td>Clostridia</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>157</th>\n", "      <td>Collinsella aerofaciens</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "      <td>0.31752</td>\n", "      <td>0.44014</td>\n", "      <td>...</td>\n", "      <td>0.40132</td>\n", "      <td>0.00795</td>\n", "      <td>0.07962</td>\n", "      <td>Bacteria</td>\n", "      <td>Actinobacteria</td>\n", "      <td>Coriobacteriia</td>\n", "      <td>Coriobacteriales</td>\n", "      <td>Coriobacteriaceae</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Collinsella aerofaciens</td>\n", "    </tr>\n", "    <tr>\n", "      <th>173</th>\n", "      <td>Bacteroides <PERSON>ii</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.14668</td>\n", "      <td>...</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.37451</td>\n", "      <td>Bacteria</td>\n", "      <td>Bacteroidetes</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides <PERSON>ii</td>\n", "    </tr>\n", "    <tr>\n", "      <th>174</th>\n", "      <td>Parabacteroides distasonis</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.14574</td>\n", "      <td>...</td>\n", "      <td>1.09853</td>\n", "      <td>7.03982</td>\n", "      <td>1.02967</td>\n", "      <td>Bacteria</td>\n", "      <td>Bacteroidetes</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Tannerellaceae</td>\n", "      <td>Parabacteroides</td>\n", "      <td>Parabacteroides distasonis</td>\n", "    </tr>\n", "    <tr>\n", "      <th>176</th>\n", "      <td>Bacteroides thetaiotaomicron</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.06814</td>\n", "      <td>...</td>\n", "      <td>0.98720</td>\n", "      <td>2.46371</td>\n", "      <td>0.22252</td>\n", "      <td>Bacteria</td>\n", "      <td>Bacteroidetes</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides thetaiotaomicron</td>\n", "    </tr>\n", "    <tr>\n", "      <th>194</th>\n", "      <td>Bacteroides fragilis</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>...</td>\n", "      <td>0.00000</td>\n", "      <td>0.30557</td>\n", "      <td>0.00000</td>\n", "      <td>Bacteria</td>\n", "      <td>Bacteroidetes</td>\n", "      <td>Bacteroidia</td>\n", "      <td>Bacteroidales</td>\n", "      <td>Bacteroidaceae</td>\n", "      <td>Bacteroides</td>\n", "      <td>Bacteroides fragilis</td>\n", "    </tr>\n", "    <tr>\n", "      <th>330</th>\n", "      <td><PERSON><PERSON><PERSON> hydrogenotrophica</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>...</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>Bacteria</td>\n", "      <td>Firmicutes</td>\n", "      <td>Clostridia</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON> hydrogenotrophica</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>22 rows × 18733 columns</p>\n", "</div>"], "text/plain": ["                      tax_identifier  MV_FEI1_t1Q14  MV_FEI2_t1Q14  \\\n", "0                   Escherichia coli       59.35010        0.00000   \n", "2             Bifidobacterium longum        7.79189       60.54102   \n", "55              Phocaeicola vulgatus        0.00000        0.00000   \n", "86                  Prevotella copri        0.00000        0.00000   \n", "88             Bacteroides uniformis        0.00000        0.00000   \n", "96               Ruminococcus bromii        0.00000        0.00000   \n", "98               Eubacterium rectale        0.00000        0.00000   \n", "101               Bacteroides ovatus        0.00000        0.00000   \n", "103     Faecalibacterium <PERSON>ii        0.00000        0.00000   \n", "117           Roseburia intestinalis        0.00000        0.00000   \n", "120              Lachnospira eligens        0.00000        0.00000   \n", "126     Bifidobacterium adolescentis        0.00000        0.00000   \n", "134  Fusicatenibacter saccharivorans        0.00000        0.00000   \n", "139                Dorea longicatena        0.00000        0.00000   \n", "150              Eubacterium siraeum        0.00000        0.00000   \n", "152                 Blautia we<PERSON>ae        0.00000        0.00000   \n", "157          Collinsella aerofaciens        0.00000        0.00000   \n", "173           Bacteroides <PERSON>        0.00000        0.00000   \n", "174       Parabacteroides distasonis        0.00000        0.00000   \n", "176     Bacteroides thetaiotaomicron        0.00000        0.00000   \n", "194             Bacteroides fragilis        0.00000        0.00000   \n", "330        Blautia hydrogenotrophica        0.00000        0.00000   \n", "\n", "     MV_FEI3_t1Q14  MV_FEI4_t1Q14  MV_FEI4_t2Q15  MV_FEI5_t1Q14  \\\n", "0         85.42333       46.70438        0.54770            0.0   \n", "2          0.49524        0.00000        0.00000            0.0   \n", "55         0.00000       39.42854       15.72732            0.0   \n", "86         0.00000        0.00000        0.00000            0.0   \n", "88         0.00000        0.00000        0.00000            0.0   \n", "96         0.00000        0.00000        0.00000            0.0   \n", "98         0.00000        0.00000        0.00000            0.0   \n", "101        0.00000        0.00000        0.00000            0.0   \n", "103        0.00000        0.00000        0.00000            0.0   \n", "117        0.00000        0.00000        0.00000            0.0   \n", "120        0.00000        0.00000        0.00000            0.0   \n", "126        0.00000        0.00000        0.00000            0.0   \n", "134        0.00000        0.00000        0.00000            0.0   \n", "139        0.00000        0.00000        0.00000            0.0   \n", "150        0.00000        0.00000        0.00000            0.0   \n", "152        0.00000        0.00000        0.00000            0.0   \n", "157        0.00000        0.00000        0.00000            0.0   \n", "173        0.00000        0.00000        0.00000            0.0   \n", "174        0.00000        0.00000        0.00000            0.0   \n", "176        0.00000        0.00000        0.00000            0.0   \n", "194        0.00000        0.00000        0.00000            0.0   \n", "330        0.00000        0.00000        0.00000            0.0   \n", "\n", "     MV_FEI5_t2Q14  MV_FEI5_t3Q15  MV_FEM1_t1Q14  ...  wHAXPI043594-11  \\\n", "0          0.02243        0.00000        0.00000  ...          0.40001   \n", "2          0.00000       28.51001        0.05094  ...          0.69706   \n", "55         1.66320        0.00000        1.65638  ...          9.87256   \n", "86        24.69258        0.00000       77.93173  ...          0.12733   \n", "88        10.65525        0.14548        0.26326  ...          5.78249   \n", "96         1.52691        1.47188        0.00000  ...          0.01980   \n", "98         1.24209        0.46164        0.95505  ...          1.74946   \n", "101        1.08811        0.00000        0.20600  ...         10.86053   \n", "103        0.89957        5.81342        0.79970  ...          0.76597   \n", "117        0.17343        1.15057        0.00000  ...          0.00395   \n", "120        0.11092        0.26718        0.31868  ...          0.59743   \n", "126        0.06512       13.31134        0.45910  ...          0.31081   \n", "134        0.04148        2.21698        0.09111  ...          0.38462   \n", "139        0.02598        1.03387        0.04662  ...          0.08931   \n", "150        0.00249        0.00000        0.00000  ...          0.00000   \n", "152        0.00000        3.95483        0.00000  ...          0.03602   \n", "157        0.00000        0.31752        0.44014  ...          0.40132   \n", "173        0.00000        0.00000        0.14668  ...          0.00000   \n", "174        0.00000        0.00000        0.14574  ...          1.09853   \n", "176        0.00000        0.00000        0.06814  ...          0.98720   \n", "194        0.00000        0.00000        0.00000  ...          0.00000   \n", "330        0.00000        0.00000        0.00000  ...          0.00000   \n", "\n", "     wHAXPI047830-11  wHAXPI048670-90  superkingdom          phylum  \\\n", "0            0.45324          6.05846      Bacteria  Proteobacteria   \n", "2            2.61017          0.03886      Bacteria  Actinobacteria   \n", "55          34.63441         11.67750      Bacteria   Bacteroidetes   \n", "86           0.00401          0.00000      Bacteria   Bacteroidetes   \n", "88           7.68644          0.06681      Bacteria   Bacteroidetes   \n", "96           0.00000          0.03721      Bacteria      Firmicutes   \n", "98           1.20044          0.06421      Bacteria      Firmicutes   \n", "101          2.32125          0.09462      Bacteria   Bacteroidetes   \n", "103          0.45920          6.23166      Bacteria      Firmicutes   \n", "117          0.00000          5.36433      Bacteria      Firmicutes   \n", "120          0.45380          0.26122      Bacteria      Firmicutes   \n", "126          0.00000          0.00000      Bacteria  Actinobacteria   \n", "134          0.81829          0.39666      Bacteria      Firmicutes   \n", "139          0.30618          0.21417      Bacteria      Firmicutes   \n", "150          0.00000          0.00000      Bacteria      Firmicutes   \n", "152          0.00318          0.04264      Bacteria      Firmicutes   \n", "157          0.00795          0.07962      Bacteria  Actinobacteria   \n", "173          0.00000          0.37451      Bacteria   Bacteroidetes   \n", "174          7.03982          1.02967      Bacteria   Bacteroidetes   \n", "176          2.46371          0.22252      Bacteria   Bacteroidetes   \n", "194          0.30557          0.00000      Bacteria   Bacteroidetes   \n", "330          0.00000          0.00000      Bacteria      Firmicutes   \n", "\n", "                   class              order              family  \\\n", "0    Gammaproteobacteria   Enterobacterales  Enterobacteriaceae   \n", "2          Actinomycetia  Bifidobacteriales  Bifidobacteriaceae   \n", "55           Bacteroidia      Bacteroidales      Bacteroidaceae   \n", "86           Bacteroidia      Bacteroidales      Prevotellaceae   \n", "88           Bacteroidia      Bacteroidales      Bacteroidaceae   \n", "96            Clostridia      Eubacteriales    Oscillospiraceae   \n", "98            Clostridia      Eubacteriales     Lachnospiraceae   \n", "101          Bacteroidia      Bacteroidales      Bacteroidaceae   \n", "103           Clostridia      Eubacteriales    Oscillospiraceae   \n", "117           Clostridia      Eubacteriales     Lachnospiraceae   \n", "120           Clostridia      Eubacteriales     Lachnospiraceae   \n", "126        Actinomycetia  Bifidobacteriales  Bifidobacteriaceae   \n", "134           Clostridia      Eubacteriales     Lachnospiraceae   \n", "139           Clostridia      Eubacteriales     Lachnospiraceae   \n", "150           Clostridia      Eubacteriales    Oscillospiraceae   \n", "152           Clostridia      Eubacteriales     Lachnospiraceae   \n", "157       Coriobacteriia   Coriobacteriales   Coriobacteriaceae   \n", "173          Bacteroidia      Bacteroidales      Bacteroidaceae   \n", "174          Bacteroidia      Bacteroidales      Tannerellaceae   \n", "176          Bacteroidia      Bacteroidales      Bacteroidaceae   \n", "194          Bacteroidia      Bacteroidales      Bacteroidaceae   \n", "330           Clostridia      Eubacteriales     Lachnospiraceae   \n", "\n", "                   genus                          species  \n", "0            Escherichia                 Escherichia coli  \n", "2        Bifidobacterium           Bifidobacterium longum  \n", "55           Phocaeicola             Phocaeicola vulgatus  \n", "86            Prevotella                 Prevotella copri  \n", "88           Bacteroides            Bacteroides uniformis  \n", "96          Ruminococcus              Ruminococcus bromii  \n", "98    Lachnospiraceae_NA              Eubacterium rectale  \n", "101          Bacteroides               Bacteroides ovatus  \n", "103     Faecalibacterium     Faecalibacterium <PERSON>ii  \n", "117            Roseburia           Roseburia intestinalis  \n", "120          Lachnospira              Lachnospira eligens  \n", "126      Bifidobacterium     Bifidobacterium adolescentis  \n", "134     Fusicatenibacter  Fusicatenibacter saccharivorans  \n", "139                Dorea                Dorea longicatena  \n", "150  Oscillospiraceae_NA              Eubacterium siraeum  \n", "152              Blau<PERSON> we<PERSON>ae  \n", "157          Collinsella          <PERSON>ella aerofaciens  \n", "173          Bacteroides           Bacteroides <PERSON>ii  \n", "174      Parabacteroides       Parabacteroides distasonis  \n", "176          Bacteroides     Bacteroides thetaiotaomicron  \n", "194          Bacteroides             Bacteroides fragilis  \n", "330              Blautia        Blautia hydrogenotrophica  \n", "\n", "[22 rows x 18733 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#big table with abundance of different species in all thausands of samples from the data collection\n", "relab=pd.read_csv(\"data_curated_microbiome/relabundance.csv\")\n", "relab.rename(columns={'Unnamed: 0': 'tax_identifier'},inplace=True)\n", "display(relab.head(3))\n", "display(relab.shape)\n", "\n", "#information about samples\n", "colnames=pd.read_csv(\"data_curated_microbiome/relabundance_colData.csv\")\n", "colnames.rename(columns={'Unnamed: 0': 'sample'},inplace=True)\n", "display(colnames.head(3))\n", "display(colnames.shape)\n", "\n", "#information about different species detected in the different samples\n", "rownames=pd.read_csv(\"data_curated_microbiome/relabundance_rowData.csv\")\n", "rownames.rename(columns={'Unnamed: 0': 'tax_identifier'},inplace=True)\n", "display(rownames.head(3))\n", "display(rownames.shape)\n", "\n", "#add species information to major data table. Used for groupby analysis later on\n", "#relab.join\n", "relab2 = relab.merge(rownames, on='tax_identifier', how='inner')\n", "display(relab2.head(3))\n", "\n", "#select abundance data for experimentally characterized strains\n", "relab2_exp=relab2.loc[relab2[\"tax_identifier\"].isin(characterized_species)]\n", "display(relab2_exp)"]}, {"cell_type": "markdown", "id": "f4cf2f17-1113-4885-804d-2441dff5fbc3", "metadata": {}, "source": ["# Go through all microbiome samples in dataset and estimate uptake as well as excretion values"]}, {"cell_type": "code", "execution_count": 5, "id": "4c39f33f-0bcc-4616-8492-38672be516d4", "metadata": {}, "outputs": [{"data": {"text/plain": ["'number samples included:'"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["18725"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["tax_identifier     Escherichia coliBifidobacterium longumPhocaeic...\n", "MV_FEI1_t1Q14                                               67.14199\n", "MV_FEI2_t1Q14                                               60.54102\n", "MV_FEI3_t1Q14                                               85.91857\n", "MV_FEI4_t1Q14                                               86.13292\n", "                                         ...                        \n", "wHAXPI043592-8                                              76.80946\n", "wHAXPI043593-9                                              60.72212\n", "wHAXPI043594-11                                              34.1844\n", "wHAXPI047830-11                                             60.76766\n", "wHAXPI048670-90                                             32.25467\n", "Length: 18726, dtype: object"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["sample MV_FEI5_t1Q14 no abundance (sample number without abundance: 1)\n", "sample SID635_12M no abundance (sample number without abundance: 2)\n", "sample N2_031_008G1 no abundance (sample number without abundance: 3)\n", "sample N2_031_012G1 no abundance (sample number without abundance: 4)\n", "sample N2_031_018G1 no abundance (sample number without abundance: 5)\n", "sample N2_031_040G1 no abundance (sample number without abundance: 6)\n", "sample N2_031_055G1 no abundance (sample number without abundance: 7)\n", "sample N2_038_009G1 no abundance (sample number without abundance: 8)\n", "sample N2_038_011G1 no abundance (sample number without abundance: 9)\n", "sample N2_038_013G1 no abundance (sample number without abundance: 10)\n", "sample N2_038_015G1 no abundance (sample number without abundance: 11)\n", "sample N2_038_018G1 no abundance (sample number without abundance: 12)\n", "sample N2_038_020G1 no abundance (sample number without abundance: 13)\n", "sample N2_038_036G1 no abundance (sample number without abundance: 14)\n", "sample N2_038_039G1 no abundance (sample number without abundance: 15)\n", "sample N2_038_044G1 no abundance (sample number without abundance: 16)\n", "sample N2_039_008G1 no abundance (sample number without abundance: 17)\n", "sample N2_039_011G1 no abundance (sample number without abundance: 18)\n", "sample N2_039_013G1 no abundance (sample number without abundance: 19)\n", "sample N2_039_015G1 no abundance (sample number without abundance: 20)\n", "sample N2_039_018G1 no abundance (sample number without abundance: 21)\n", "sample N2_039_020G1 no abundance (sample number without abundance: 22)\n", "sample N2_039_021G1 no abundance (sample number without abundance: 23)\n", "sample N2_039_044G1 no abundance (sample number without abundance: 24)\n", "sample N2_061_008G1 no abundance (sample number without abundance: 25)\n", "sample N2_061_015G1 no abundance (sample number without abundance: 26)\n", "sample N2_061_019G1 no abundance (sample number without abundance: 27)\n", "sample N2_061_021G1 no abundance (sample number without abundance: 28)\n", "sample N2_061_025G1 no abundance (sample number without abundance: 29)\n", "sample N2_061_082G1 no abundance (sample number without abundance: 30)\n", "sample N2_064_011G1 no abundance (sample number without abundance: 31)\n", "sample N2_064_017G1 no abundance (sample number without abundance: 32)\n", "sample N2_066_009G1 no abundance (sample number without abundance: 33)\n", "sample N2_066_011G1 no abundance (sample number without abundance: 34)\n", "sample N2_066_013G1 no abundance (sample number without abundance: 35)\n", "sample N2_066_015G1 no abundance (sample number without abundance: 36)\n", "sample N2_066_017G1 no abundance (sample number without abundance: 37)\n", "sample N2_066_019G1 no abundance (sample number without abundance: 38)\n", "sample N2_066_021G1 no abundance (sample number without abundance: 39)\n", "sample N2_066_024G1 no abundance (sample number without abundance: 40)\n", "sample N2_066_031G1 no abundance (sample number without abundance: 41)\n", "sample N2_066_033G1 no abundance (sample number without abundance: 42)\n", "sample N2_066_037G1 no abundance (sample number without abundance: 43)\n", "sample N2_066_048G1 no abundance (sample number without abundance: 44)\n", "sample N2_066_050G1 no abundance (sample number without abundance: 45)\n", "sample N2_070_016G1 no abundance (sample number without abundance: 46)\n", "sample N2_070_019G1 no abundance (sample number without abundance: 47)\n", "sample N2_070_020G1 no abundance (sample number without abundance: 48)\n", "sample N2_070_030G1 no abundance (sample number without abundance: 49)\n", "sample N2_070_057G1 no abundance (sample number without abundance: 50)\n", "sample N2_088_008G1 no abundance (sample number without abundance: 51)\n", "sample N2_088_013G1 no abundance (sample number without abundance: 52)\n", "sample N2_088_022G1 no abundance (sample number without abundance: 53)\n", "sample N2_088_027G1 no abundance (sample number without abundance: 54)\n", "sample N2_088_034G1 no abundance (sample number without abundance: 55)\n", "sample N2_088_042G1 no abundance (sample number without abundance: 56)\n", "sample N2_093_007G1 no abundance (sample number without abundance: 57)\n", "sample N2_093_008G1 no abundance (sample number without abundance: 58)\n", "sample N2_093_008G2 no abundance (sample number without abundance: 59)\n", "sample N2_093_010G1 no abundance (sample number without abundance: 60)\n", "sample S2_002_005G1 no abundance (sample number without abundance: 61)\n", "sample S2_002_007G1 no abundance (sample number without abundance: 62)\n", "sample S2_002_010G1 no abundance (sample number without abundance: 63)\n", "sample S2_002_011G1 no abundance (sample number without abundance: 64)\n", "sample S2_002_012G1 no abundance (sample number without abundance: 65)\n", "sample S2_002_013G1 no abundance (sample number without abundance: 66)\n", "sample S2_002_014G1 no abundance (sample number without abundance: 67)\n", "sample S2_002_015G1 no abundance (sample number without abundance: 68)\n", "sample S2_002_016G1 no abundance (sample number without abundance: 69)\n", "sample S2_002_017G1 no abundance (sample number without abundance: 70)\n", "sample S2_002_018G1 no abundance (sample number without abundance: 71)\n", "sample S2_002_019G1 no abundance (sample number without abundance: 72)\n", "sample S2_002_020G1 no abundance (sample number without abundance: 73)\n", "sample S2_002_022G1 no abundance (sample number without abundance: 74)\n", "sample S2_002_023G1 no abundance (sample number without abundance: 75)\n", "sample S2_002_027G1 no abundance (sample number without abundance: 76)\n", "sample S2_002_028G1 no abundance (sample number without abundance: 77)\n", "sample S2_003_005G1 no abundance (sample number without abundance: 78)\n", "sample S2_003_007G1 no abundance (sample number without abundance: 79)\n", "sample S2_003_008G1 no abundance (sample number without abundance: 80)\n", "sample S2_003_009G1 no abundance (sample number without abundance: 81)\n", "sample S2_003_010G1 no abundance (sample number without abundance: 82)\n", "sample S2_003_011G1 no abundance (sample number without abundance: 83)\n", "sample S2_003_012G1 no abundance (sample number without abundance: 84)\n", "sample S2_003_013G1 no abundance (sample number without abundance: 85)\n", "sample S2_003_014G1 no abundance (sample number without abundance: 86)\n", "sample S2_003_015G1 no abundance (sample number without abundance: 87)\n", "sample S2_003_016G1 no abundance (sample number without abundance: 88)\n", "sample S2_003_017G1 no abundance (sample number without abundance: 89)\n", "sample S2_003_018G1 no abundance (sample number without abundance: 90)\n", "sample S2_003_019G1 no abundance (sample number without abundance: 91)\n", "sample S2_003_020G1 no abundance (sample number without abundance: 92)\n", "sample S2_003_021G1 no abundance (sample number without abundance: 93)\n", "sample S2_004_006G1 no abundance (sample number without abundance: 94)\n", "sample S2_004_007G1 no abundance (sample number without abundance: 95)\n", "sample S2_004_008G1 no abundance (sample number without abundance: 96)\n", "sample S2_004_009G1 no abundance (sample number without abundance: 97)\n", "sample S2_004_010G1 no abundance (sample number without abundance: 98)\n", "sample S2_004_011G1 no abundance (sample number without abundance: 99)\n", "sample S2_004_012G1 no abundance (sample number without abundance: 100)\n", "sample S2_004_013G1 no abundance (sample number without abundance: 101)\n", "sample S2_004_014G1 no abundance (sample number without abundance: 102)\n", "sample S2_004_016G1 no abundance (sample number without abundance: 103)\n", "sample S2_006_005G1 no abundance (sample number without abundance: 104)\n", "sample S2_008_010G1 no abundance (sample number without abundance: 105)\n", "sample S2_008_011G1 no abundance (sample number without abundance: 106)\n", "sample S2_008_015G1 no abundance (sample number without abundance: 107)\n", "sample S2_008_017G1 no abundance (sample number without abundance: 108)\n", "sample S2_008_020G1 no abundance (sample number without abundance: 109)\n", "sample S2_008_023G1 no abundance (sample number without abundance: 110)\n", "sample S2_008_024G1 no abundance (sample number without abundance: 111)\n", "sample S2_008_026G1 no abundance (sample number without abundance: 112)\n", "sample S2_009_005G1 no abundance (sample number without abundance: 113)\n", "sample S2_009_009G1 no abundance (sample number without abundance: 114)\n", "sample S2_009_010G1 no abundance (sample number without abundance: 115)\n", "sample S2_009_011G1 no abundance (sample number without abundance: 116)\n", "sample S2_009_013G1 no abundance (sample number without abundance: 117)\n", "sample S2_009_014G1 no abundance (sample number without abundance: 118)\n", "sample S2_009_015G1 no abundance (sample number without abundance: 119)\n", "sample S2_009_016G1 no abundance (sample number without abundance: 120)\n", "sample S2_009_017G1 no abundance (sample number without abundance: 121)\n", "sample S2_009_018G1 no abundance (sample number without abundance: 122)\n", "sample S2_009_019G1 no abundance (sample number without abundance: 123)\n", "sample S2_009_020G1 no abundance (sample number without abundance: 124)\n", "sample S2_009_021G1 no abundance (sample number without abundance: 125)\n", "sample S2_009_022G1 no abundance (sample number without abundance: 126)\n", "sample S2_009_023G1 no abundance (sample number without abundance: 127)\n", "sample S2_009_024G1 no abundance (sample number without abundance: 128)\n", "sample S2_009_025G1 no abundance (sample number without abundance: 129)\n", "sample S2_009_026G1 no abundance (sample number without abundance: 130)\n", "sample S2_009_027G1 no abundance (sample number without abundance: 131)\n", "sample S2_009_028G1 no abundance (sample number without abundance: 132)\n", "sample S2_010_005G1 no abundance (sample number without abundance: 133)\n", "sample S2_012_005G1 no abundance (sample number without abundance: 134)\n", "sample S2_012_007G1 no abundance (sample number without abundance: 135)\n", "sample S2_012_008G1 no abundance (sample number without abundance: 136)\n", "sample S2_012_009G1 no abundance (sample number without abundance: 137)\n", "sample S2_012_010G1 no abundance (sample number without abundance: 138)\n", "sample S2_012_011G1 no abundance (sample number without abundance: 139)\n", "sample S2_012_012G1 no abundance (sample number without abundance: 140)\n", "sample S2_012_013G1 no abundance (sample number without abundance: 141)\n", "sample S2_012_014G1 no abundance (sample number without abundance: 142)\n", "sample S2_012_015G1 no abundance (sample number without abundance: 143)\n", "sample S2_012_016G1 no abundance (sample number without abundance: 144)\n", "sample S2_012_017G1 no abundance (sample number without abundance: 145)\n", "sample S2_012_019G1 no abundance (sample number without abundance: 146)\n", "sample S2_012_020G1 no abundance (sample number without abundance: 147)\n", "sample S2_012_021G1 no abundance (sample number without abundance: 148)\n", "sample S2_012_022G1 no abundance (sample number without abundance: 149)\n", "sample S2_014_007G1 no abundance (sample number without abundance: 150)\n", "sample S2_014_009G1 no abundance (sample number without abundance: 151)\n", "sample S2_014_010G1 no abundance (sample number without abundance: 152)\n", "sample S2_014_011G1 no abundance (sample number without abundance: 153)\n", "sample S2_014_013G1 no abundance (sample number without abundance: 154)\n", "sample S2_014_015G1 no abundance (sample number without abundance: 155)\n", "sample S2_014_016G1 no abundance (sample number without abundance: 156)\n", "sample S2_014_017G1 no abundance (sample number without abundance: 157)\n", "sample S2_014_018G1 no abundance (sample number without abundance: 158)\n", "sample S2_014_019G1 no abundance (sample number without abundance: 159)\n", "sample S2_014_020G1 no abundance (sample number without abundance: 160)\n", "sample S2_014_021G1 no abundance (sample number without abundance: 161)\n", "sample S2_014_023G1 no abundance (sample number without abundance: 162)\n", "sample S2_014_024G1 no abundance (sample number without abundance: 163)\n", "sample S2_014_025G1 no abundance (sample number without abundance: 164)\n", "sample S2_014_026G1 no abundance (sample number without abundance: 165)\n", "sample S2_014_028G1 no abundance (sample number without abundance: 166)\n", "sample S2_015_006G1 no abundance (sample number without abundance: 167)\n", "sample S2_015_009G1 no abundance (sample number without abundance: 168)\n", "sample S2_015_010G1 no abundance (sample number without abundance: 169)\n", "sample S2_015_011G1 no abundance (sample number without abundance: 170)\n", "sample S2_015_013G1 no abundance (sample number without abundance: 171)\n", "sample S2_016_007G1 no abundance (sample number without abundance: 172)\n", "sample S2_016_008G1 no abundance (sample number without abundance: 173)\n", "sample S2_016_009G1 no abundance (sample number without abundance: 174)\n", "sample S2_016_010G1 no abundance (sample number without abundance: 175)\n", "sample S2_016_011G1 no abundance (sample number without abundance: 176)\n", "sample S2_016_013G1 no abundance (sample number without abundance: 177)\n", "sample S2_016_014G1 no abundance (sample number without abundance: 178)\n", "sample S2_016_015G1 no abundance (sample number without abundance: 179)\n", "sample S2_016_016G1 no abundance (sample number without abundance: 180)\n", "sample S2_016_017G1 no abundance (sample number without abundance: 181)\n", "sample S2_016_018G1 no abundance (sample number without abundance: 182)\n", "sample S2_016_019G1 no abundance (sample number without abundance: 183)\n", "sample S2_016_021G1 no abundance (sample number without abundance: 184)\n", "sample S2_CON_001E1 no abundance (sample number without abundance: 185)\n", "sample NCS-003-Meconium_microbiome no abundance (sample number without abundance: 186)\n", "sample NCS-005-<PERSON>ool-maternal_microbiome no abundance (sample number without abundance: 187)\n", "sample NCS-028-Meconium_microbiome no abundance (sample number without abundance: 188)\n", "sample NCS-053-Meconium_microbiome no abundance (sample number without abundance: 189)\n", "sample LD-Run1-06 no abundance (sample number without abundance: 190)\n", "sample CA_C10008IS2123FE_t2M15 no abundance (sample number without abundance: 191)\n", "sample CA_C10020IS2336FE_t1M15 no abundance (sample number without abundance: 192)\n", "sample CA_C10020IS2343FE_t3M15 no abundance (sample number without abundance: 193)\n", "sample CA_C10024IS2408FE_t1M15 no abundance (sample number without abundance: 194)\n", "sample CA_C10032IS2555FE_t3M15 no abundance (sample number without abundance: 195)\n", "sample CA_C10039IS2681FE_t3M16 no abundance (sample number without abundance: 196)\n", "sample CA_C10043IS2749FE_t2M16 no abundance (sample number without abundance: 197)\n", "sample p8816_mo12 no abundance (sample number without abundance: 198)\n", "sample p8585_mo4 no abundance (sample number without abundance: 199)\n", "sample p8748_mo5 no abundance (sample number without abundance: 200)\n", "sample p9216_mo3 no abundance (sample number without abundance: 201)\n", "sample p9220_mo1 no abundance (sample number without abundance: 202)\n", "sample MG100191 no abundance (sample number without abundance: 203)\n", "sample HSM67VIL no abundance (sample number without abundance: 204)\n", "sample MSM9VZJZ no abundance (sample number without abundance: 205)\n", "sample PSM7J19F no abundance (sample number without abundance: 206)\n", "sample PSM7J19J no abundance (sample number without abundance: 207)\n", "sample HMP2_J05379_M_ST_T0_B0_0120_ZOZOW1T-44_H15WVBG no abundance (sample number without abundance: 208)\n", "sample HMP2_J05381_M_ST_T0_B0_0120_ZOZOW1T-48b_H15WVBG no abundance (sample number without abundance: 209)\n", "sample HMP2_J05387_M_ST_T0_B0_0120_ZOZOW1T-54b_H15WVBG no abundance (sample number without abundance: 210)\n", "sample SID62affe5c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 211)\n", "sample SID62cbe612-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 212)\n", "sample SID633a81d0-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 213)\n", "sample SID64423bea-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 214)\n", "sample SID64bbfa2a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 215)\n", "sample SID6515b0b0-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 216)\n", "sample SID657f81ac-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 217)\n", "sample SID66a0feda-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 218)\n", "sample SID69a55b58-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 219)\n", "sample SID6a8c22c2-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 220)\n", "sample SID6cf5af38-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 221)\n", "sample SID6e0a2cbe-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 222)\n", "sample SID6e7c2148-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 223)\n", "sample SID6e8c8eb6-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 224)\n", "sample SID6ec12e0a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 225)\n", "sample SID6ed0c900-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 226)\n", "sample SID6f0181da-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 227)\n", "sample SID6f42328e-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 228)\n", "sample SID7028073c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 229)\n", "sample SID705ac7ee-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 230)\n", "sample SID712f7fb6-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 231)\n", "sample SID720e1046-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 232)\n", "sample SID72970d10-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 233)\n", "sample SID72d9530a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 234)\n", "sample SID742f9d7c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 235)\n", "sample SID751f0a56-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 236)\n", "sample SID7636bc22-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 237)\n", "sample SID766d76fe-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 238)\n", "sample SID76a45cdc-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 239)\n", "sample SID76d9d24a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 240)\n", "sample SID77cde0b0-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 241)\n", "sample SID78c45256-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 242)\n", "sample SID7a464b52-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 243)\n", "sample SID7b0aa40c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 244)\n", "sample SID7d36da20-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 245)\n", "sample SID7d4ef51a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 246)\n", "sample SID7eadd96c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 247)\n", "sample SID7f12ed3e-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 248)\n", "sample SID7f2d179a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 249)\n", "sample SID7fac8d36-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 250)\n", "sample SID7fc5d30e-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 251)\n", "sample SID7ff7d05c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 252)\n", "sample SID80f0f916-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 253)\n", "sample SID81098a62-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 254)\n", "sample SID81222810-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 255)\n", "sample SID815390bc-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 256)\n", "sample SID81efe142-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 257)\n", "sample SID8208e1f6-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 258)\n", "sample SID82708054-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 259)\n", "sample SID82b621a4-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 260)\n", "sample SID82df6d34-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 261)\n", "sample SID82edf4b2-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 262)\n", "sample SID82fbfa76-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 263)\n", "sample SID83203346-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 264)\n", "sample SID8346ebe4-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 265)\n", "sample SID836f1650-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 266)\n", "sample SID83acf4f2-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 267)\n", "sample SID83c107bc-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 268)\n", "sample SID847b9924-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 269)\n", "sample SID85c86316-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 270)\n", "sample SID862be18e-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 271)\n", "sample SID864470dc-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 272)\n", "sample SID865d378e-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 273)\n", "sample SID87a79076-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 274)\n", "sample SID880cf920-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 275)\n", "sample SID88971088-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 276)\n", "sample SID88c71aee-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 277)\n", "sample SID895710cc-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 278)\n", "sample SID898a85f6-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 279)\n", "sample SID89bfcc52-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 280)\n", "sample SID8ac54172-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 281)\n", "sample SID8b0fa91a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 282)\n", "sample SID8b48d456-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 283)\n", "sample SID8c0a6d5a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 284)\n", "sample SID8c8885fa-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 285)\n", "sample SID8d053e7e-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 286)\n", "sample SID8d81be18-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 287)\n", "sample SID8e1d3d0c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 288)\n", "sample SID8e2e66c2-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 289)\n", "sample SID8e3e95d8-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 290)\n", "sample SID8ec02788-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 291)\n", "sample SID91ae54c4-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 292)\n", "sample SID91c1af1a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 293)\n", "sample SID92230b7a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 294)\n", "sample SID92364b04-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 295)\n", "sample SID924a0f0e-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 296)\n", "sample SID92f667ea-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 297)\n", "sample SID9406e8e4-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 298)\n", "sample SID94402140-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 299)\n", "sample SID9479573a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 300)\n", "sample SID948c6d5c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 301)\n", "sample SID94b2934c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 302)\n", "sample SID94c5d2ae-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 303)\n", "sample SID94d93dd0-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 304)\n", "sample SID95c71f46-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 305)\n", "sample SID9613c580-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 306)\n", "sample SID96b11baa-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 307)\n", "sample SID97163148-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 308)\n", "sample SID972c1cf6-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 309)\n", "sample SID998b9b66-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 310)\n", "sample SID99d841aa-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 311)\n", "sample SID9ba3be6a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 312)\n", "sample SID9d2a7eae-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 313)\n", "sample SID9d444546-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 314)\n", "sample SID9e880154-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 315)\n", "sample SID9ed42250-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 316)\n", "sample SID9eed2c6e-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 317)\n", "sample SID9f54df30-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 318)\n", "sample a0a9fbea-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 319)\n", "sample a0c3921c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 320)\n", "sample a0dd2164-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 321)\n", "sample a0f8ee08-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 322)\n", "sample a111c0d6-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 323)\n", "sample a1dbd178-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 324)\n", "sample a1f32a08-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 325)\n", "sample a22605c2-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 326)\n", "sample a23f5bd0-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 327)\n", "sample a276761a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 328)\n", "sample a2a7a01e-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 329)\n", "sample a30113d8-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 330)\n", "sample a31ab16c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 331)\n", "sample a3280290-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 332)\n", "sample a337024a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 333)\n", "sample a3488c54-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 334)\n", "sample a367ac1a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 335)\n", "sample a3fdb96c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 336)\n", "sample a40c51c0-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 337)\n", "sample a496ac6c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 338)\n", "sample a4d74a7e-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 339)\n", "sample a4e5fb50-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 340)\n", "sample a4f45e02-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 341)\n", "sample a52f3978-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 342)\n", "sample a6025e48-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 343)\n", "sample a622098c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 344)\n", "sample a678029c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 345)\n", "sample a687e3a6-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 346)\n", "sample a6f00602-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 347)\n", "sample a7498470-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 348)\n", "sample a759ac38-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 349)\n", "sample a7911d3a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 350)\n", "sample a7a491a8-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 351)\n", "sample a831a138-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 352)\n", "sample a8451088-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 353)\n", "sample a858baa2-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 354)\n", "sample a912a39a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 355)\n", "sample a928b086-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 356)\n", "sample a950c986-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 357)\n", "sample aa70304a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 358)\n", "sample aa931bbe-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 359)\n", "sample aacaff20-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 360)\n", "sample ab1cf064-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 361)\n", "sample ac0e247a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 362)\n", "sample ae7e87ea-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 363)\n", "sample aefc0f58-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 364)\n", "sample af1567be-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 365)\n", "sample af2f5282-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 366)\n", "sample af48eb52-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 367)\n", "sample af95fc94-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 368)\n", "sample afca14ca-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 369)\n", "sample b0e77c12-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 370)\n", "sample b0f76708-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 371)\n", "sample b187a458-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 372)\n", "sample b1975204-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 373)\n", "sample b1c65a04-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 374)\n", "sample b2191d34-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 375)\n", "sample b2488362-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 376)\n", "sample b2588050-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 377)\n", "sample b2682f64-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 378)\n", "sample b382aee2-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 379)\n", "sample b3a1f7de-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 380)\n", "sample b3b1a422-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 381)\n", "sample b3c1d9e6-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 382)\n", "sample b3e1802a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 383)\n", "sample b400cbb0-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 384)\n", "sample b41013d6-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 385)\n", "sample b4d8faa8-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 386)\n", "sample b52f89fe-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 387)\n", "sample b53fabfe-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 388)\n", "sample b62950ba-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 389)\n", "sample b772af0c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 390)\n", "sample b825961c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 391)\n", "sample b87f4392-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 392)\n", "sample b8b17db2-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 393)\n", "sample b8fdcd48-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 394)\n", "sample b9607538-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 395)\n", "sample bb1be90c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 396)\n", "sample bb5eeed2-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 397)\n", "sample bb8a011c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 398)\n", "sample bbca2a30-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 399)\n", "sample bc6d8612-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 400)\n", "sample bcdcfa10-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 401)\n", "sample bcf732ea-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 402)\n", "sample bd1f53b0-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 403)\n", "sample bd6186fe-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 404)\n", "sample bd7f94aa-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 405)\n", "sample bda4d6ca-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 406)\n", "sample bddbbeba-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 407)\n", "sample bdee828e-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 408)\n", "sample bdfef8c6-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 409)\n", "sample be0fba1c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 410)\n", "sample be1f9202-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 411)\n", "sample be2f9b20-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 412)\n", "sample bfd415b4-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 413)\n", "sample c039335e-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 414)\n", "sample c0528962-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 415)\n", "sample c262288e-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 416)\n", "sample c295a4ac-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 417)\n", "sample c3002e76-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 418)\n", "sample c333923e-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 419)\n", "sample c34c8686-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 420)\n", "sample c468b008-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 421)\n", "sample c5311b88-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 422)\n", "sample c54efc52-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 423)\n", "sample c596de50-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 424)\n", "sample c5b09264-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 425)\n", "sample c6b49368-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 426)\n", "sample c6ce4eb6-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 427)\n", "sample c7695802-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 428)\n", "sample c78237fa-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 429)\n", "sample c79bb73e-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 430)\n", "sample c7e61c98-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 431)\n", "sample c84a5622-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 432)\n", "sample c8caff84-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 433)\n", "sample c8e70e72-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 434)\n", "sample c9d7eab8-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 435)\n", "sample ca7d62f4-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 436)\n", "sample cafcb52c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 437)\n", "sample cb2d6640-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 438)\n", "sample cb3d6dce-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 439)\n", "sample cbd006ca-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 440)\n", "sample cd11c4d8-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 441)\n", "sample ce28a3b4-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 442)\n", "sample ce8f9506-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 443)\n", "sample cf1326c8-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 444)\n", "sample cf2ce694-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 445)\n", "sample cf74b85c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 446)\n", "sample cf9e7f34-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 447)\n", "sample d1e8330c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 448)\n", "sample d21a0472-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 449)\n", "sample d24df9f8-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 450)\n", "sample d2f4a2a8-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 451)\n", "sample d30e4b0e-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 452)\n", "sample d329884c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 453)\n", "sample d35c4d5e-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 454)\n", "sample d38fa1ea-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 455)\n", "sample d3c13dcc-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 456)\n", "sample d459c0ec-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 457)\n", "sample d4734562-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 458)\n", "sample d5700392-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 459)\n", "sample d5bc3de8-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 460)\n", "sample d62236f2-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 461)\n", "sample d6579022-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 462)\n", "sample d6a4a38a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 463)\n", "sample d6d8a720-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 464)\n", "sample d75fd600-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 465)\n", "sample d7792bf0-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 466)\n", "sample d7e148ca-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 467)\n", "sample d87cbeea-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 468)\n", "sample d897762c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 469)\n", "sample d9192492-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 470)\n", "sample d9353b64-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 471)\n", "sample d9adf07c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 472)\n", "sample d9c8c8d4-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 473)\n", "sample d9e27090-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 474)\n", "sample d9fc1f36-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 475)\n", "sample da2f9ffa-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 476)\n", "sample daeb883c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 477)\n", "sample db391c64-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 478)\n", "sample db547e82-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 479)\n", "sample dbb4b928-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 480)\n", "sample dd1f5d04-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 481)\n", "sample dd382582-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 482)\n", "sample dedb98ba-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 483)\n", "sample df2ebb80-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 484)\n", "sample dfe88cc2-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 485)\n", "sample e00f5f78-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 486)\n", "sample e09dfd32-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 487)\n", "sample e0b194b4-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 488)\n", "sample e0f1368c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 489)\n", "sample e1558466-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 490)\n", "sample e1910fae-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 491)\n", "sample e1a5156c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 492)\n", "sample e2452020-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 493)\n", "sample e281c4da-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 494)\n", "sample e2d0071c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 495)\n", "sample e2e4d39a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 496)\n", "sample e30deb54-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 497)\n", "sample e321cfa2-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 498)\n", "sample e3358308-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 499)\n", "sample e3c06c2a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 500)\n", "sample e3d44510-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 501)\n", "sample e3e7f2f4-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 502)\n", "sample e4244a56-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 503)\n", "sample e4615860-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 504)\n", "sample e4992b3c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 505)\n", "sample e57b5cd2-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 506)\n", "sample e5f3e436-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 507)\n", "sample e62fe436-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 508)\n", "sample e6800d9e-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 509)\n", "sample e6bd6f90-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 510)\n", "sample e7e74cba-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 511)\n", "sample e7fb5282-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 512)\n", "sample e84d4416-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 513)\n", "sample e861e830-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 514)\n", "sample e88c33c4-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 515)\n", "sample e8f16190-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 516)\n", "sample e9056b0e-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 517)\n", "sample e919f7e0-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 518)\n", "sample e92dccac-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 519)\n", "sample e9a9e0d0-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 520)\n", "sample e9bf5a64-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 521)\n", "sample e9ff4638-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 522)\n", "sample ea14401a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 523)\n", "sample ea9f1046-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 524)\n", "sample eb16050c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 525)\n", "sample ec2b4970-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 526)\n", "sample ec44c972-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 527)\n", "sample ecbcc38c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 528)\n", "sample ecef0b94-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 529)\n", "sample ed08827c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 530)\n", "sample ed240aec-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 531)\n", "sample ed6af006-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 532)\n", "sample ed8a0c2a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 533)\n", "sample edaba06a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 534)\n", "sample edd27b40-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 535)\n", "sample ef353f22-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 536)\n", "sample ef8c0802-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 537)\n", "sample f086e876-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 538)\n", "sample f09fdd22-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 539)\n", "sample f1206924-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 540)\n", "sample f152f8a8-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 541)\n", "sample f170d33c-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 542)\n", "sample f18c954a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 543)\n", "sample f246a020-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 544)\n", "sample f260264e-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 545)\n", "sample f29a5382-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 546)\n", "sample f2e7d80a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 547)\n", "sample f3020ef0-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 548)\n", "sample f31c2f92-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 549)\n", "sample f3850166-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 550)\n", "sample f3ab8458-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 551)\n", "sample f40f24ea-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 552)\n", "sample f428c882-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 553)\n", "sample f6c6794a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 554)\n", "sample f6e1f792-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 555)\n", "sample f8f2bfd0-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 556)\n", "sample f903ea8a-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 557)\n", "sample f996a3e8-7ae6-11e9-a106-68b59976a384 no abundance (sample number without abundance: 558)\n", "sample G80284 no abundance (sample number without abundance: 559)\n", "sample G80592 no abundance (sample number without abundance: 560)\n", "sample G78712 no abundance (sample number without abundance: 561)\n", "sample EGAR00001763502_1000IBD00255 no abundance (sample number without abundance: 562)\n", "sample MM046.2 no abundance (sample number without abundance: 563)\n", "sample MM046.3 no abundance (sample number without abundance: 564)\n", "sample MM057.4 no abundance (sample number without abundance: 565)\n", "sample MM057.5 no abundance (sample number without abundance: 566)\n", "sample MM075.1 no abundance (sample number without abundance: 567)\n", "sample MM091.4 no abundance (sample number without abundance: 568)\n", "sample MM098.10 no abundance (sample number without abundance: 569)\n", "sample MM098.13 no abundance (sample number without abundance: 570)\n", "sample MM098.5 no abundance (sample number without abundance: 571)\n", "sample MM098.6 no abundance (sample number without abundance: 572)\n", "sample MM098.9 no abundance (sample number without abundance: 573)\n", "sample V1_C113 no abundance (sample number without abundance: 574)\n", "sample V2_C114 no abundance (sample number without abundance: 575)\n", "sample V1_C115 no abundance (sample number without abundance: 576)\n", "sample V2_C118 no abundance (sample number without abundance: 577)\n", "sample V3_C118 no abundance (sample number without abundance: 578)\n", "sample M_C100 no abundance (sample number without abundance: 579)\n", "sample M_C117 no abundance (sample number without abundance: 580)\n", "sample M_C119 no abundance (sample number without abundance: 581)\n", "sample M_C123 no abundance (sample number without abundance: 582)\n", "sample G102265 no abundance (sample number without abundance: 583)\n", "sample G102232 no abundance (sample number without abundance: 584)\n", "sample G102227 no abundance (sample number without abundance: 585)\n", "sample G102302 no abundance (sample number without abundance: 586)\n", "sample G102320 no abundance (sample number without abundance: 587)\n", "sample G104619 no abundance (sample number without abundance: 588)\n", "sample G104622 no abundance (sample number without abundance: 589)\n", "sample G102205 no abundance (sample number without abundance: 590)\n", "sample G104634 no abundance (sample number without abundance: 591)\n", "sample G104637 no abundance (sample number without abundance: 592)\n", "sample G104633 no abundance (sample number without abundance: 593)\n", "sample G102284 no abundance (sample number without abundance: 594)\n", "sample G104705 no abundance (sample number without abundance: 595)\n", "sample G104677 no abundance (sample number without abundance: 596)\n", "sample G104667 no abundance (sample number without abundance: 597)\n", "sample G102358 no abundance (sample number without abundance: 598)\n", "sample G102385 no abundance (sample number without abundance: 599)\n", "sample G104709 no abundance (sample number without abundance: 600)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tax_identifier</th>\n", "      <th>species</th>\n", "      <th>genus</th>\n", "      <th>family</th>\n", "      <th>order</th>\n", "      <th>class</th>\n", "      <th>phylum</th>\n", "      <th>MV_FEI1_t1Q14</th>\n", "      <th>MV_FEI2_t1Q14</th>\n", "      <th>MV_FEI3_t1Q14</th>\n", "      <th>...</th>\n", "      <th>wHAXPI034926-15</th>\n", "      <th>wHAXPI037144-8</th>\n", "      <th>wHAXPI037145-9</th>\n", "      <th>wHAXPI037146-11</th>\n", "      <th>wHAXPI037147-12</th>\n", "      <th>wHAXPI043592-8</th>\n", "      <th>wHAXPI043593-9</th>\n", "      <th>wHAXPI043594-11</th>\n", "      <th>wHAXPI047830-11</th>\n", "      <th>wHAXPI048670-90</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Escherichia coli</td>\n", "      <td>Escherichia coli</td>\n", "      <td>Escherichia</td>\n", "      <td>Enterobacteriaceae</td>\n", "      <td>Enterobacterales</td>\n", "      <td>Gammaproteobacteria</td>\n", "      <td>Proteobacteria</td>\n", "      <td>59.35010</td>\n", "      <td>0.00000</td>\n", "      <td>85.42333</td>\n", "      <td>...</td>\n", "      <td>0.02811</td>\n", "      <td>0.41802</td>\n", "      <td>3.13657</td>\n", "      <td>1.26867</td>\n", "      <td>2.79116</td>\n", "      <td>0.07465</td>\n", "      <td>0.10713</td>\n", "      <td>0.40001</td>\n", "      <td>0.45324</td>\n", "      <td>6.05846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bifidobacterium bifidum</td>\n", "      <td>Bifidobacterium bifidum</td>\n", "      <td>Bifidobacterium</td>\n", "      <td>Bifidobacteriaceae</td>\n", "      <td>Bifidobacteriales</td>\n", "      <td>Actinomycetia</td>\n", "      <td>Actinobacteria</td>\n", "      <td>16.16243</td>\n", "      <td>5.61882</td>\n", "      <td>0.20192</td>\n", "      <td>...</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>2.37212</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Bifidobacterium longum</td>\n", "      <td>Bifidobacterium longum</td>\n", "      <td>Bifidobacterium</td>\n", "      <td>Bifidobacteriaceae</td>\n", "      <td>Bifidobacteriales</td>\n", "      <td>Actinomycetia</td>\n", "      <td>Actinobacteria</td>\n", "      <td>7.79189</td>\n", "      <td>60.54102</td>\n", "      <td>0.49524</td>\n", "      <td>...</td>\n", "      <td>0.33410</td>\n", "      <td>0.39197</td>\n", "      <td>0.09752</td>\n", "      <td>3.19180</td>\n", "      <td>0.18976</td>\n", "      <td>0.00330</td>\n", "      <td>0.55654</td>\n", "      <td>0.69706</td>\n", "      <td>2.61017</td>\n", "      <td>0.03886</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Enterococcus faecalis</td>\n", "      <td>Enterococcus faecalis</td>\n", "      <td>Enterococcus</td>\n", "      <td>Enterococcaceae</td>\n", "      <td>Lactobacillales</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Firmicutes</td>\n", "      <td>6.60110</td>\n", "      <td>0.04311</td>\n", "      <td>7.44248</td>\n", "      <td>...</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00893</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Ruminococcus gnavus</td>\n", "      <td>Ruminococcus gnavus</td>\n", "      <td>Mediterraneibacter</td>\n", "      <td>Lachnospiraceae</td>\n", "      <td>Eubacteriales</td>\n", "      <td>Clostridia</td>\n", "      <td>Firmicutes</td>\n", "      <td>2.56548</td>\n", "      <td>0.00000</td>\n", "      <td>0.22930</td>\n", "      <td>...</td>\n", "      <td>0.10444</td>\n", "      <td>0.00000</td>\n", "      <td>0.00000</td>\n", "      <td>0.74858</td>\n", "      <td>0.34631</td>\n", "      <td>0.00000</td>\n", "      <td>0.00172</td>\n", "      <td>0.29646</td>\n", "      <td>2.59479</td>\n", "      <td>0.60770</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 18732 columns</p>\n", "</div>"], "text/plain": ["            tax_identifier                  species               genus  \\\n", "0         Escherichia coli         Escherichia coli         Escherichia   \n", "1  Bifidobacterium bifidum  Bifidobacterium bifidum     Bifidobacterium   \n", "2   Bifidobacterium longum   Bifidobacterium longum     Bifidobacterium   \n", "3    Enterococcus faecalis    Enterococcus faecalis        Enterococcus   \n", "4      Ruminococcus gnavus      Ruminococcus gnavus  Mediterraneibacter   \n", "\n", "               family              order                class          phylum  \\\n", "0  Enterobacteriaceae   Enterobacterales  Gammaproteobacteria  Proteobacteria   \n", "1  Bifidobacteriaceae  Bifidobacteriales        Actinomycetia  Actinobacteria   \n", "2  Bifidobacteriaceae  Bifidobacteriales        Actinomycetia  Actinobacteria   \n", "3     Enterococcaceae    Lactobacillales              Bacilli      Firmicutes   \n", "4     Lachnospiraceae      Eubacteriales           Clostridia      Firmicutes   \n", "\n", "   MV_FEI1_t1Q14  MV_FEI2_t1Q14  MV_FEI3_t1Q14  ...  wHAXPI034926-15  \\\n", "0       59.35010        0.00000       85.42333  ...          0.02811   \n", "1       16.16243        5.61882        0.20192  ...          0.00000   \n", "2        7.79189       60.54102        0.49524  ...          0.33410   \n", "3        6.60110        0.04311        7.44248  ...          0.00000   \n", "4        2.56548        0.00000        0.22930  ...          0.10444   \n", "\n", "   wHAXPI037144-8  wHAXPI037145-9  wHAXPI037146-11  wHAXPI037147-12  \\\n", "0         0.41802         3.13657          1.26867          2.79116   \n", "1         0.00000         0.00000          2.37212          0.00000   \n", "2         0.39197         0.09752          3.19180          0.18976   \n", "3         0.00000         0.00893          0.00000          0.00000   \n", "4         0.00000         0.00000          0.74858          0.34631   \n", "\n", "   wHAXPI043592-8  wHAXPI043593-9  wHAXPI043594-11  wHAXPI047830-11  \\\n", "0         0.07465         0.10713          0.40001          0.45324   \n", "1         0.00000         0.00000          0.00000          0.00000   \n", "2         0.00330         0.55654          0.69706          2.61017   \n", "3         0.00000         0.00000          0.00000          0.00000   \n", "4         0.00000         0.00172          0.29646          2.59479   \n", "\n", "   wHAXPI048670-90  \n", "0          6.05846  \n", "1          0.00000  \n", "2          0.03886  \n", "3          0.00000  \n", "4          0.60770  \n", "\n", "[5 rows x 18732 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"ename": "KeyError", "evalue": "\"['[Eubacterium]'] not in index\"", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[5], line 88\u001b[0m\n\u001b[1;32m     86\u001b[0m tC\u001b[38;5;241m=\u001b[39mtC\u001b[38;5;241m+\u001b[39m\u001b[38;5;241m1\u001b[39m\n\u001b[1;32m     87\u001b[0m \u001b[38;5;66;03m#print(relab2_cur.columns)\u001b[39;00m\n\u001b[0;32m---> 88\u001b[0m abundance_cur_phyla\u001b[38;5;241m=\u001b[39m\u001b[43mrelab2_cur\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgroupby\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtaxlevel\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msum\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnumeric_only\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtranspose\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m[\u001b[49m\u001b[43mtaxlevels_names_characterized\u001b[49m\u001b[43m[\u001b[49m\u001b[43mtC\u001b[49m\u001b[43m]\u001b[49m\u001b[43m]\u001b[49m\n\u001b[1;32m     89\u001b[0m abundance_cur_phyla \u001b[38;5;241m=\u001b[39m abundance_cur_phyla\u001b[38;5;241m.\u001b[39madd_prefix(taxlevel\u001b[38;5;241m+\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlevel_\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     91\u001b[0m average_uptake_secretion\u001b[38;5;241m=\u001b[39maverage_uptake_secretion\u001b[38;5;241m.\u001b[39mjoin(abundance_cur_phyla)\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/frame.py:3767\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[0;34m(self, key)\u001b[0m\n\u001b[1;32m   3765\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m is_iterator(key):\n\u001b[1;32m   3766\u001b[0m         key \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(key)\n\u001b[0;32m-> 3767\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_indexer_strict\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcolumns\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m[\u001b[38;5;241m1\u001b[39m]\n\u001b[1;32m   3769\u001b[0m \u001b[38;5;66;03m# take() does not accept boolean indexers\u001b[39;00m\n\u001b[1;32m   3770\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mgetattr\u001b[39m(indexer, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdtype\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m) \u001b[38;5;241m==\u001b[39m \u001b[38;5;28mbool\u001b[39m:\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/indexes/base.py:5877\u001b[0m, in \u001b[0;36mIndex._get_indexer_strict\u001b[0;34m(self, key, axis_name)\u001b[0m\n\u001b[1;32m   5874\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   5875\u001b[0m     keyarr, indexer, new_indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_reindex_non_unique(keyarr)\n\u001b[0;32m-> 5877\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_raise_if_missing\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkeyarr\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mindexer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis_name\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   5879\u001b[0m keyarr \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtake(indexer)\n\u001b[1;32m   5880\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(key, Index):\n\u001b[1;32m   5881\u001b[0m     \u001b[38;5;66;03m# GH 42790 - Preserve name from an Index\u001b[39;00m\n", "File \u001b[0;32m~/mambaforge/envs/ete/lib/python3.9/site-packages/pandas/core/indexes/base.py:5941\u001b[0m, in \u001b[0;36mIndex._raise_if_missing\u001b[0;34m(self, key, indexer, axis_name)\u001b[0m\n\u001b[1;32m   5938\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNone of [\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mkey\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m] are in the [\u001b[39m\u001b[38;5;132;01m{\u001b[39;00maxis_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m]\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m   5940\u001b[0m not_found \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(ensure_index(key)[missing_mask\u001b[38;5;241m.\u001b[39mnonzero()[\u001b[38;5;241m0\u001b[39m]]\u001b[38;5;241m.\u001b[39munique())\n\u001b[0;32m-> 5941\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnot_found\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m not in index\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31m<PERSON>eyError\u001b[0m: \"['[Eubacterium]'] not in index\""]}], "source": ["body_site=\"stool\"\n", "#select samples belonging to specific study / metadata\n", "select=colnames.loc[(colnames['body_site']=='stool')]# & (colnames['study_name']==study_name)  ]\n", "    \n", "display(\"number samples included:\")\n", "display(select.shape[0])\n", "select_samples=select['sample'].tolist()\n", "#get abundance of experimentally characterized species, list of 16 entri4s\n", "abundance_cur_exp=relab2_exp[[\"tax_identifier\"]+select_samples]\n", "    \n", "#get which biomass characterized species represent in the different samples\n", "biomass_represented_curexp=abundance_cur_exp.sum()\n", "display(biomass_represented_curexp)\n", "\n", "#calculations secretion\n", "average_uptake_secretion=pd.DataFrame(0, index=select_samples, columns=sublist)\n", "average_uptake_secretion.loc[:] = np.nan\n", "samplecount=-1\n", "noabundancecount=0\n", "for sample in select_samples:\n", "        samplecount=samplecount+1\n", "        abundancec=abundance_cur_exp[[\"tax_identifier\",sample]]\n", "        abundancec_sum=abundancec.sum().iloc[1]\n", "        average_uptake_secretion.at[sample,\"BM_represented\"]=abundancec_sum\n", "        \n", "        avsec_ut=0\n", "        avsec_sec=0\n", "        avsec_energy=0\n", "        avsec=[0]*len(sublist)\n", "        for index,row in abundancec.iterrows(): #go through each characterized strain\n", "            #determine which species\n", "            curspecinfo=speciesinformation.loc[speciesinformation[\"species_x\"]==row[\"tax_identifier\"]]\n", "            if curspecinfo.shape[0]>1:\n", "                print(curspecinfo)\n", "                error\n", "            curspecinfo=curspecinfo.iloc[0]\n", "            #go through every secretion\n", "            \n", "            \n", "            scc=-1\n", "            for sub in sublist:\n", "                scc=scc+1\n", "                avsec[scc]=avsec[scc]+curspecinfo[sub]*row[sample]\n", "                \n", "                if sub ==\"glucose\":\n", "                    avsec_ut=avsec_ut-curspecinfo[sub]*row[sample]\n", "                elif sub ==\"maltose\":\n", "                    avsec_ut=avsec_ut-2*curspecinfo[sub]*row[sample]\n", "                else:\n", "                    avsec_sec=avsec_sec+curspecinfo[sub]*row[sample]\n", "                    avsec_energy=avsec_energy+curspecinfo[sub]*row[sample]*sublist_energy[scc]\n", "        \n", "        #norm results by dividing by abundance of all characterized strains. \n", "        \n", "        if abundancec_sum>0:\n", "            scc=-1\n", "            for sub in sublist:\n", "                scc=scc+1\n", "                avsec[scc]=avsec[scc]/conversionfactorOD/abundancec_sum #normalizing by sum and convertion to OD\n", "                average_uptake_secretion.at[sample,sub]=avsec[scc]\n", "\n", "\n", "            average_uptake_secretion.at[sample,\"uptake\"]=avsec_ut/conversionfactorOD/abundancec_sum\n", "            average_uptake_secretion.at[sample,\"total_secretion\"]=avsec_sec/conversionfactorOD/abundancec_sum\n", "            average_uptake_secretion.at[sample,\"total_secretion_energy\"]=avsec_energy/conversionfactorOD/abundancec_sum\n", "            #calculate bacterial dry mass for british reference diet\n", "            #see also FPcalc for calculation. the 0.18015 converts glucose equivalents to gr. \n", "            average_uptake_secretion.at[sample,\"dryweightBRD\"]=BRD[\"carbLI_standard\"]/0.18015/average_uptake_secretion.at[sample,\"uptake\"]\n", "        else:\n", "            noabundancecount=noabundancecount+1\n", "            print(\"sample \"+sample+\" no abundance (sample number without abundance: \"+str(noabundancecount)+\")\")\n", "            \n", "\n", "##############################\n", "#add phyla abundance to table\n", "##############################\n", "\n", "#get abundance information for these samples\n", "relab2_cur=relab2[[\"tax_identifier\"]+taxlevels+average_uptake_secretion.index.tolist()]\n", "display(relab2_cur.head())\n", "\n", "#group by taxonomic level, sume over abundance, and only take taxonomic entities which are represented by characterized strains\n", "#phylanames=['Bacteroidetes','Firmicutes','Actinobacteria','Proteobacteria']\n", "tC=-1\n", "for taxlevel in taxlevels:\n", "    tC=tC+1\n", "    #print(relab2_cur.columns)\n", "    abundance_cur_phyla=relab2_cur.groupby(taxlevel).sum(numeric_only=True).transpose()[taxlevels_names_characterized[tC]]\n", "    abundance_cur_phyla = abundance_cur_phyla.add_prefix(taxlevel+\"level_\")\n", "    \n", "    average_uptake_secretion=average_uptake_secretion.join(abundance_cur_phyla)\n", "display(average_uptake_secretion.head())\n", "display(average_uptake_secretion.columns)\n", "\n", "\n", "##############################\n", "#calculate excretion on different taxonomic levels\n", "##############################\n", "samplecount=-1\n", "for sample in select_samples:\n", "    \n", "    #    speciesinformation_taxonomiclevel=[]\n", "#taxlevels=[\"genus\",\"family\",\"order\",\"class\",\"phylum\"]\n", "\n", "#taxlevels_names=[]\n", "#for taxlevel in taxlevels:\n", "#    speciesinformation_taxonomiclevel.append(speciesinformation.groupby(taxlevel).mean())\n", "#    print(taxlevel)\n", "#    display(speciesinformation_taxonomiclevel[-1])\n", "#    print(speciesinformation_taxonomiclevel[-1].shape)\n", "#    taxlevels_names_characterized.append(speciesinformation_taxonomiclevel[-1].index)\n", "        samplecount=samplecount+1   \n", "        tC=-1\n", "        for taxlevel in taxlevels:\n", "            tC=tC+1\n", "            avsec_ut=0\n", "            avsec_sec=0\n", "            avsec_energy=0\n", "            avsec=[0]*len(sublist)\n", "            avphylumsum=0\n", "            for taxanamec in taxlevels_names_characterized[tC]:\n", "                taxanamec_withprefix=taxlevel+\"level_\"+taxanamec\n", "                \n", "                avphylumsum=avphylumsum+average_uptake_secretion.at[sample,taxanamec_withprefix]\n", "                scc=-1\n", "                for sub in sublist:\n", "                    scc=scc+1\n", "                    avsec[scc]=avsec[scc]+average_uptake_secretion.at[sample,taxanamec_withprefix]*speciesinformation_taxonomiclevel[tC].at[taxanamec,sub]\n", "                    if sub ==\"glucose\":\n", "                        avsec_ut=avsec_ut-average_uptake_secretion.at[sample,taxanamec_withprefix]*speciesinformation_taxonomiclevel[tC].at[taxanamec,sub]\n", "                    elif sub ==\"maltose\":\n", "                        avsec_ut=avsec_ut-2*average_uptake_secretion.at[sample,taxanamec_withprefix]*speciesinformation_taxonomiclevel[tC].at[taxanamec,sub]\n", "                    else:\n", "                        avsec_sec=avsec_sec+average_uptake_secretion.at[sample,taxanamec_withprefix]*speciesinformation_taxonomiclevel[tC].at[taxanamec,sub]\n", "                        avsec_energy=avsec_energy+average_uptake_secretion.at[sample,taxanamec_withprefix]*speciesinformation_taxonomiclevel[tC].at[taxanamec,sub]*sublist_energy[scc]\n", "\n", "            scc=-1\n", "            for sub in sublist:\n", "                scc=scc+1\n", "                avsec[scc]=avsec[scc]/conversionfactorOD/avphylumsum #normalizing by sum and convertion to OD\n", "                average_uptake_secretion.at[sample,taxlevel+\"level_\"+sub]=avsec[scc]\n", "            average_uptake_secretion.at[sample,taxlevel+\"level_BM_represented\"]=avphylumsum\n", "            \n", "            average_uptake_secretion.at[sample,taxlevel+\"level_uptake\"]=avsec_ut/conversionfactorOD/avphylumsum\n", "            average_uptake_secretion.at[sample,taxlevel+\"level_total_secretion\"]=avsec_sec/conversionfactorOD/avphylumsum\n", "            average_uptake_secretion.at[sample,taxlevel+\"level_total_secretion_energy\"]=avsec_energy/conversionfactorOD/avphylumsum\n", "            #calculate bacterial dry mass for british reference diet\n", "            #see also FPcalc for calculation. the 0.18015 converts glucose equivalents to gr. \n", "            average_uptake_secretion.at[sample,taxlevel+\"level_dryweightBRD\"]=BRD[\"carbLI_standard\"]/0.18015/average_uptake_secretion.at[sample,taxlevel+\"level_uptake\"]\n", "\n", "\n", "display(average_uptake_secretion.head())\n", "\n", "#sort\n", "\n", "\n", "average_uptake_secretion.sort_values(by=['phylumlevel_Bacteroidetes'],inplace=True)\n", "\n", "average_uptake_secretion.to_csv(\"data_analysisresults/secretion_microbiomestudies.csv\")\n", "\n", "\n", "\n", "#average to dict\n", "display(average_uptake_secretion.mean(axis=0))\n", "dict_out=average_uptake_secretion.mean(axis=0).to_dict()\n", "print(\"results stored to dictionary\")\n", "print(dict_out)\n", "\n", "with open(\"data_analysisresults/average_excretion/av_allmicrobiomesamples.json\", 'w') as fp:\n", "    json.dump(dict_out, fp)\n"]}, {"cell_type": "markdown", "id": "57e10159-8f97-4580-bde5-f94d40282522", "metadata": {}, "source": ["# Save simpler table for interactive analysis (reduced content to minimize size)\n"]}, {"cell_type": "code", "execution_count": null, "id": "9861ec71-4fa5-4961-ab5c-ff3f07f8711e", "metadata": {}, "outputs": [], "source": ["\n", "#print(average_uptake_secretion.columns.tolist())\n", "#select which columns from abundance data to include\n", "col_data=['familylevel_glucose', 'familylevel_maltose', 'familylevel_acetate', 'familylevel_butyrate', 'familylevel_formate', 'familylevel_lactate', 'familylevel_propionate', 'familylevel_succinate', 'familylevel_BM_represented', 'familylevel_uptake',  'familylevel_total_secretion_energy' ]\n", "#print(colnames.columns.tolist())\n", "#select which information in metadatatable to include\n", "col_metadata=['study_name','antibiotics_current_use','disease', 'age', 'infant_age', 'age_category', 'gender', 'country', 'non_westernized',  'pregnant', 'lactating', 'curator', 'BMI', 'family']\n", "colnames2=colnames.set_index(\"sample\")\n", "\n", "display(colnames)\n", "combined_table=average_uptake_secretion[col_data].join(colnames2[col_metadata])\n", "combined_table.to_csv(\"data_analysisresults/resultstable_website.csv\")\n", "#combined_table.to_csv(\"data_analysisresults/resultstable_website.csv\", float_format='%.3f')\n", "#display(combined_table)\n", "#'days_from_first_collection', 'family_role', 'born_method', 'feeding_practice', 'travel_destination', 'location', 'visit_number', 'premature', 'birth_weight', 'gestational_age',               \n", "  #            'antibiotics_family', 'disease_subtype', 'days_after_onset', 'creatine', 'albumine', 'hscrp', 'ESR', 'treatment', 'ast', 'alt', 'globulin', 'urea_nitrogen', 'BASDAI', 'BASFI', 'alcohol', 'flg_genotype', 'population', 'menopausal_status', 'lifestyle', 'body_subsite', 'diet', 'tnm', 'triglycerides', 'hdl', 'ldl', 'hba1c', 'smoker', 'ever_smoker', 'dental_sample_type', 'history_of_periodontitis', 'PPD_M', 'PPD_B', 'PPD_D', 'PPD_L', 'fobt', 'disease_stage', 'disease_location', 'calprotectin', 'HBI', 'SCCAI', 'uncurated_metadata', 'mumps', 'cholesterol', 'c_peptide', 'glucose', 'creatinine', 'bilubirin', 'prothrombin_time', 'wbc', 'rbc', 'hemoglobinometry', 'remission', 'dyastolic_p', 'systolic_p', 'insulin_cat', 'adiponectin', 'glp_1', 'cd163', 'il_1', 'leptin', 'fgf_19', 'glutamate_decarboxylase_2_antibody', 'HLA', 'autoantibody_positive', 'age_seroconversion', 'age_T1D_diagnosis', 'hitchip_probe_class', 'fasting_insulin', 'fasting_glucose', 'protein_intake', 'stec_count', 'shigatoxin_2_elisa', 'stool_texture', 'anti_PD_1', 'mgs_richness', 'ferm_milk_prod_consumer', 'inr', 'ctp', 'birth_control_pil', 'c_section_type', 'ajcc', 'hla_drb12', 'hla_dqa12', 'hla_dqa11', 'hla_drb11', 'birth_order', 'age_twins_started_to_live_apart', 'zigosity', 'brinkman_index', 'alcohol_numeric', 'breastfeeding_duration', 'formula_first_day', 'ALT', 'eGFR']\n", "#, 'subject_id', 'body_site', \n", "      "]}, {"cell_type": "code", "execution_count": null, "id": "c688e109-8551-4d86-bcae-a11d12c2d0e1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7d2707f8-666f-404e-a1b8-5572a9729e16", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "67213ad3-e585-4166-b305-ae0b60d5c7d8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}