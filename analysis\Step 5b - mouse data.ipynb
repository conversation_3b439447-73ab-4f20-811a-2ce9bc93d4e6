{"cells": [{"cell_type": "markdown", "metadata": {"tags": []}, "source": ["# Considerations of fecal mass and energy contents in mice\n", "\n", "- Based on the data presented in <PERSON><PERSON> et al. \n", "<PERSON><PERSON> D,<PERSON>n<PERSON>,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON> ̈ubli M<PERSON>,Cappio Barazzone E,etal.(2022) Metabolic reconstitution ofgerm-free mice byagnotobiotic microbiota varies overthecircadian cycle. PLoS Biol20(9): e3001743. https://doi.org/10.1371/ journal.pbio.3001743\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'fecaldryweight_GF': 1.5499999999999998, 'fecaldryweightSTD_GF': 0.2685652113123975, 'energyinfood_GF': 62.7664852, 'energyinfoodSTD_GF': 8.949038238171195, 'energydensityinfeces_GF': 16.14426285714286, 'energydensityinfecesSTD_GF': 0.1950326235567492, 'energyinfeces_GF': 25.023607428571427, 'energyinfecesstd_GF': 4.6380879322244235, 'energytohost_GF': 37.74287777142857, 'energyextraction_GF': 0.6013221490922128, 'energyextractionSTD_GF': 0.08573452677075334, 'fecaldryweight_SPF': 0.815, 'fecaldryweightSTD_SPF': 0.08812869377601525, 'energyinfood_SPF': 54.46630784, 'energyinfoodSTD_SPF': 8.54861700280201, 'energydensityinfeces_SPF': 16.64471272727273, 'energydensityinfecesSTD_SPF': 0.37273695682319746, 'energyinfeces_SPF': 13.565440872727274, 'energyinfecesstd_SPF': 1.7706574107424677, 'energytohost_SPF': 40.90086696727273, 'energyextraction_SPF': 0.7509388572367149, 'energyextractionSTD_SPF': 0.11786164580673167, 'fecaldryweight_Oligo': 1.205, 'fecaldryweightSTD_Oligo': 0.16291978745031907, 'energyinfood_Oligo': 54.25287098947369, 'energyinfoodSTD_Oligo': 8.005328988692762, 'energydensityinfeces_Oligo': 16.74741090909091, 'energydensityinfecesSTD_Oligo': 0.21341755885842845, 'energyinfeces_Oligo': 20.180630145454547, 'energyinfecesstd_Oligo': 2.985652784076652, 'energytohost_Oligo': 34.07224084401915, 'energyextraction_Oligo': 0.628026502977694, 'energyextractionSTD_Oligo': 0.09266899019832771, 'energy_from_microbiota': 8.149069683805887, 'energy_from_microbiotaSTD': 1.755491634351854, 'FP_production_via_feces': 12.92647998477458, 'energy_from_microbiota_via_feces': 11.872668762990058, 'energy_from_microbiota_via_fecesSTD': 1.2838316438252917, 'FP_production_via_fecesSTD': 1.3977837989938517, 'dailyenergyexp_SPF': 37.999088, 'dailyenergyexpSTD_SPF': 2.3404484715769036, 'energyfraction_from_microbiota': 0.21445434910979672, 'energyfractionSTD_from_microbiota': 0.04804944603713973, 'energyfraction_from_microbiota_via_feces': 0.31244615036524187, 'energyfraction_from_microbiota_via_fecesSTD': 0.03378585411905916}\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import matplotlib\n", "matplotlib.rcParams['pdf.fonttype'] = 42\n", "matplotlib.rcParams['ps.fonttype'] = 42\n", "from matplotlib.ticker import PercentFormatter\n", "import scipy\n", "\n", "import json\n", "import os\n", "\n", "capsize=6\n", "#set colorscheme\n", "colorNHANES='blue'\n", "colorav='k'\n", "colorrefdiet='purple'\n", "colorHadza='green'\n", "colorMouse='gray'\n", "colorBK='#fb8072' #color for Burkitt data\n", "colorBK2='gray' #color for Burkitt data\n", "colorlistferm=['#1b9e77','#d95f02','#7570b3','#e7298a','#66a61e']\n", "\n", "labelenergybac=\"energy supply via bacteria (kcal)\"\n", "labelenergybacfrac=\"enery supply via bacteria (%)\"\n", "labelenergybacfracnounit=\"energy supply via bacteria\"\n", "\n", "#load data hoces\n", "\n", "\n", "#load file if already exists, otherwise start with empty dict\n", "try:\n", "    with open('data_analysisresults/mouse_characteristics.json', 'r') as fp:\n", "        MOUSE = json.load(fp)\n", "except:\n", "    MOUSE={}\n", "print(MOUSE)\n", "import FPcalc #basic calculations of \n", "\n", "\n", "\n", "####dict for diet data\n", "diet_analysis_results={}\n", "\n", "#############################\n", "#yield data to use as standard\n", "dict_yielddata=\"data_analysisresults/average_excretion/selectedsamples_HMP_2019_ibdmdb_disease_healthy_age_adultsenior_NW_no_genus.json\"\n", "#############################\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Load data"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>GF_lean_mass</th>\n", "      <th>GF_energy_exp</th>\n", "      <th>Oligo_lean_mass</th>\n", "      <th>Oligo_energy_exp</th>\n", "      <th>SPF_lean_mass</th>\n", "      <th>SPF_energy_exp</th>\n", "      <th>GF_lean_mass_DP</th>\n", "      <th>GF_energy_exp_DP</th>\n", "      <th>Oligo_lean_mass_DP</th>\n", "      <th>Oligo_energy_exp_DP</th>\n", "      <th>...</th>\n", "      <th>SPF_dailyenergyexp</th>\n", "      <th>GF_foodintake</th>\n", "      <th>Oligo_foodintake</th>\n", "      <th>SPF_foodintake</th>\n", "      <th>GF_fecaldrymass</th>\n", "      <th>Oligo_fecaldrymass</th>\n", "      <th>SPF_fecaldrymass</th>\n", "      <th>GF_energyinfeces</th>\n", "      <th>Oligo_energyinfeces</th>\n", "      <th>SPF_energyinfeces</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>19.79</td>\n", "      <td>3.69</td>\n", "      <td>22.86</td>\n", "      <td>3.91</td>\n", "      <td>22.09</td>\n", "      <td>3.73</td>\n", "      <td>19.79</td>\n", "      <td>4.85</td>\n", "      <td>22.86</td>\n", "      <td>4.99</td>\n", "      <td>...</td>\n", "      <td>9.04</td>\n", "      <td>2.57</td>\n", "      <td>2.47</td>\n", "      <td>2.57</td>\n", "      <td>1.93</td>\n", "      <td>1.02</td>\n", "      <td>0.71</td>\n", "      <td>3.82</td>\n", "      <td>3.95</td>\n", "      <td>3.95</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21.07</td>\n", "      <td>3.41</td>\n", "      <td>21.40</td>\n", "      <td>3.82</td>\n", "      <td>22.50</td>\n", "      <td>4.10</td>\n", "      <td>21.07</td>\n", "      <td>4.64</td>\n", "      <td>21.40</td>\n", "      <td>5.05</td>\n", "      <td>...</td>\n", "      <td>9.89</td>\n", "      <td>2.93</td>\n", "      <td>2.50</td>\n", "      <td>2.94</td>\n", "      <td>1.90</td>\n", "      <td>1.32</td>\n", "      <td>0.86</td>\n", "      <td>3.81</td>\n", "      <td>3.95</td>\n", "      <td>3.89</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>20.69</td>\n", "      <td>3.41</td>\n", "      <td>24.30</td>\n", "      <td>4.05</td>\n", "      <td>22.09</td>\n", "      <td>3.75</td>\n", "      <td>20.69</td>\n", "      <td>4.41</td>\n", "      <td>24.30</td>\n", "      <td>4.86</td>\n", "      <td>...</td>\n", "      <td>8.69</td>\n", "      <td>2.94</td>\n", "      <td>2.52</td>\n", "      <td>3.36</td>\n", "      <td>1.25</td>\n", "      <td>1.06</td>\n", "      <td>0.91</td>\n", "      <td>3.83</td>\n", "      <td>4.07</td>\n", "      <td>3.98</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>20.44</td>\n", "      <td>3.66</td>\n", "      <td>22.09</td>\n", "      <td>3.67</td>\n", "      <td>23.44</td>\n", "      <td>3.58</td>\n", "      <td>20.44</td>\n", "      <td>4.72</td>\n", "      <td>22.09</td>\n", "      <td>4.64</td>\n", "      <td>...</td>\n", "      <td>8.22</td>\n", "      <td>2.98</td>\n", "      <td>2.81</td>\n", "      <td>3.65</td>\n", "      <td>1.57</td>\n", "      <td>1.41</td>\n", "      <td>0.78</td>\n", "      <td>3.83</td>\n", "      <td>4.02</td>\n", "      <td>3.92</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>23.93</td>\n", "      <td>3.93</td>\n", "      <td>21.66</td>\n", "      <td>3.85</td>\n", "      <td>20.08</td>\n", "      <td>3.95</td>\n", "      <td>23.93</td>\n", "      <td>5.21</td>\n", "      <td>21.66</td>\n", "      <td>4.86</td>\n", "      <td>...</td>\n", "      <td>9.52</td>\n", "      <td>3.36</td>\n", "      <td>2.89</td>\n", "      <td>2.95</td>\n", "      <td>1.70</td>\n", "      <td>1.01</td>\n", "      <td>NaN</td>\n", "      <td>3.86</td>\n", "      <td>3.99</td>\n", "      <td>4.01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>20.73</td>\n", "      <td>3.22</td>\n", "      <td>20.82</td>\n", "      <td>3.69</td>\n", "      <td>21.97</td>\n", "      <td>4.13</td>\n", "      <td>20.73</td>\n", "      <td>4.52</td>\n", "      <td>20.82</td>\n", "      <td>4.89</td>\n", "      <td>...</td>\n", "      <td>9.38</td>\n", "      <td>3.55</td>\n", "      <td>2.92</td>\n", "      <td>2.96</td>\n", "      <td>1.12</td>\n", "      <td>1.32</td>\n", "      <td>NaN</td>\n", "      <td>3.96</td>\n", "      <td>3.93</td>\n", "      <td>3.85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>20.28</td>\n", "      <td>4.17</td>\n", "      <td>20.87</td>\n", "      <td>3.63</td>\n", "      <td>21.94</td>\n", "      <td>3.85</td>\n", "      <td>20.28</td>\n", "      <td>4.85</td>\n", "      <td>20.87</td>\n", "      <td>4.66</td>\n", "      <td>...</td>\n", "      <td>8.80</td>\n", "      <td>3.63</td>\n", "      <td>3.05</td>\n", "      <td>3.12</td>\n", "      <td>1.55</td>\n", "      <td>1.35</td>\n", "      <td>NaN</td>\n", "      <td>3.81</td>\n", "      <td>4.01</td>\n", "      <td>3.87</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>21.32</td>\n", "      <td>4.19</td>\n", "      <td>21.63</td>\n", "      <td>3.56</td>\n", "      <td>19.77</td>\n", "      <td>3.43</td>\n", "      <td>21.32</td>\n", "      <td>5.00</td>\n", "      <td>21.63</td>\n", "      <td>4.76</td>\n", "      <td>...</td>\n", "      <td>8.32</td>\n", "      <td>3.69</td>\n", "      <td>3.30</td>\n", "      <td>3.26</td>\n", "      <td>1.42</td>\n", "      <td>1.15</td>\n", "      <td>NaN</td>\n", "      <td>3.83</td>\n", "      <td>4.01</td>\n", "      <td>4.04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>19.93</td>\n", "      <td>3.70</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>24.69</td>\n", "      <td>4.03</td>\n", "      <td>19.93</td>\n", "      <td>4.53</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>9.42</td>\n", "      <td>3.70</td>\n", "      <td>3.38</td>\n", "      <td>3.99</td>\n", "      <td>1.33</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.91</td>\n", "      <td>3.97</td>\n", "      <td>4.06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>22.11</td>\n", "      <td>4.03</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>9.54</td>\n", "      <td>3.71</td>\n", "      <td>3.50</td>\n", "      <td>4.24</td>\n", "      <td>1.70</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.88</td>\n", "      <td>4.04</td>\n", "      <td>4.08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>3.79</td>\n", "      <td>3.53</td>\n", "      <td>NaN</td>\n", "      <td>1.83</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.84</td>\n", "      <td>4.09</td>\n", "      <td>4.11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>3.80</td>\n", "      <td>3.55</td>\n", "      <td>NaN</td>\n", "      <td>1.30</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.80</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>3.94</td>\n", "      <td>3.57</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.93</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>3.96</td>\n", "      <td>3.58</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.83</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>3.96</td>\n", "      <td>3.63</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.91</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>4.09</td>\n", "      <td>3.69</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.84</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>4.10</td>\n", "      <td>3.72</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.87</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>4.17</td>\n", "      <td>3.77</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.87</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>4.22</td>\n", "      <td>4.15</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.82</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>4.28</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.84</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>4.41</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.94</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>4.46</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>4.56</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>4.58</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>24 rows × 24 columns</p>\n", "</div>"], "text/plain": ["    GF_lean_mass  GF_energy_exp  Oligo_lean_mass  Oligo_energy_exp  \\\n", "0          19.79           3.69            22.86              3.91   \n", "1          21.07           3.41            21.40              3.82   \n", "2          20.69           3.41            24.30              4.05   \n", "3          20.44           3.66            22.09              3.67   \n", "4          23.93           3.93            21.66              3.85   \n", "5          20.73           3.22            20.82              3.69   \n", "6          20.28           4.17            20.87              3.63   \n", "7          21.32           4.19            21.63              3.56   \n", "8          19.93           3.70              NaN               NaN   \n", "9            NaN            NaN              NaN               NaN   \n", "10           NaN            NaN              NaN               NaN   \n", "11           NaN            NaN              NaN               NaN   \n", "12           NaN            NaN              NaN               NaN   \n", "13           NaN            NaN              NaN               NaN   \n", "14           NaN            NaN              NaN               NaN   \n", "15           NaN            NaN              NaN               NaN   \n", "16           NaN            NaN              NaN               NaN   \n", "17           NaN            NaN              NaN               NaN   \n", "18           NaN            NaN              NaN               NaN   \n", "19           NaN            NaN              NaN               NaN   \n", "20           NaN            NaN              NaN               NaN   \n", "21           NaN            NaN              NaN               NaN   \n", "22           NaN            NaN              NaN               NaN   \n", "23           NaN            NaN              NaN               NaN   \n", "\n", "    SPF_lean_mass  SPF_energy_exp  GF_lean_mass_DP  GF_energy_exp_DP  \\\n", "0           22.09            3.73            19.79              4.85   \n", "1           22.50            4.10            21.07              4.64   \n", "2           22.09            3.75            20.69              4.41   \n", "3           23.44            3.58            20.44              4.72   \n", "4           20.08            3.95            23.93              5.21   \n", "5           21.97            4.13            20.73              4.52   \n", "6           21.94            3.85            20.28              4.85   \n", "7           19.77            3.43            21.32              5.00   \n", "8           24.69            4.03            19.93              4.53   \n", "9           22.11            4.03              NaN               NaN   \n", "10            NaN             NaN              NaN               NaN   \n", "11            NaN             NaN              NaN               NaN   \n", "12            NaN             NaN              NaN               NaN   \n", "13            NaN             NaN              NaN               NaN   \n", "14            NaN             NaN              NaN               NaN   \n", "15            NaN             NaN              NaN               NaN   \n", "16            NaN             NaN              NaN               NaN   \n", "17            NaN             NaN              NaN               NaN   \n", "18            NaN             NaN              NaN               NaN   \n", "19            NaN             NaN              NaN               NaN   \n", "20            NaN             NaN              NaN               NaN   \n", "21            NaN             NaN              NaN               NaN   \n", "22            NaN             NaN              NaN               NaN   \n", "23            NaN             NaN              NaN               NaN   \n", "\n", "    Oligo_lean_mass_DP  Oligo_energy_exp_DP  ...  SPF_dailyenergyexp  \\\n", "0                22.86                 4.99  ...                9.04   \n", "1                21.40                 5.05  ...                9.89   \n", "2                24.30                 4.86  ...                8.69   \n", "3                22.09                 4.64  ...                8.22   \n", "4                21.66                 4.86  ...                9.52   \n", "5                20.82                 4.89  ...                9.38   \n", "6                20.87                 4.66  ...                8.80   \n", "7                21.63                 4.76  ...                8.32   \n", "8                  NaN                  NaN  ...                9.42   \n", "9                  NaN                  NaN  ...                9.54   \n", "10                 NaN                  NaN  ...                 NaN   \n", "11                 NaN                  NaN  ...                 NaN   \n", "12                 NaN                  NaN  ...                 NaN   \n", "13                 NaN                  NaN  ...                 NaN   \n", "14                 NaN                  NaN  ...                 NaN   \n", "15                 NaN                  NaN  ...                 NaN   \n", "16                 NaN                  NaN  ...                 NaN   \n", "17                 NaN                  NaN  ...                 NaN   \n", "18                 NaN                  NaN  ...                 NaN   \n", "19                 NaN                  NaN  ...                 NaN   \n", "20                 NaN                  NaN  ...                 NaN   \n", "21                 NaN                  NaN  ...                 NaN   \n", "22                 NaN                  NaN  ...                 NaN   \n", "23                 NaN                  NaN  ...                 NaN   \n", "\n", "    GF_foodintake  Oligo_foodintake  SPF_foodintake  GF_fecaldrymass  \\\n", "0            2.57              2.47            2.57             1.93   \n", "1            2.93              2.50            2.94             1.90   \n", "2            2.94              2.52            3.36             1.25   \n", "3            2.98              2.81            3.65             1.57   \n", "4            3.36              2.89            2.95             1.70   \n", "5            3.55              2.92            2.96             1.12   \n", "6            3.63              3.05            3.12             1.55   \n", "7            3.69              3.30            3.26             1.42   \n", "8            3.70              3.38            3.99             1.33   \n", "9            3.71              3.50            4.24             1.70   \n", "10           3.79              3.53             NaN             1.83   \n", "11           3.80              3.55             NaN             1.30   \n", "12           3.94              3.57             NaN              NaN   \n", "13           3.96              3.58             NaN              NaN   \n", "14           3.96              3.63             NaN              NaN   \n", "15           4.09              3.69             NaN              NaN   \n", "16           4.10              3.72             NaN              NaN   \n", "17           4.17              3.77             NaN              NaN   \n", "18           4.22              4.15             NaN              NaN   \n", "19           4.28               NaN             NaN              NaN   \n", "20           4.41               NaN             NaN              NaN   \n", "21           4.46               NaN             NaN              NaN   \n", "22           4.56               NaN             NaN              NaN   \n", "23           4.58               NaN             NaN              NaN   \n", "\n", "    Oligo_fecaldrymass  SPF_fecaldrymass  GF_energyinfeces  \\\n", "0                 1.02              0.71              3.82   \n", "1                 1.32              0.86              3.81   \n", "2                 1.06              0.91              3.83   \n", "3                 1.41              0.78              3.83   \n", "4                 1.01               NaN              3.86   \n", "5                 1.32               NaN              3.96   \n", "6                 1.35               NaN              3.81   \n", "7                 1.15               NaN              3.83   \n", "8                  NaN               NaN              3.91   \n", "9                  NaN               NaN              3.88   \n", "10                 NaN               NaN              3.84   \n", "11                 NaN               NaN              3.80   \n", "12                 NaN               NaN              3.93   \n", "13                 NaN               NaN              3.83   \n", "14                 NaN               NaN              3.91   \n", "15                 NaN               NaN              3.84   \n", "16                 NaN               NaN              3.87   \n", "17                 NaN               NaN              3.87   \n", "18                 NaN               NaN              3.82   \n", "19                 NaN               NaN              3.84   \n", "20                 NaN               NaN              3.94   \n", "21                 NaN               NaN               NaN   \n", "22                 NaN               NaN               NaN   \n", "23                 NaN               NaN               NaN   \n", "\n", "    Oligo_energyinfeces  SPF_energyinfeces  \n", "0                  3.95               3.95  \n", "1                  3.95               3.89  \n", "2                  4.07               3.98  \n", "3                  4.02               3.92  \n", "4                  3.99               4.01  \n", "5                  3.93               3.85  \n", "6                  4.01               3.87  \n", "7                  4.01               4.04  \n", "8                  3.97               4.06  \n", "9                  4.04               4.08  \n", "10                 4.09               4.11  \n", "11                  NaN                NaN  \n", "12                  NaN                NaN  \n", "13                  NaN                NaN  \n", "14                  NaN                NaN  \n", "15                  NaN                NaN  \n", "16                  NaN                NaN  \n", "17                  NaN                NaN  \n", "18                  NaN                NaN  \n", "19                  NaN                NaN  \n", "20                  NaN                NaN  \n", "21                  NaN                NaN  \n", "22                  NaN                NaN  \n", "23                  NaN                NaN  \n", "\n", "[24 rows x 24 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Index(['GF_lean_mass', 'GF_energy_exp', 'Oligo_lean_mass', 'Oligo_energy_exp',\n", "       'SPF_lean_mass', 'SPF_energy_exp', 'GF_lean_mass_DP',\n", "       'GF_energy_exp_DP', 'Oligo_lean_mass_DP', 'Oligo_energy_exp_DP',\n", "       'SPF_lean_mass_DP', 'SPF_energy_exp_DP', 'GF_dailyenergyexp',\n", "       'Oligo_dailyenergyexp', 'SPF_dailyenergyexp', 'GF_foodintake',\n", "       'Oligo_foodintake', 'SPF_foodintake', 'GF_fecaldrymass',\n", "       'Oligo_fecaldrymass', 'SPF_fecaldrymass', 'GF_energyinfeces',\n", "       'Oligo_energyinfeces', 'SPF_energyinfeces'],\n", "      dtype='object')\n"]}, {"data": {"text/plain": ["37.962759999999996"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "\n", "data=pd.read_csv(\"data/hoces_energymeasurements.csv\",skiprows=1)\n", "display(data)\n", "print(data.columns)\n", "\n", "\n", "#calculate energy in food\n", "display(data[\"SPF_dailyenergyexp\"].mean()*4.18)\n", "#\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Looking at age distribution of participants"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["energy from microbiota via GF SPF comparison - mean\n", "8.149069683805887\n", "energy from microbiota via GF SPF comparison- variation based on food intake\n", "0      6.116038\n", "1      6.190322\n", "2      6.239844\n", "3      6.957921\n", "4      7.156012\n", "5      7.230296\n", "6      7.552192\n", "7      8.171225\n", "8      8.369315\n", "9      8.666450\n", "10     8.740734\n", "11     8.790257\n", "12     8.839779\n", "13     8.864541\n", "14     8.988347\n", "15     9.136915\n", "16     9.211199\n", "17     9.335005\n", "18    10.275934\n", "19          NaN\n", "20          NaN\n", "21          NaN\n", "22          NaN\n", "23          NaN\n", "Name: Oligo_foodintake, dtype: float64\n", "FP_production_via_feces 11.770536866264122\n", "FP_production_via_fecesSTD 1.2727877779954466\n", "dailyenergyexpSTD_SPF 2.3404484715769036\n", "dailyenergyexp_SPF 37.999088\n", "energy_from_microbiota 8.149069683805887\n", "energy_from_microbiotaSTD 1.755491634351854\n", "energy_from_microbiota_via_feces 10.708072348664098\n", "energy_from_microbiota_via_fecesSTD 1.1578999128182021\n", "energydensityinfecesSTD_GF 0.1950326235567492\n", "energydensityinfecesSTD_Oligo 0.21341755885842845\n", "energydensityinfecesSTD_SPF 0.37273695682319746\n", "energydensityinfeces_GF 16.14426285714286\n", "energydensityinfeces_Oligo 16.74741090909091\n", "energydensityinfeces_SPF 16.64471272727273\n", "energyextractionSTD_GF 0.08573452677075334\n", "energyextractionSTD_Oligo 0.09266899019832771\n", "energyextractionSTD_SPF 0.11786164580673167\n", "energyextraction_GF 0.6013221490922128\n", "energyextraction_Oligo 0.628026502977694\n", "energyextraction_SPF 0.7509388572367149\n", "energyfractionSTD_from_microbiota 0.04804944603713973\n", "energyfraction_from_microbiota 0.21445434910979672\n", "energyfraction_from_microbiota_via_feces 0.31244615036524187\n", "energyfraction_from_microbiota_via_fecesSTD 0.03378585411905916\n", "energyinfeces_GF 25.023607428571427\n", "energyinfeces_Oligo 20.180630145454547\n", "energyinfeces_SPF 13.565440872727274\n", "energyinfecesstd_GF 4.6380879322244235\n", "energyinfecesstd_Oligo 2.985652784076652\n", "energyinfecesstd_SPF 1.7706574107424677\n", "energyinfoodSTD_GF 8.949038238171195\n", "energyinfoodSTD_Oligo 8.005328988692762\n", "energyinfoodSTD_SPF 8.54861700280201\n", "energyinfood_GF 62.7664852\n", "energyinfood_Oligo 54.25287098947369\n", "energyinfood_SPF 54.46630784\n", "energytohost_GF 37.74287777142857\n", "energytohost_Oligo 34.07224084401915\n", "energytohost_SPF 40.90086696727273\n", "fecaldryweightSTD_GF 0.2685652113123975\n", "fecaldryweightSTD_Oligo 0.16291978745031907\n", "fecaldryweightSTD_SPF 0.08812869377601525\n", "fecaldryweight_GF 1.5499999999999998\n", "fecaldryweight_Oligo 1.205\n", "fecaldryweight_SPF 0.815\n"]}, {"data": {"text/plain": ["0.04804944603713973"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["37.999088"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["'fraction energy'"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["0.21445434910979672"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["0.28179814075180165"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["0.030471781660080948"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["0.04804944603713973"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# fecal dry mass\n", "\n", "for MT in [\"GF\",\"SPF\",\"Oligo\"]: \n", "    \n", "    #fecal weight\n", "    MOUSE[\"fecaldryweight_\"+MT]=data[MT+\"_fecaldrymass\"].mean()\n", "    MOUSE[\"fecaldryweightSTD_\"+MT]=data[MT+\"_fecaldrymass\"].std()\n", "\n", "    \n", "    \n", "    #energy in food\n", "    MOUSE[\"energyinfood_\"+MT]=data[MT+\"_foodintake\"].mean()*3.94*4.184 # food intake in g, energy content of food: 3.94 kcal/g\n", "    \n", "    relvariation_foodintake=data[MT+\"_foodintake\"]/data[MT+\"_foodintake\"].mean()\n", "    \n", "    MOUSE[\"energyinfoodSTD_\"+MT]=data[MT+\"_foodintake\"].std()*3.94*4.184 # food intake in g, energy content of food: 3.94 kcal/g\n", "   \n", "    #energy density in feces\n", "    MOUSE[\"energydensityinfeces_\"+MT]=data[MT+\"_energyinfeces\"].mean()*4.184 # density of energy in feces in kcal/g\n", "    MOUSE[\"energydensityinfecesSTD_\"+MT]=data[MT+\"_energyinfeces\"].std()*4.184 # density of energy in feces in kcal/g\n", "   \n", "    #energy in feces\n", "    MOUSE[\"energyinfeces_\"+MT]=MOUSE[\"energydensityinfeces_\"+MT]*MOUSE[\"fecaldryweight_\"+MT]\n", "    relerror=np.sqrt(np.power(MOUSE[\"energydensityinfecesSTD_\"+MT]/MOUSE[\"energydensityinfeces_\"+MT]+MOUSE[\"fecaldryweightSTD_\"+MT]/MOUSE[\"fecaldryweight_\"+MT],2.))\n", "    #calculation error - energy in feces\n", "    MOUSE[\"energyinfecesstd_\"+MT]=relerror*MOUSE[\"energyinfeces_\"+MT]\n", "    \n", "    #calculate \n", "    \n", "    #difference\n", "    MOUSE[\"energytohost_\"+MT]=MOUSE[\"energyinfood_\"+MT]-MOUSE[\"energyinfeces_\"+MT]\n", "    MOUSE[\"energyextraction_\"+MT]=MOUSE[\"energytohost_\"+MT]/MOUSE[\"energyinfood_\"+MT]\n", "    #uncertainty in energyextraction: consider only variation in energyinfood \n", "    MOUSE[\"energyextractionSTD_\"+MT]=MOUSE[\"energyextraction_\"+MT]*MOUSE[\"energyinfoodSTD_\"+MT]/MOUSE[\"energyinfood_\"+MT]\n", "    \n", "    #calculate amount via bacteria\n", "MOUSE[\"energy_from_microbiota\"]=(MOUSE[\"energyextraction_SPF\"]-MOUSE[\"energyextraction_GF\"])*MOUSE[\"energyinfood_SPF\"]\n", "#print()\n", "\n", "\n", "#simples energy variation, just use uncertainty in food intake\n", "rellerrorextraction=MOUSE[\"energyextractionSTD_\"+MT]/MOUSE[\"energyextraction_\"+MT]\n", "MOUSE[\"energy_from_microbiotaSTD\"]=MOUSE[\"energy_from_microbiota\"]*np.sqrt(np.power(rellerrorextraction,2.)+np.power(MOUSE[\"energyinfoodSTD_SPF\"]/MOUSE[\"energyinfood_SPF\"],2))\n", "\n", "\n", "print(\"energy from microbiota via GF SPF comparison - mean\")\n", "print(MOUSE[\"energy_from_microbiota\"])\n", "print(\"energy from microbiota via GF SPF comparison- variation based on food intake\")\n", "print(relvariation_foodintake*MOUSE[\"energy_from_microbiota\"])\n", "\n", "\n", "#for SPF mice, calculate energy via feces\n", "\n", "fracbac=0.5\n", "energyinkcal,ferc,fermpro,fermpro_g,ferpro_sum,totalcarb,order,bacterialdrymass_feces=FPcalc.energycalc(MOUSE[\"fecaldryweight_SPF\"]*fracbac,scenario='reference',calctype='from_feces',dict_yielddata=dict_yielddata)\n", "MOUSE[\"FP_production_via_feces\"]=fermpro\n", "MOUSE[\"energy_from_microbiota_via_feces\"]=energyinkcal*4.18\n", "MOUSE[\"energy_from_microbiota_via_fecesSTD\"]=MOUSE[\"energy_from_microbiota_via_feces\"]* MOUSE[\"fecaldryweightSTD_SPF\"]/ MOUSE[\"fecaldryweight_SPF\"]\n", "MOUSE[\"FP_production_via_fecesSTD\"]=MOUSE[\"FP_production_via_feces\"]* MOUSE[\"fecaldryweightSTD_SPF\"]/ MOUSE[\"fecaldryweight_SPF\"]\n", "\n", "for key in sorted(MOUSE.keys()):\n", "    print(key+\" \"+str(MOUSE[key]))\n", "\n", "#calculating fraction of energy expenditure\n", "MOUSE[\"dailyenergyexp_SPF\"]=data[\"SPF_dailyenergyexp\"].mean()*4.184\n", "MOUSE[\"dailyenergyexpSTD_SPF\"]=data[\"SPF_dailyenergyexp\"].std()*4.184\n", "\n", "MOUSE[\"energyfraction_from_microbiota\"]=MOUSE[\"energy_from_microbiota\"]/MOUSE[\"dailyenergyexp_SPF\"]\n", "\n", "#MOUSE[\"energyfraction_from_microbiota\"]=MOUSE[\"energy_from_microbiota\"]/MOUSE[\"dailyenergyexp_SPF\"]\n", "\n", "#estimation variation energy by looking at variatiion in energy from microbiota and variation in total energy expenditure\n", "MOUSE[\"energyfractionSTD_from_microbiota\"]=MOUSE[\"energyfraction_from_microbiota\"]*np.sqrt(np.power(MOUSE[\"energy_from_microbiotaSTD\"]/MOUSE[\"energy_from_microbiota\"],2.)+np.power(MOUSE[\"dailyenergyexpSTD_SPF\"]/MOUSE[\"dailyenergyexp_SPF\"],2))\n", "\n", "display(MOUSE[\"energyfractionSTD_from_microbiota\"])\n", "\n", "\n", "\n", "MOUSE[\"energyfraction_from_microbiota_via_feces\"]=MOUSE[\"energy_from_microbiota_via_feces\"]/MOUSE[\"dailyenergyexp_SPF\"]\n", "MOUSE[\"energyfraction_from_microbiota_via_fecesSTD\"]=MOUSE[\"energyfraction_from_microbiota_via_feces\"]* MOUSE[\"fecaldryweightSTD_SPF\"]/ MOUSE[\"fecaldryweight_SPF\"]\n", "                                                           \n", "\n", "display(MOUSE[\"dailyenergyexp_SPF\"])\n", "display(\"fraction energy\")\n", "display(MOUSE[\"energyfraction_from_microbiota\"])\n", "display(MOUSE[\"energyfraction_from_microbiota_via_feces\"])\n", "display(MOUSE[\"energyfraction_from_microbiota_via_fecesSTD\"])\n", "display(MOUSE[\"energyfractionSTD_from_microbiota\"])\n", "\n", "with open('data_analysisresults/mouse_characteristics.json', 'w') as fp:\n", "    json.dump(MOUSE, fp)\n", "\n", "# calculate energy\n", "\n", "#Index(['GF_lean_mass', 'GF_energy_exp', 'Oligo_lean_mass', 'Oligo_energy_exp',\n", "#       'SPF_lean_mass', 'SPF_energy_exp', 'GF_lean_mass_DP',\n", "#       'GF_energy_exp_DP', 'Oligo_lean_mass_DP', 'Oligo_energy_exp_DP',\n", "#       'SPF_lean_mass_DP', 'SPF_energy_exp_DP', 'GF_foodintake',\n", "#       'Oligo_foodintake', 'SPF_foodintake', 'GF_fecaldrymass',\n", "#       'Oligo_fecaldrymass', 'SPF_fecaldrymass', 'GF_energyinfeces',\n", "#       'Oligo_energyinfeces', 'SPF_energyinfeces'],\n", "#      dtype='object')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 400x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, axs = plt.subplots(1,1, figsize=(4,2))\n", "\n", "####from carb calculation\n", "#carbs\n", "axs.barh(1,MOUSE[\"energy_from_microbiota_via_feces\"],height=0.7,xerr=MOUSE[\"energy_from_microbiota_via_fecesSTD\"],color='blue',capsize=capsize)\n", "axs.barh(2,MOUSE[\"energy_from_microbiota\"],height=0.7,xerr=MOUSE[\"energy_from_microbiota_via_fecesSTD\"],color='green',capsize=capsize)\n", "\n", "axs.set_xlabel(\"energy from microbiota (kJ/day)\")\n", "#bacterial dry mass\n", "axs.set_yticks([1,2])\n", "axs.set_yticklabels([\"via feces\",\"GF vs CC\"])\n", "#axs.set_ylim(ym,ymax)\n", "axs.spines[['right', 'top']].set_visible(False)\n", "\n", "\n", "fig.tight_layout()\n", "fig.savefig(\"plot_output_figure5/mouse_energyestimations.pdf\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Plot distributions into one plot"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Basic considerations of ATP yield"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 4}