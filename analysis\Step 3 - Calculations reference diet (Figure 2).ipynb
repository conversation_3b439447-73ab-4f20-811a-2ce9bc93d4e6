{"cells": [{"cell_type": "markdown", "metadata": {"tags": []}, "source": ["# Estimation of Daily Fermentation Product Release - Analysis for British Reference Diet (Figure 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Make sure to run Figure3 script first when running script"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'energy': 2275.0,\n", " 'carbohydrates': 276.75,\n", " 'sugars': 59.0,\n", " 'proteins': 72.05000000000001,\n", " 'fat': 105.5,\n", " 'fiber': 19.9,\n", " 'fiber_low': 14.599999999999998,\n", " 'fiber_high': 25.2,\n", " 'carbLI_standard': 35.670500000000004,\n", " 'carbLI_higher': 44.6025,\n", " 'carbLI_lower': 25.755000000000003,\n", " 'carbLI_error': 9.423749999999998,\n", " 'bacwetweight': 117.72413793103448,\n", " 'fecalwetmass': 117.72413793103448,\n", " 'fecaldrymass': 29.58620689655172,\n", " 'fecaldrymassstd': 6.845624030794191,\n", " 'fecealfractionbac': 0.546888888888889,\n", " 'bacterialdrymass_feces': 16.180367816091955,\n", " 'energybacteria_fromfeces': 101.71752675768855,\n", " 'FP_fromfeces': 467.3659285620218,\n", " 'FP_fromfeces_g': [8.035607499560594,\n", "  2.7046488116402316,\n", "  6.192333401520376,\n", "  9.51608312429447,\n", "  1.6695716933854843,\n", "  4.741614607512687],\n", " 'FP_fromfeces_gsum': 32.859859137913844,\n", " 'energyfrac_fromfeces': 0.04471100077261035,\n", " 'energybacteria_fromfeces_error': 23.53528953407475,\n", " 'energyfrac_fromfeces_error': 0.01034518221278011,\n", " 'FP_fromfeces_error': 108.13861482566425,\n", " 'bacterialdrymass_feces_error': 3.743795719952113,\n", " 'energy_fromcarbs_standard': 98.74730465043015,\n", " 'FPlist_fromcarbs_standard': [129.903453754712,\n", "  29.799922782745863,\n", "  130.5998910474014,\n", "  102.55558301278339,\n", "  21.8796021223617,\n", "  38.98006759839011],\n", " 'FP_fromcarbs_standard': 453.7185203183944,\n", " 'FP_fromcarbs_g_standard': [7.8009622048779645,\n", "  2.6256711963877377,\n", "  6.011512984911887,\n", "  9.238206917791528,\n", "  1.6208190456224323,\n", "  4.603156182693888],\n", " 'FP_fromcarbs_gsum_standard': 31.900328532285435,\n", " 'ferm_fromcarbs_cc_standard': 1038.8323154868044,\n", " 'drymass_fromcarbs_standard': 15.707889889004568,\n", " 'energyfrac_fromcarbs_standard': 0.04340540863755171,\n", " 'energy_fromcarbs_lower': 71.29804267593188,\n", " 'FPlist_fromcarbs_lower': [93.7935675544948,\n", "  21.51629529357928,\n", "  94.29641283205514,\n", "  74.04771563320492,\n", "  15.797624161742212,\n", "  28.14459121673476],\n", " 'FP_fromcarbs_lower': 327.59620669181106,\n", " 'FP_fromcarbs_g_lower': [5.632491318782521,\n", "  1.8958007783172701,\n", "  4.340463882659498,\n", "  6.6702182242390995,\n", "  1.170272200277701,\n", "  3.323594776784208],\n", " 'FP_fromcarbs_gsum_lower': 23.0328411810603,\n", " 'ferm_fromcarbs_cc_lower': 750.0631133671424,\n", " 'drymass_fromcarbs_lower': 11.341492384219807,\n", " 'energy_fromcarbs_higher': 123.47392539131242,\n", " 'FPlist_fromcarbs_higher': [162.43166751502054,\n", "  37.261912670622,\n", "  163.3024947909819,\n", "  128.2358080578537,\n", "  27.358320002877377,\n", "  48.74079323410646],\n", " 'FP_fromcarbs_higher': 567.3309962714619,\n", " 'FP_fromcarbs_g_higher': [9.754346497612014,\n", "  3.2831471254085045,\n", "  7.516813835228898,\n", "  11.551481589851461,\n", "  2.026676987493153,\n", "  5.755800273015632],\n", " 'FP_fromcarbs_gsum_higher': 39.88826630860966,\n", " 'ferm_fromcarbs_cc_higher': 1298.95903762213,\n", " 'drymass_fromcarbs_higher': 19.641192547744666,\n", " 'energyfrac_fromcarbs_lower': 0.031339798978431595,\n", " 'energyfrac_fromcarbs_higher': 0.054274252919258206,\n", " 'FP_fromcarbs_standard_error': 119.86739478982543,\n", " 'FP_fromcarbs_gsum_standard_error': 8.427712563774682,\n", " 'drymass_fromcarbs_standard_error': 4.1498500817624295,\n", " 'energy_fromcarbs_standard_error': 26.087941357690273,\n", " 'energyfrac_fromcarbs_standard_error': 0.011467226970413306,\n", " 'FP_infeces': 9.041213793103447,\n", " 'FP_infeces_error': 9.41793103448276,\n", " 'proteins_LI': 7,\n", " 'mucus': 4.4,\n", " 'mucus_protein': 0.8800000000000001,\n", " 'mucus_CH': 3.5200000000000005}"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#load required functions\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import matplotlib\n", "matplotlib.rcParams['pdf.fonttype'] = 42\n", "matplotlib.rcParams['ps.fonttype'] = 42\n", "from matplotlib.ticker import PercentFormatter\n", "import scipy\n", "import json\n", "import os\n", "\n", "capsize=6\n", "#set colorscheme\n", "colorav='k'\n", "colorrefdiet='purple'\n", "colorlistferm=['#1b9e77','#d95f02','#7570b3','#e7298a','#66a61e']\n", "\n", "labelenergybac=\"energy supply via bacteria (kcal)\"\n", "labelenergybacfrac=\"enery supply via bacteria (%)\"\n", "\n", "labelenergybacfracnounit=\"energy supply via bacteria\"\n", "\n", "results_for_figure2={}\n", "import FPcalc #basic calculations of \n", "\n", "#dict to save major characteristics of British reference diet\n", "#load file if already exists, otherwise start with empty dict\n", "try:\n", "    with open('data_analysisresults/BRD_characteristics.json', 'r') as fp:\n", "        BRD = json.load(fp)\n", "except:\n", "    BRD={}\n", "\n", "display(BRD)\n", "\n", "#############################\n", "#yield data to use as standard\n", "dict_yielddata=\"data_analysisresults/average_excretion/selectedsamples_HMP_2019_ibdmdb_disease_healthy_age_adultsenior_NW_no_genus.json\"\n", "#selectedsamples_HMP_2019_ibdmdb_disease_healthy_age_All_NW_All_genus.json\"\n", "#############################\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Calculation fermentation products / energy contribution for British diet (via feces)\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["av and std bacterial dry mass (std derived taking variation of fecal dry weight and fraction of bacteria into account)\n"]}, {"ename": "NameError", "evalue": "name 'BRD' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 5\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m#start with calculating bacterial dry mass\u001b[39;00m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mav and std bacterial dry mass (std derived taking variation of fecal dry weight and fraction of bacteria into account)\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m----> 5\u001b[0m BRD[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mbacterialdrymass_feces\u001b[39m\u001b[38;5;124m\"\u001b[39m]\u001b[38;5;241m=\u001b[39m\u001b[43mBRD\u001b[49m[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfecaldrymass\u001b[39m\u001b[38;5;124m\"\u001b[39m]\u001b[38;5;241m*\u001b[39mBRD[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfecealfractionbac\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[1;32m      8\u001b[0m \u001b[38;5;66;03m#calculate energy fraction\u001b[39;00m\n\u001b[1;32m      9\u001b[0m BRD[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124menergybacteria_fromfeces\u001b[39m\u001b[38;5;124m\"\u001b[39m],ferc,BRD[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFP_fromfeces\u001b[39m\u001b[38;5;124m\"\u001b[39m],BRD[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFP_fromfeces_g\u001b[39m\u001b[38;5;124m\"\u001b[39m],BRD[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFP_fromfeces_gsum\u001b[39m\u001b[38;5;124m\"\u001b[39m],totalcarb,orderc,BRD[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mbacterialdrymass_feces\u001b[39m\u001b[38;5;124m\"\u001b[39m]\u001b[38;5;241m=\u001b[39mFPcalc\u001b[38;5;241m.\u001b[39menergycalc(BRD[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mbacterialdrymass_feces\u001b[39m\u001b[38;5;124m\"\u001b[39m],scenario\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mreference\u001b[39m\u001b[38;5;124m'\u001b[39m,calctype\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfrom_feces\u001b[39m\u001b[38;5;124m'\u001b[39m,dict_yielddata\u001b[38;5;241m=\u001b[39mdict_yielddata)\n", "\u001b[0;31mNameError\u001b[0m: name 'BRD' is not defined"]}], "source": ["#start with calculating bacterial dry mass\n", "print(\"av and std bacterial dry mass (std derived taking variation of fecal dry weight and fraction of bacteria into account)\")\n", "\n", "\n", "BRD[\"bacterialdrymass_feces\"]=BRD[\"fecaldrymass\"]*BRD[\"fecealfractionbac\"]\n", "\n", "\n", "#calculate energy fraction\n", "BRD[\"energybacteria_fromfeces\"],ferc,BRD[\"FP_fromfeces\"],BRD[\"FP_fromfeces_g\"],BRD[\"FP_fromfeces_gsum\"],totalcarb,orderc,BRD[\"bacterialdrymass_feces\"]=FPcalc.energycalc(BRD[\"bacterialdrymass_feces\"],scenario='reference',calctype='from_feces',dict_yielddata=dict_yielddata)\n", "BRD[\"energyfrac_fromfeces\"]=BRD[\"energybacteria_fromfeces\"]/BRD[\"energy\"]\n", "\n", "#totenergy,totfermlist,totfermlist_sum,totfermlist_gram,totfermlist_gram_sum,totfem_carb,order,bacdrymass #returns energy of fermentation products (in kcal) and \n", "print(BRD)\n", "\n", "print(BRD[\"energybacteria_fromfeces\"])\n", "print(BRD[\"energy\"])\n", "print(BRD[\"energyfrac_fromfeces\"])\n", "\n", "#####\n", "#calculate relative errors\n", "#####\n", "\n", "#relative error accumulates\n", "#relerror=np.sqrt(np.power(BRD[\"fecaldrymassstd\"]/BRD[\"fecaldrymass\"],2)+np.power(BRD[\"fecealfractionbacstd\"]/BRD[\"fecealfractionbac\"],2)) #takes fecal mass variation and fraction bac dry mass into account\n", "relerror=BRD[\"fecaldrymassstd\"]/BRD[\"fecaldrymass\"]\n", "\n", "BRD[\"energybacteria_fromfeces_error\"]=relerror*BRD[\"energybacteria_fromfeces\"]\n", "BRD[\"energyfrac_fromfeces_error\"]=relerror*BRD[\"energyfrac_fromfeces\"]\n", "BRD[\"FP_fromfeces_error\"]=relerror*BRD[\"FP_fromfeces\"]\n", "BRD[\"bacterialdrymass_feces_error\"]=BRD[\"bacterialdrymass_feces\"]*relerror"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Calculation energy contribution of bacteira for British ref diet (via carb content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#todo: check if energy calculations, why are the numbers so different for the different ways to estimate?"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["BRD[\"energy_fromcarbs_standard\"],BRD[\"FPlist_fromcarbs_standard\"],BRD[\"FP_fromcarbs_standard\"],BRD[\"FP_fromcarbs_g_standard\"],BRD[\"FP_fromcarbs_gsum_standard\"],BRD[\"ferm_fromcarbs_cc_standard\"],order,BRD[\"drymass_fromcarbs_standard\"]=FPcalc.energycalc(BRD[\"carbLI_standard\"],scenario='reference',calctype='from_carbs',dict_yielddata=dict_yielddata)\n", "BRD[\"energyfrac_fromcarbs_standard\"]=BRD[\"energy_fromcarbs_standard\"]/BRD[\"energy\"]\n", "\n", "\n", "BRD[\"energy_fromcarbs_lower\"],BRD[\"FPlist_fromcarbs_lower\"],BRD[\"FP_fromcarbs_lower\"],BRD[\"FP_fromcarbs_g_lower\"],BRD[\"FP_fromcarbs_gsum_lower\"],BRD[\"ferm_fromcarbs_cc_lower\"],order,BRD[\"drymass_fromcarbs_lower\"]=FPcalc.energycalc(BRD[\"carbLI_lower\"],scenario='reference',calctype='from_carbs',dict_yielddata=dict_yielddata)\n", "BRD[\"energy_fromcarbs_higher\"],BRD[\"FPlist_fromcarbs_higher\"],BRD[\"FP_fromcarbs_higher\"],BRD[\"FP_fromcarbs_g_higher\"],BRD[\"FP_fromcarbs_gsum_higher\"],BRD[\"ferm_fromcarbs_cc_higher\"],order,BRD[\"drymass_fromcarbs_higher\"]=FPcalc.energycalc(BRD[\"carbLI_higher\"],scenario='reference',calctype='from_carbs',dict_yielddata=dict_yielddata)\n", "BRD[\"energyfrac_fromcarbs_lower\"]=BRD[\"energy_fromcarbs_lower\"]/BRD[\"energy\"]\n", "BRD[\"energyfrac_fromcarbs_higher\"]=BRD[\"energy_fromcarbs_higher\"]/BRD[\"energy\"]\n", "#return totenergy,totfermlist,totfermlist_sum,totfermlist_gram,totfermlist_gram_sum,totfem_carb,order,bacdrymass #returns energy of fermentation products (in kcal) and amount of fermentation products (in mmol)\n", "\n", "#errors from variation in carbon\n", "relerror=BRD[\"carbLI_error\"]/BRD[\"carbLI_standard\"]\n", "BRD[\"FP_fromcarbs_standard_error\"]=relerror*BRD[\"FP_fromcarbs_standard\"]\n", "BRD[\"FP_fromcarbs_gsum_standard_error\"]=relerror*BRD[\"FP_fromcarbs_gsum_standard\"]\n", "BRD[\"drymass_fromcarbs_standard_error\"]=relerror*BRD[\"drymass_fromcarbs_standard\"]\n", "BRD[\"energy_fromcarbs_standard_error\"]=relerror*BRD[\"energy_fromcarbs_standard\"]\n", "BRD[\"energyfrac_fromcarbs_standard_error\"]=relerror*BRD[\"energyfrac_fromcarbs_standard\"]\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Look into carbon balance (for via carbohydrate estimation)\n", "\n", "## look at the numbers together with <PERSON>. Double check: ASSUMED CARBON NUMBERS, DOUBLE CHECK CARBS PER CELL"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MEDIUM YCA\n", "********used av. yield and excretion*******\n", "********carbon input*******\n", "carbon in LI in g\n", "35.670500000000004\n", "carbon in LI in mmol\n", "198.16944444444448\n", "carbon in LI in mmol carbon units\n", "1189.0166666666669\n", "********carbon in excreted fermentation products *******\n", "order FP list\n", "['acetate', 'butyrate', 'formate', 'lactate', 'propionate', 'succinate']\n", "used carbon per FP\n", "[2 4 1 3 3 4]\n", "daily secretion in mmol FP/day\n", "[129.90345375  29.79992278 130.59989105 102.55558301  21.87960212\n", "  38.9800676 ]\n", "daily secretion in mmol C/day\n", "[259.80690751 119.19969113 130.59989105 307.66674904  65.63880637\n", " 155.92027039]\n", "total daily secretion mmol C/ day\n", "1038.8323154868044\n", "********carbon in bacteria*******\n", "bacterial drymass in g\n", "15.707889889004568\n", "bacterial drymass - carb atoms in g, assuming 0.5 dryweight is carb\n", "DOUBLECHECK 0.5 factor correct?\n", "7.853944944502284\n", "bacterial drymass - g carb \n", "6.1260770567117815\n"]}], "source": ["for media in [\"YCA\"]:\n", "    print(\"MEDIUM \"+media)\n", "    print(\"********used av. yield and excretion*******\")\n", "\n", "\n", "    #########################\n", "    # DECIDE HERE WHICH AVERAGE YIELD AND EXCRETION VALUES TO USE\n", "    #########################\n", "    #old: av_\"+media+\"\n", "    with open(os.path.join(dict_yielddata)) as f:\n", "            yielddict = json.load(f)\n", "    #print(\"carbon total secretion mmolC/g\")\n", "    #print(yielddict[\"total_secretion_carbon\"])\n", "    #print(\"carbon uptake mmolC/g\")\n", "    #print(yielddict[\"uptake_carbon\"])\n", "\n", "\n", "    print(\"********carbon input*******\")\n", "    print(\"carbon in LI in g\")\n", "    print(BRD[\"carbLI_standard\"])\n", "    print(\"carbon in LI in mmol\")\n", "    print(BRD[\"carbLI_standard\"]/0.180 )\n", "    print(\"carbon in LI in mmol carbon units\")\n", "    print(BRD[\"carbLI_standard\"]*6/0.180)\n", "\n", "\n", "    print(\"********carbon in excreted fermentation products *******\")\n", "    print(\"order FP list\")\n", "    print(order)\n", "    print(\"used carbon per FP\")\n", "    cfactorlist=np.array([2,4,1,3,3,4])\n", "    print(cfactorlist)\n", "\n", "    print(\"daily secretion in mmol FP/day\")\n", "    print(np.array(BRD[\"FPlist_fromcarbs_standard\"]))\n", "    print(\"daily secretion in mmol C/day\")\n", "    print(np.array(BRD[\"FPlist_fromcarbs_standard\"])*cfactorlist)\n", "    print(\"total daily secretion mmol C/ day\")\n", "    print(np.sum(np.array(BRD[\"FPlist_fromcarbs_standard\"])*cfactorlist))\n", "\n", "    print(\"********carbon in bacteria*******\")\n", "    print(\"bacterial drymass in g\")\n", "    print(BRD[\"drymass_fromcarbs_standard\"])\n", "    print(\"bacterial drymass - carb atoms in g, assuming 0.5 dryweight is carb\")\n", "    print(\"DOUBLECHECK 0.5 factor correct?\")\n", "    print(BRD[\"drymass_fromcarbs_standard\"]*0.5)\n", "    print(\"bacterial drymass - g carb \")\n", "    print(BRD[\"drymass_fromcarbs_standard\"]*0.39)#/0.012) #assumes 12g/mol or 0.012g/mmol\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# same carbon analysis but for different media\n", "\n", "# Note: this assumes equal average of all strains characterized for this media"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["************************\n", "************************\n", "*********MEDIA: YCA\n", "********used av. yield and excretion*******\n", "carbon total secretion mmolC/g\n", "62.7521949917264\n", "carbon uptake mmolC/g\n", "84.6450046608256\n", "total FP release\n", "417.7575049985361\n", "total FP release via feces\n", "481.6014689894519\n", "********carbon input*******\n", "carbon in LI in mmol carbon units\n", "1189.0166666666669\n", "carbon in LI in g\n", "35.670500000000004\n", "********carbon in excreted fermentation products *******\n", "total daily secretion mmol C/ day\n", "913.1918696714423\n", "total daily secretion g C/day\n", "10.96743435475402\n", "********carbon in bacteria*******\n", "bacterial drymass - g carb\n", "5.473806672905561\n", "************************\n", "************************\n", "*********MEDIA: epsilon\n", "********used av. yield and excretion*******\n", "carbon total secretion mmolC/g\n", "72.42044430725797\n", "carbon uptake mmolC/g\n", "76.3328459496272\n", "total FP release\n", "451.11936861675287\n", "total FP release via feces\n", "468.9916790061733\n", "********carbon input*******\n", "carbon in LI in mmol carbon units\n", "1189.0166666666669\n", "carbon in LI in g\n", "35.670500000000004\n", "********carbon in excreted fermentation products *******\n", "total daily secretion mmol C/ day\n", "1130.8790347451766\n", "total daily secretion g C/day\n", "13.58185720728957\n", "********carbon in bacteria*******\n", "bacterial drymass - g carb\n", "6.069869209989968\n", "************************\n", "************************\n", "*********MEDIA: BHI\n", "********used av. yield and excretion*******\n", "carbon total secretion mmolC/g\n", "71.77180512724799\n", "carbon uptake mmolC/g\n", "44.312293685590305\n", "total FP release\n", "931.4403481145304\n", "total FP release via feces\n", "562.1361128853688\n", "********carbon input*******\n", "carbon in LI in mmol carbon units\n", "1189.0166666666669\n", "carbon in LI in g\n", "35.670500000000004\n", "********carbon in excreted fermentation products *******\n", "total daily secretion mmol C/ day\n", "2006.3753630892816\n", "total daily secretion g C/day\n", "24.096568110702272\n", "********carbon in bacteria*******\n", "bacterial drymass - g carb\n", "10.456023663049917\n", "************************\n", "************************\n", "*********MEDIA: gamma\n", "********used av. yield and excretion*******\n", "carbon total secretion mmolC/g\n", "62.06569117313568\n", "carbon uptake mmolC/g\n", "88.88596769505881\n", "total FP release\n", "506.8744240295388\n", "total FP release via feces\n", "613.6147519340119\n", "********carbon input*******\n", "carbon in LI in mmol carbon units\n", "1189.0166666666669\n", "carbon in LI in g\n", "35.670500000000004\n", "********carbon in excreted fermentation products *******\n", "total daily secretion mmol C/ day\n", "860.0028023135144\n", "total daily secretion g C/day\n", "10.328633655785309\n", "********carbon in bacteria*******\n", "bacterial drymass - g carb\n", "5.212638207755106\n"]}], "source": ["\n", "diffmedia=[\"YCA\",\"epsilon\",\"BHI\",\"gamma\"]\n", "diffmedia_FP_viacarbs=[]\n", "diffmedia_FP_viafeces=[]\n", "for media in diffmedia:\n", "    curengery,curFPlist,curFP,curFPg,curFPgsum,curFPcc,order,curdryweight=FPcalc.energycalc(BRD[\"carbLI_standard\"],scenario='reference',calctype='from_carbs',dict_yielddata=\"data_analysisresults/average_excretion/av_\"+media+\".json\")\n", "\n", "    #also via feces\n", "    curengeryVF,curFPlistVF,curFPVF,curFPgVF,curFPgsumVF,curFPccVF,orderVF,curdryweightVF=FPcalc.energycalc(BRD[\"bacterialdrymass_feces\"],scenario='reference',calctype='from_feces',dict_yielddata=\"data_analysisresults/average_excretion/av_\"+media+\".json\")\n", "                                                                                          \n", "    print(\"************************\")\n", "    print(\"************************\")\n", "    print(\"*********MEDIA: \"+media)\n", "    print(\"********used av. yield and excretion*******\")\n", "\n", "    with open(os.path.join(\"data_analysisresults/average_excretion/av_\"+media+\".json\")) as f:\n", "            yielddict = json.load(f)\n", "    print(\"carbon total secretion mmolC/g\")\n", "    print(yielddict[\"total_secretion_carbon\"])\n", "    print(\"carbon uptake mmolC/g\")\n", "    print(yielddict[\"uptake_carbon\"])\n", "\n", "    print(\"total FP release\")\n", "    print(curFP)\n", "    diffmedia_FP_viacarbs.append(curFP)\n", "    print(\"total FP release via feces\")\n", "    print(curFPVF)\n", "    diffmedia_FP_viafeces.append(curFPVF)\n", "   \n", "    print(\"********carbon input*******\")\n", "    #print(\"carbon in LI in g\")\n", "    #print(BRD[\"carbLI\"])\n", "    #print(\"carbon in LI in mmol\")\n", "    #print(BRD[\"carbLI\"]/0.180 )\n", "    print(\"carbon in LI in mmol carbon units\")\n", "    print(BRD[\"carbLI_standard\"]*6/0.180)\n", "    print(\"carbon in LI in g\")\n", "    print(BRD[\"carbLI_standard\"])\n", "\n", "    print(\"********carbon in excreted fermentation products *******\")\n", "    #print(\"order FP list\")\n", "    #print(order)\n", "    #print(\"used carbon per FP\")\n", "    #cfactorlist=np.array([2,4,1,3,2,4])\n", "    #print(cfactorlist)\n", "\n", "    #print(\"daily secretion in mmol FP/day\")\n", "    #print(np.array(curFPlist))\n", "    #print(\"daily secretion in mmol C/day\")\n", "    #print(np.array(curFPlist)*cfactorlist)\n", "    print(\"total daily secretion mmol C/ day\")\n", "    print(np.sum(np.array(curFPlist)*cfactorlist))\n", "    print(\"total daily secretion g C/day\")\n", "    print(np.sum(np.array(curFPlist)*cfactorlist)*12.01/1000.)\n", "\n", "    print(\"********carbon in bacteria*******\")\n", "    #print(\"bacterial drymass in g\")\n", "    #print(curdryweight)\n", "    #print(\"bacterial drymass - carb atoms in g, assuming 0.5 dryweight is carb\")\n", "    #print(\"DOUBLECHECK 0.5 factor correct?\")\n", "    #print(curdryweight*0.5)\n", "    print(\"bacterial drymass - g carb\")\n", "    print(curdryweight*0.39)#/0.012) #assumes 12g/mol or 0.012g/mmol\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 230x350 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "#plot FP release for different media\n", "fig, axs = plt.subplots(1,1, figsize=(2.3,3.5))\n", "axs=[axs]\n", "\n", "#\n", "labelc=[]\n", "poscount=0\n", "for mC in [0,1,3,2]:\n", "    poscount=poscount+1\n", "    if poscount==1:\n", "        label=\"via carb.\"\n", "        label2=\"via feces.\"\n", "    else:\n", "        label=None\n", "        label2=None\n", "    axs[0].bar(poscount,diffmedia_FP_viacarbs[mC],width=0.4,color=\"gray\",label=label)\n", "    axs[0].bar(poscount+.4,diffmedia_FP_viafeces[mC],hatch=\"\\\\\\\\\",width=0.4,color=\"brown\",label=label2)\n", "    labelc.append(diffmedia[mC])\n", "axs[0].set_ylabel(\"fermentation products (mmol/day)\")\n", "\n", " \n", "axs[0].set_xticks([1,2,3,4])\n", "axs[0].set_xticklabels(labelc,rotation=45)\n", "#axs[0].set_xticks([1,2])\n", "#axs[0].set_xticklabels([\"via fecal mass\",\"via carbs\"],rotation=90)\n", "#axs[0].set_ylim(0,280)\n", "\n", "axs[0].legend()\n", "\n", "fig.tight_layout()\n", "fig.savefig(\"plot_output_figure2/FP_estimate_diffmedia.pdf\")\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# SIMPLE comparion plot"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.04471100077261035\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 230x350 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 230x350 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#compare different estimations (from fecal weight, and from carbs reaching LI\n", "#\n", "#plot energy fraction for both types of measurements\n", "fig, axs = plt.subplots(1,1, figsize=(2.3,3.5))\n", "axs=[axs]\n", "\n", "\n", "print(BRD[\"energyfrac_fromfeces\"])\n", "axs[0].bar([1,2],[BRD[\"energyfrac_fromfeces\"]*100,BRD[\"energyfrac_fromcarbs_standard\"]*100],yerr=[100*BRD[\"energyfrac_fromfeces_error\"],100*BRD[\"energyfrac_fromcarbs_standard_error\"]],color=[colorrefdiet,colorrefdiet],capsize=capsize)\n", "#\n", "#    databox,labels=[\"bacteria\"],vert=True,showfliers=True, widths=[0.7]*1,patch_artist=True,)#,\"Mi<PERSON> (autoclaved lab coy)\"])\n", "\n", "axs[0].set_ylabel(labelenergybacfrac)\n", "axs[0].set_xticks([1,2])\n", "axs[0].set_xticklabels([\"via fecal mass\",\"via carbs\"],rotation=90)\n", "#axs[0].set_ylim(0,280)\n", "\n", "fig.tight_layout()\n", "fig.savefig(\"plot_output_figure2/BRITISH_carbLI.pdf\")\n", "\n", "\n", "#plot energy fraction for both types of measurements\n", "fig, axs = plt.subplots(1,1, figsize=(2.3,3.5))\n", "axs=[axs]\n", "\n", "axs[0].bar([1,2],[BRD[\"FP_fromfeces\"],BRD[\"FP_fromcarbs_standard\"]],yerr=[BRD[\"FP_fromfeces_error\"],BRD[\"FP_fromcarbs_standard_error\"]],color=[colorrefdiet,colorrefdiet],capsize=capsize)\n", "  \n", "#    databox,labels=[\"bacteria\"],vert=True,showfliers=True, widths=[0.7]*1,patch_artist=True,)#,\"Mi<PERSON> (autoclaved lab coy)\"])\n", "\n", "axs[0].set_ylabel(\"fermentation products (mmol/day)\")\n", "axs[0].set_xticks([1,2])\n", "axs[0].set_xticklabels([\"via fecal mass\",\"via carbs\"],rotation=90)\n", "#axs[0].set_ylim(0,280)\n", "\n", "fig.tight_layout()\n", "fig.savefig(\"plot_output_figure2/BRITISH_carbLI_ferm2.pdf\")\n", "\n", "\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["453.7185203183944\n", "517.3548839547581\n", "506.4919098565491\n", "from carbs:\n", "453.7185203183944\n", "from prot:\n", "63.63636363636368\n", "from mucus:\n", "52.77338953815473\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYYAAAC+CAYAAAAx3qiRAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAAAoH0lEQVR4nO3dfVxN2f4H8M/p6fRwjqRSoZFETypTalR3yHiIcDMueZpMGnlu6jbN0Muza378zMyVGVPGvVTXnZfuvAzycmkYFIpKIa506WoKmcQoMiq1f3/4ta+lBzFI9Xm/Xuf1stdee+3vWuV8z1p7d7ZCkiQJRERE/0+jtQMgIqLXCxMDEREJmBiIiEjAxEBERAImBiIiEjAxEBGRgImBiIgETAxERCTQau0AiFpTXV0drl+/DrVaDYVC0drhEL00kiTh7t276NatGzQ0mp8TMDFQh3b9+nVYWlq2dhhEr0xxcTF69OjRbB0mBurQ1Go1gEf/WTp16tTK0RC9PBUVFbC0tJR/55vDxEAdWv3yUadOnZgYqENoyZIpLz4TEZGAiYGIiARMDEREJOA1BiIAawzXQBe6rR0G0XNbLi1/YW1xxkBERAImBiIiEjAxEBGRgImBiIgETAxERCRgYiAiIgETAxERCZgYiIhIwMRAREQCJgYiIhI8U2KQJAmzZs1Cly5doFAocObMmZcU1m9jZWWF6OhoeVuhUGD37t2tFs9vFR8fj86dO//mdl7WOLT18SUi0TN9V1JycjLi4+ORkpICa2trmJiYvKy4XqiSkhIYGRm1uH5QUBDu3LnDN7tXzMfHB/379xeSOhG9es+UGAoKCmBhYQEvL68m61RXV0NHR+c3B/YimZubt3YIz62mpqa1Q/jNampqoK2t3dphEFELtXgpKSgoCKGhoSgqKoJCoYCVlRWAR5/yFixYgIiICJiYmGD48OEAgNTUVHh4eECpVMLCwgKLFi3Cw4cP5fZ8fHwQGhqK8PBwGBkZwczMDJs3b0ZlZSVmzJgBtVqN3r17Y//+/c3GVVpairFjx0JPTw+9evXCt99+26DOk0sd165dw6RJk2BkZARjY2P4+/ujsLAQALBixQokJCQgKSkJCoUCCoUCKSkpLR0mpKWlYfDgwdDX14eRkRF8fX3xyy+/AHg04/rd736Hzp07w9jYGGPGjEFBQYF8bGFhIRQKBb777jv4+PhAV1cXf//73+X9u3fvRt++faGrq4vhw4ejuLhYOHdsbCx69+4NHR0d2NraYtu2bQ3iKysrw7vvvgt9fX306dMHe/bsAfBomdDGxgaff/65UP/8+fPQ0NCQ47x06RIGDRoEXV1dODg44ODBg0L9pvpw69YtTJkyBT169IC+vj6cnJywfft2+bigoCCkpqZiw4YN8rjX/0wuXLgAPz8/qFQqmJmZITAwEGVlZfKxO3bsgJOTE/T09GBsbIxhw4ahsrKyxT8zIhK1ODFs2LABq1atQo8ePVBSUoKsrCx5X0JCArS0tJCWloZvvvkG165dg5+fH9zd3XH27FnExsZiy5YtWL16tdBmQkICTExMkJmZidDQUMydOxcTJ06El5cXcnJy4Ovri8DAQNy/f7/JuIKCglBYWIjDhw9jx44diImJQWlpaZP179+/jyFDhkClUuHo0aM4fvw4VCoVRo4cierqakRGRiIgIAAjR45ESUkJSkpKmp0hPe7MmTMYOnQoHB0dceLECRw/fhxjx45FbW0tAKCyshIRERHIysrCoUOHoKGhgXfffRd1dXVCOwsXLsSHH36IvLw8+Pr6ynF/+umnSEhIQFpaGioqKjB58mT5mF27diEsLAwfffQRzp8/j9mzZ2PGjBk4cuSI0PbKlSsREBCA3Nxc+Pn5Ydq0abh9+zYUCgWCg4MRFxcn1N+6dSvefvtt9O7dG3V1dRg/fjw0NTVx8uRJbNq0CQsXLmx0LJ7sw4MHD+Dm5oa9e/fi/PnzmDVrFgIDA5GRkQHg0e+Xp6cnQkJC5HG3tLRESUkJBg8ejP79++PUqVNITk7Gzz//jICAAACPlgmnTJmC4OBg5OXlISUlBePHj4ckSY3GVVVVhYqKCuFFRCKF1NT/oEZER0cjOjpa/iQHPPrkX15ejtOnT8tlixcvxvfff4+8vDz5+aIxMTFYuHAhysvLoaGhAR8fH9TW1uLYsWMAgNraWhgaGmL8+PH429/+BgC4ceMGLCwscOLECQwcOLBBPP/+979ha2uLkydP4q233gIAXLx4Efb29li/fj3Cw8MfdVKhwK5duzBu3Dhs3boV69atE2Krrq5G586dsXv3bowYMeK5rzFMnToVRUVFOH78eIvq37x5E127dsW5c+fQr18/FBYWolevXoiOjkZYWJhcLz4+HjNmzGi0nxkZGfDw8IC3tzccHR2xefNm+biAgABUVlbin//8pzwOS5YswZ/+9CcAjxKVWq3Gvn375ERoaWmJ9PR0eHh4oKamBt27d8dnn32G999/HwcOHICfnx8KCwvRo0cPAI9mQaNGjZLHt6k+NGb06NGwt7eXZymNXWNYtmwZMjIy8MMPP8hlV69ehaWlJfLz83Hv3j24ubmhsLAQPXv2fOqYr1ixAitXrmxQvgiL+DwGatOe9jyGiooKGBoaory8/KnPN38ht6sOGDBA2M7Ly4Onp6fw0Glvb2/cu3cPV69elcucnZ3lf2tqasLY2BhOTk5ymZmZGQA0OQPIy8uDlpaWcH47O7tm7+DJzs7G5cuXoVaroVKpoFKp0KVLFzx48EBY1nke9TOGphQUFGDq1KmwtrZGp06d0KtXLwBAUVGRUO/J8QTQZD/z8vIAPBoLb29v4Rhvb295f73Hx9zAwABqtVoeXwsLC4wePRpbt24FAOzduxcPHjzAxIkT5XO88cYbclIAAE9Pz0b7+mQfamtr8emnn8LZ2RnGxsZQqVQ4cOBAg74/KTs7G0eOHJF/ViqVCnZ2dgAejaeLiwuGDh0KJycnTJw4EX/5y1/kpbvGREVFoby8XH49uRxHRC/oCW4GBgbCtiRJQlKoLwMglD95QVKhUAhl9XWfXGpprs2nqaurg5ubW6PXIkxNTVvcTmP09PSa3T927FhYWlriL3/5C7p164a6ujr069cP1dXVQr0nx7NeY/18vKyxMX+yrLExf3x8Z86cicDAQKxfvx5xcXGYNGkS9PX15fZaElNjffjiiy+wfv16REdHw8nJCQYGBggPD2/Q9yfV1dVh7Nix+N///d8G+ywsLKCpqYmDBw8iPT0dBw4cwFdffYXFixcjIyNDTryPUyqVUCqVzZ6TqKN7KX/g5uDggPT0dOGNJD09HWq1Gt27d39h57G3t8fDhw9x6tQpuSw/Px937txp8hhXV1dcunQJXbt2hY2NjfAyNDQEAOjo6MjXBZ6Fs7MzDh061Oi+W7duIS8vD0uWLMHQoUNhb2/f7CfbJzXVz/pPz/b29g2WsNLT02Fvb/9MffDz84OBgQFiY2Oxf/9+BAcHy/scHBxQVFSE69evy2UnTpxoUbvHjh2Dv78/3nvvPbi4uMDa2hqXLl0S6jQ27q6urvjXv/4FKyurBj+v+uSjUCjg7e2NlStX4vTp09DR0cGuXbueqd9E9F8vJTHMmzcPxcXFCA0NxcWLF5GUlITly5cjIiICGhov7pS2trYYOXIkQkJCkJGRgezsbMycObPZT+7Tpk2DiYkJ/P39cezYMVy5cgWpqakICwuTl7msrKyQm5uL/Px8lJWVybeMDh06FBs3bmyy7aioKGRlZWHevHnIzc3FxYsXERsbi7KyMvkOqM2bN+Py5cs4fPgwIiIiWtxXbW1thIaGIiMjAzk5OZgxYwYGDhwIDw8PAMDHH3+M+Ph4bNq0CZcuXcKf//xn7Ny5E5GRkS0+B/BoSS8oKAhRUVGwsbERloqGDRsGW1tbTJ8+HWfPnsWxY8ewePHiFrVrY2Mjf7LPy8vD7NmzcePGDaGOlZUVMjIyUFhYiLKyMtTV1WH+/Pm4ffs2pkyZgszMTPznP//BgQMHEBwcjNraWmRkZOB//ud/cOrUKRQVFWHnzp24efPmMydEIvqvl5IYunfvjn379iEzMxMuLi6YM2cOPvjgAyxZsuSFnysuLg6WlpYYPHgwxo8fj1mzZqFr165N1tfX18fRo0fxxhtvYPz48bC3t0dwcDB+/fVX+YJMSEgIbG1tMWDAAJiamiItLQ3AozXtx2+TfFLfvn1x4MABnD17Fh4eHvD09ERSUhK0tLSgoaGBxMREZGdno1+/fvjjH/+Izz77rMX91NfXx8KFCzF16lR4enpCT08PiYmJ8v5x48Zhw4YN+Oyzz+Do6IhvvvkGcXFx8PHxafE56n3wwQeorq4WZgsAoKGhgV27dqGqqgoeHh6YOXMmPv300xa1uXTpUri6usLX1xc+Pj4wNzfHuHHjhDqRkZHQ1NSEg4MDTE1NUVRUhG7duiEtLQ21tbXw9fVFv379EBYWBkNDQ2hoaKBTp044evQo/Pz80LdvXyxZsgRffPEFRo0a9cz9JqJHnumuJOoY0tLS4OPjg6tXr8o3ALRX9Xdq8K4kaute5F1JL+TiM7UPVVVVKC4uxtKlSxEQENDukwIRNY7frkqy7du3w9bWFuXl5Vi3bl1rh0NErYSJgWRBQUGora1Fdnb2C717jIjaFiYGIiISMDEQEZGAiYGIiARMDEREJGBiICIiARMDEREJmBiIiEjAv3wmAhBVHvXUrwkg6ig4YyAiIgETAxERCZgYiIhIwMRAREQCJgYiIhIwMRARkYCJgYiIBPw7BiIAawzXvNRHez7tsYtErxPOGIiISMDEQEREAiYGIiISMDEQEZGAiYGIiARMDEREJGBiICIiARMDEREJmBiIiEjAxEBERIJ2nRgkScKsWbPQpUsXKBQKnDlzprVDapSVlRWio6PlbYVCgd27d7daPK1pxYoV6N+/f2uHQdShtevEkJycjPj4eOzduxclJSXo169fa4fUIiUlJRg1alSL6wcFBWHcuHEvL6Dn8Lxv8JGRkTh06NCLD4iIWqxdf4leQUEBLCws4OXl1WSd6upq6OjovMKons7c3Ly1Q2hSTU0NtLW1X1r7KpUKKpXqpbVPRE/XbmcMQUFBCA0NRVFRERQKBaysrAAAPj4+WLBgASIiImBiYoLhw4cDAFJTU+Hh4QGlUgkLCwssWrQIDx8+lNvz8fFBaGgowsPDYWRkBDMzM2zevBmVlZWYMWMG1Go1evfujf379zcbV2lpKcaOHQs9PT306tUL3377bYM6Ty4lXbt2DZMmTYKRkRGMjY3h7++PwsJCAI8+mSckJCApKQkKhQIKhQIpKSktGqP4+Hh07twZu3fvRt++faGrq4vhw4ejuLhYrlP/yX/r1q2wtraGUqmEJEkoKiqCv78/VCoVOnXqhICAAPz8889yuytXrsTZs2flmOLj4wEA5eXlmDVrFrp27YpOnTrhnXfewdmzZxucr179bOjzzz+HhYUFjI2NMX/+fNTU1Mh1YmJi0KdPH+jq6sLMzAwTJkxoUf+JqHHtNjFs2LABq1atQo8ePVBSUoKsrCx5X0JCArS0tJCWloZvvvkG165dg5+fH9zd3XH27FnExsZiy5YtWL16tdBmQkICTExMkJmZidDQUMydOxcTJ06El5cXcnJy4Ovri8DAQNy/f7/JuIKCglBYWIjDhw9jx44diImJQWlpaZP179+/jyFDhkClUuHo0aM4fvw4VCoVRo4cierqakRGRiIgIAAjR45ESUkJSkpKmp0hNdb+p59+ioSEBKSlpaGiogKTJ08W6ly+fBnfffcdvv/+e/k6zbhx43D79m2kpqbi4MGDKCgowKRJkwAAkyZNwkcffQRHR0c5pkmTJkGSJIwePRo3btzAvn37kJ2dDVdXVwwdOhS3b99uMsYjR46goKAAR44cQUJCAuLj4+VEc+rUKXz44YdYtWoV8vPzkZycjEGDBjXZVlVVFSoqKoQXEYna7VKSoaEh1Go1NDU1GyzN2NjYYN26dfL24sWLYWlpiY0bN0KhUMDOzg7Xr1/HwoULsWzZMmhoPMqfLi4uWLJkCQAgKioKa9euhYmJCUJCQgAAy5YtQ2xsLHJzczFw4MAGMf373//G/v37cfLkSbz11lsAgC1btsDe3r7JfiQmJkJDQwN//etfoVAoAABxcXHo3LkzUlJSMGLECOjp6aGqquq5lqBqamqwceNGOZ6EhATY29sjMzMTHh4eAB4tt23btg2mpqYAgIMHDyI3NxdXrlyBpaUlAGDbtm1wdHREVlYW3N3doVKpoKWlJcR0+PBhnDt3DqWlpVAqlQCAzz//HLt378aOHTswa9asRmM0MjLCxo0boampCTs7O4wePRqHDh1CSEgIioqKYGBggDFjxkCtVqNnz5548803m+zvmjVrsHLlymceJ6KOpN3OGJozYMAAYTsvLw+enp7yGy8AeHt74969e7h69apc5uzsLP9bU1MTxsbGcHJyksvMzMwAoMkZQF5eHrS0tITz29nZoXPnzk3Gmp2djcuXL0OtVsvr7126dMGDBw9QUFDQsg43o6l48vLy5LKePXvKSaG+H5aWlnJSAAAHB4cGxzXWl3v37sHY2Fjui0qlwpUrV5rti6OjIzQ1NeVtCwsLeYyHDx+Onj17wtraGoGBgfj222+bnbFFRUWhvLxcfj2+bEZEj7TbGUNzDAwMhG1JkoSkUF8GQCh/8qKrQqEQyurr1tXVNXrextp8mrq6Ori5uTV6LeLxN+vforF4Hi9ryXg1V16vrq4OFhYWjV4DaS45Njbu9WOsVquRk5ODlJQUHDhwAMuWLcOKFSuQlZXVaJtKpVKerRBR4zrkjOFJDg4OSE9Pl9+4ASA9PR1qtRrdu3d/Yeext7fHw4cPcerUKbksPz8fd+7cafIYV1dXXLp0CV27doWNjY3wMjQ0BADo6Oigtrb2uWJqKh47O7smj3FwcEBRUZHwafvChQsoLy+Xl8Uai8nV1RU3btyAlpZWg76YmJg8V/zAo1nPsGHDsG7dOuTm5srXcIjo+TAxAJg3bx6Ki4sRGhqKixcvIikpCcuXL0dERIR8feFFsLW1xciRIxESEoKMjAxkZ2dj5syZ0NPTa/KYadOmwcTEBP7+/jh27BiuXLmC1NRUhIWFyctcVlZWyM3NRX5+PsrKyuQ7doYOHYqNGzc2G5O2tjZCQ0ORkZGBnJwczJgxAwMHDpSvLzRm2LBhcHZ2xrRp05CTk4PMzExMnz4dgwcPlpelrKyscOXKFZw5cwZlZWWoqqrCsGHD4OnpiXHjxuGHH35AYWEh0tPTsWTJEiE5PYu9e/fiyy+/xJkzZ/DTTz/hb3/7G+rq6mBra/tc7REREwMAoHv37ti3bx8yMzPh4uKCOXPm4IMPPpAvNL9IcXFxsLS0xODBgzF+/Hj51s2m6Ovr4+jRo3jjjTcwfvx42NvbIzg4GL/++is6deoEAAgJCYGtrS0GDBgAU1NTpKWlAXj0dxxlZWXNxqOvr4+FCxdi6tSp8PT0hJ6eHhITE5s9pv52WiMjIwwaNAjDhg2DtbU1/vGPf8h1/vCHP2DkyJEYMmQITE1NsX37digUCuzbtw+DBg1CcHAw+vbti8mTJ6OwsFC+PvOsOnfujJ07d+Kdd96Bvb09Nm3ahO3bt8PR0fG52iMiQCE9vn5CHUp8fDzCw8ObXcpq7yoqKmBoaIhFWARd6L608yyXlr+0tolaov53vby8XP5Q2RTOGIiISMDEQEREAiaGDiwoKKhDLyMRUeOYGIiISMDEQEREAiYGIiISMDEQEZGAiYGIiARMDEREJGBiICIiQYf82m2iJ0WVRz31awKIOgrOGIiISMDEQEREAiYGIiISMDEQEZGAiYGIiARMDEREJGBiICIiAf+OgQjAGsM1L/TRnnyUJ7VlnDEQEZGAiYGIiARMDEREJGBiICIiARMDEREJmBiIiEjAxEBERAImBiIiEjAxEBGRgImBiIgETAyvIUmSMGvWLHTp0gUKhQJnzpxp7ZCIqAPhdyW9hpKTkxEfH4+UlBRYW1vDxMSktUMiog6EieE1VFBQAAsLC3h5eTVZp7q6Gjo6Oq8wKiLqKLiU9JoJCgpCaGgoioqKoFAoYGVlBQDw8fHBggULEBERARMTEwwfPhwAkJqaCg8PDyiVSlhYWGDRokV4+PCh3J6Pjw9CQ0MRHh4OIyMjmJmZYfPmzaisrMSMGTOgVqvRu3dv7N+/v9m4rKyssHr1akyfPh0qlQo9e/ZEUlISbt68CX9/f6hUKjg5OeHUqVPyMStWrED//v2FdqKjo+U+1du6dSscHR3lPixYsAAAUFhY2GAp7c6dO1AoFEhJSQEA/PLLL5g2bRpMTU2hp6eHPn36IC4u7hlGnIiexMTwmtmwYQNWrVqFHj16oKSkBFlZWfK+hIQEaGlpIS0tDd988w2uXbsGPz8/uLu74+zZs4iNjcWWLVuwevVqoc2EhASYmJggMzMToaGhmDt3LiZOnAgvLy/k5OTA19cXgYGBuH//frOxrV+/Ht7e3jh9+jRGjx6NwMBATJ8+He+99x5ycnJgY2OD6dOnQ5KkFvc3NjYW8+fPx6xZs3Du3Dns2bMHNjY2LT5+6dKluHDhAvbv34+8vDzExsZy6Y3oN+JS0mvG0NAQarUampqaMDc3F/bZ2Nhg3bp18vbixYthaWmJjRs3QqFQwM7ODtevX8fChQuxbNkyaGg8yvsuLi5YsmQJACAqKgpr166FiYkJQkJCAADLli1DbGwscnNzMXDgwCZj8/Pzw+zZs4Vj3N3dMXHiRADAwoUL4enpiZ9//rlB7E1ZvXo1PvroI4SFhcll7u7uLToWAIqKivDmm29iwIABANBgNvKkqqoqVFVVydsVFRUtPhdRR8EZQxtS/+ZXLy8vD56enlAoFHKZt7c37t27h6tXr8plzs7O8r81NTVhbGwMJycnuczMzAwAUFpa2uz5H2+n/pjnaadeaWkprl+/jqFDh7aofmPmzp2LxMRE9O/fH5988gnS09Obrb9mzRoYGhrKL0tLy+c+N1F7xcTQhhgYGAjbkiQJSaG+DIBQrq2tLdRRKBRCWX3durq6Zs/f2DHNtaOhodFgWammpkb+t56eXrPnq5/xPN7G48cDwKhRo/DTTz8hPDxcTjKRkZFNthkVFYXy8nL5VVxc3GwMRB0RE0Mb5uDggPT0dOGNMz09HWq1Gt27d2/FyB4xNTXFjRs3hPgev5CsVqthZWWFQ4cONXk8AJSUlDR6/OP1goKC8Pe//x3R0dHYvHlzkzEplUp06tRJeBGRiNcY2rB58+YhOjoaoaGhWLBgAfLz87F8+XJERETIn7Zbk4+PD27evIl169ZhwoQJSE5Oxv79+4U34xUrVmDOnDno2rUrRo0ahbt37yItLQ2hoaHQ09PDwIEDsXbtWlhZWaGsrEy+VlJv2bJlcHNzg6OjI6qqqrB3717Y29u/6q4StSut/+5Bz6179+7Yt28fMjMz4eLigjlz5uCDDz5o8ObZWuzt7RETE4Ovv/4aLi4uyMzMbLDM8/777yM6OhoxMTFwdHTEmDFjcOnSJXn/1q1bUVNTgwEDBiAsLKzBHVc6OjqIioqCs7MzBg0aBE1NTSQmJr6S/hG1VwrpWe4tJGpnKioqYGhoiEVYBF3ovrB2l0vLX1hbRC9C/e96eXn5U5dQOWMgIiIBEwMREQmYGIiISMDEQEREAiYGIiISMDEQEZGAiYGIiARMDEREJGBiICIiARMDEREJ+CV6RACiyqP4TatE/48zBiIiEjAxEBGRgImBiIgETAxERCTgxWfq0OofR1JRUdHKkRC9XPW/4y15BA8TA3Vot27dAgBYWlq2ciREr8bdu3dhaGjYbB0mBurQunTpAgAoKip66n8W+q+KigpYWlqiuLiYt/k+o9YaO0mScPfuXXTr1u2pdZkYqEPT0Hh0mc3Q0JBvcM+hU6dOHLfn1Bpj19IPP7z4TEREAiYGIiISMDFQh6ZUKrF8+XIolcrWDqVN4bg9v7YwdgqpJfcuERFRh8EZAxERCZgYiIhIwMRAREQCJgbqsGJiYtCrVy/o6urCzc0Nx44da+2QWtWaNWvg7u4OtVqNrl27Yty4ccjPzxfqSJKEFStWoFu3btDT04OPjw/+9a9/CXWqqqoQGhoKExMTGBgY4Pe//z2uXr36KrvSqtasWQOFQoHw8HC5rK2NGxMDdUj/+Mc/EB4ejsWLF+P06dN4++23MWrUKBQVFbV2aK0mNTUV8+fPx8mTJ3Hw4EE8fPgQI0aMQGVlpVxn3bp1+POf/4yNGzciKysL5ubmGD58OO7evSvXCQ8Px65du5CYmIjjx4/j3r17GDNmDGpra1ujW69UVlYWNm/eDGdnZ6G8zY2bRNQBeXh4SHPmzBHK7OzspEWLFrVSRK+f0tJSCYCUmpoqSZIk1dXVSebm5tLatWvlOg8ePJAMDQ2lTZs2SZIkSXfu3JG0tbWlxMREuc61a9ckDQ0NKTk5+dV24BW7e/eu1KdPH+ngwYPS4MGDpbCwMEmS2ua4ccZAHU51dTWys7MxYsQIoXzEiBFIT09vpaheP+Xl5QD++31SV65cwY0bN4RxUyqVGDx4sDxu2dnZqKmpEep069YN/fr1a/djO3/+fIwePRrDhg0TytviuPG7kqjDKSsrQ21tLczMzIRyMzMz3Lhxo5Wier1IkoSIiAj87ne/Q79+/QBAHpvGxu2nn36S6+jo6MDIyKhBnfY8tomJicjJyUFWVlaDfW1x3JgYqMNSKBTCtiRJDco6qgULFiA3NxfHjx9vsO95xq09j21xcTHCwsJw4MAB6OrqNlmvLY0bl5KowzExMYGmpmaDT2KlpaUNPtV1RKGhodizZw+OHDmCHj16yOXm5uYA0Oy4mZubo7q6Gr/88kuTddqb7OxslJaWws3NDVpaWtDS0kJqaiq+/PJLaGlpyf1uS+PGxEAdjo6ODtzc3HDw4EGh/ODBg/Dy8mqlqFqfJElYsGABdu7cicOHD6NXr17C/l69esHc3FwYt+rqaqSmpsrj5ubmBm1tbaFOSUkJzp8/327HdujQoTh37hzOnDkjvwYMGIBp06bhzJkzsLa2bnvj9sovdxO9BhITEyVtbW1py5Yt0oULF6Tw8HDJwMBAKiwsbO3QWs3cuXMlQ0NDKSUlRSopKZFf9+/fl+usXbtWMjQ0lHbu3CmdO3dOmjJlimRhYSFVVFTIdebMmSP16NFD+vHHH6WcnBzpnXfekVxcXKSHDx+2RrdaxeN3JUlS2xs3JgbqsL7++mupZ8+eko6OjuTq6irfltlRAWj0FRcXJ9epq6uTli9fLpmbm0tKpVIaNGiQdO7cOaGdX3/9VVqwYIHUpUsXSU9PTxozZoxUVFT0invTup5MDG1t3PjtqkREJOA1BiIiEjAxEBGRgImBiIgETAxERCRgYiAiIgETAxERCZgYiIhIwMRAREQCJgYiAgDk5+fD3NxceKrY68TKygrR0dHPdEx8fDw6d+78QuOIjIzEhx9++ELbfN0wMRC1MUFBQVAoFA1ely9fbrBfW1sb1tbWiIyMFB7R2ZjFixdj/vz5UKvVr6IbL0yvXr2QnJz8ys73ySefIC4uDleuXHll53zVmBiI2qCRI0eipKREeD3+baj1+//zn/9g9erViImJQWRkZJPtXb16FXv27MGMGTNeRfgvTG5uLm7duoUhQ4a8snN27doVI0aMwKZNm17ZOV81JgaiNkipVMLc3Fx4aWpqNthvaWmJqVOnYtq0adi9e3eT7X333XdwcXERnr9Qvwyzd+9e2NraQl9fHxMmTEBlZSUSEhJgZWUFIyMjhIaGCg+st7KywurVqzF9+nSoVCr07NkTSUlJuHnzJvz9/aFSqeDk5IRTp04JMXz//fdwdHSEUqmElZUVvvjii6eOQ1JSEnx9faFUKuWY33jjDejr6+Pdd9/FrVu3hPoFBQXw9/eHmZkZVCoV3N3d8eOPP8r7V61aBScnpwbncXNzw7Jly+Tt3//+99i+fftT42urmBiIOgA9PT3U1NQ0uf/o0aMYMGBAg/L79+/jyy+/RGJiIpKTk5GSkoLx48dj37592LdvH7Zt24bNmzdjx44dwnHr16+Ht7c3Tp8+jdGjRyMwMBDTp0/He++9h5ycHNjY2GD69Omo/w7P7OxsBAQEYPLkyTh37hxWrFiBpUuXIj4+vtl+7dmzB/7+/gCAjIwMBAcHY968eThz5gyGDBmC1atXC/Xv3bsHPz8//Pjjjzh9+jR8fX0xduxYFBUVAQCCg4Nx4cIF4RGdubm5OH36NIKCguQyDw8PFBcXy4/mbHda5Ttdiei5vf/++5KmpqZkYGAgvyZMmCDs9/f3l7czMjIkY2NjKSAgoMk2XVxcpFWrVgllcXFxEgDp8uXLctns2bMlfX196e7du3KZr6+vNHv2bHm7Z8+e0nvvvSdvl5SUSACkpUuXymUnTpyQAEglJSWSJEnS1KlTpeHDhwvn//jjjyUHBweh3fXr18vbV69elbS1taVbt25JkiRJU6ZMkUaOHCm0MWnSJMnQ0LDJfkuSJDk4OEhfffWVvD1q1Chp7ty58nZ4eLjk4+MjHFNeXi4BkFJSUpptu63ijIGoDRoyZIjwxLAvv/xS2L93716oVCro6urC09MTgwYNwldffdVke7/++mujzyvW19dH79695W0zMzNYWVlBpVIJZaWlpcJxzs7Own4AwhJNfVn9cXl5efD29hba8Pb2xqVLl4Rlqsft2bMH3t7e6NKli9yGp6enUOfJ7crKSnzyySdwcHBA586doVKpcPHiRXnGAAAhISHYvn07Hjx4gJqaGnz77bcIDg4W2tHT0wPwaEbVHmm1dgBE9OwMDAxgY2PT5P4hQ4YgNjYW2tra6NatG7S1tZttz8TEpMHzhgE0OK7+Tqcny+rq6po8rv5h9o2V1R8nNfLQe+kpj4p5fBmpJfUB4OOPP8YPP/yAzz//HDY2NtDT08OECRNQXV0t1xk7diyUSiV27doFpVKJqqoq/OEPfxDauX37NgDA1NT0qedsi5gYiNqhpyWOJ7355pu4cOHCS4yoeQ4ODjh+/LhQlp6ejr59+woX1evdu3cPR44cwddffy20cfLkSaHek9vHjh1DUFAQ3n33XbmdwsJCoY6Wlhbef/99xMXFQalUYvLkydDX1xfqnD9/Htra2nB0dHzmvrYFTAxEBF9fX8ycORO1tbWNvhG/bB999BHc3d3xpz/9CZMmTcKJEyewceNGxMTENFo/OTkZffr0gbW1tVz24YcfwsvLC+vWrcO4ceNw4MCBBn/fYGNjg507d2Ls2LFQKBRYunRpg9kOAMycORP29vYAgLS0tAb7jx07hrffflteUmpveI2BiODn5wdtbW3h1s1XydXVFd999x0SExPRr18/LFu2DKtWrRLuBHpcUlKSsIwEAAMHDsRf//pXfPXVV+jfvz8OHDiAJUuWCHXWr18PIyMjeHl5YezYsfD19YWrq2uD9vv06QMvLy/Y2trirbfearB/+/btCAkJef4Ov+b4zGciAgDExMQgKSkJP/zwQ2uH0qza2lp07doV+/fvh4eHx0s5hyRJsLOzw+zZsxERESHs++c//4mPP/4Yubm50NJqn4su7bNXRPTMZs2ahV9++QV37959rb8W49atW/jjH/8Id3f3l9J+aWkptm3bhmvXrjX6l+CVlZWIi4trt0kB4IyBiEigUChgYmKCDRs2YOrUqa0dTqtovymPiOg58LMyLz4TEdETmBiIiEjAxEBERAImBiIiEjAxEBGRgImBiIgETAxERCRgYiAiIgETAxERCf4PmyGWZT40WA4AAAAASUVORK5CYII=", "text/plain": ["<Figure size 400x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 230x350 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAANwAAAFUCAYAAABPzlKAAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAABEC0lEQVR4nO3deVyM6/8/8NekRaSIFqnsWZJ0ShLFyBZCtsi+5tjL4ehYsh37sXRSEY4lS8fJwYfsS8gadTiOXSotkqV9n/fvj35zfxstahqzuZ6PR4+<PERSON><PERSON>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", "text/plain": ["<Figure size 230x350 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# add mucus and protein digestion\n", "\n", "BRD[\"proteins_LI\"]=7\n", "#2. mucus reaching large intestine (from small intestine and sheddeing along LI)\n", "BRD[\"mucus\"]=4.4\n", "#3. calculate protein and carhobhydrate content\n", "BRD[\"mucus_protein\"]=BRD[\"mucus\"]*0.2\n", "BRD[\"mucus_CH\"]=BRD[\"mucus\"]*0.8\n", "\n", "energyfromc_x,fplistfromc_c,FPfromc_withprot,fpfromc_c_g,fpfromc_c_g_sum,cc,order,drymass=FPcalc.energycalc(BRD[\"carbLI_standard\"],scenario='reference',calctype='from_carbs',dict_yielddata=dict_yielddata)\n", "FPfromc_withprot=FPfromc_withprot+1*BRD[\"proteins_LI\"]/0.110 #convert into AA\n", "\n", "energyfromc_x,fplistfromc_c,FPfromc_withmucus,fpfromc_c_g,fpfromc_c_g_sum,cc,order,drymass=FPcalc.energycalc(BRD[\"carbLI_standard\"]+BRD[\"mucus_CH\"],scenario='reference',calctype='from_carbs',dict_yielddata=dict_yielddata)\n", "FPfromc_withmucus=FPfromc_withmucus+1*BRD[\"mucus_protein\"]/0.110\n", "\n", "energyfromc_x,fplistfromc_c,FPfromc_withmucus_and_protein,fpfromc_c_g,fpfromc_c_g_sum,cc,order,drymass=FPcalc.energycalc(BRD[\"carbLI_standard\"]+BRD[\"mucus_CH\"],scenario='reference',calctype='from_carbs',dict_yielddata=dict_yielddata)\n", "FPfromc_withmucus_and_protein=FPfromc_withmucus_and_protein+1*(BRD[\"proteins_LI\"])/0.110\n", "\n", "######################\n", "#plot different estimations (w/o and w mucus and proteins etc)\n", "###################\n", "\n", "#horizontal bars, comparison from mucus and from \n", "fig, axs = plt.subplots(1,1, figsize=(4,2))\n", "axs=[axs]\n", "\n", "print(BRD[\"FP_fromcarbs_standard\"])\n", "print(FPfromc_withprot)\n", "print(FPfromc_withmucus)\n", "\n", "axs[0].barh([3,2,1],[BRD[\"FP_fromcarbs_standard\"],FPfromc_withprot-BRD[\"FP_fromcarbs_standard\"],FPfromc_withmucus-BRD[\"FP_fromcarbs_standard\"]],color=3*[colorrefdiet],capsize=capsize)\n", "  \n", "#databox,labels=[\"bacteria\"],vert=True,showfliers=True, widths=[0.7]*1,patch_artist=True,)#,\"Mi<PERSON> (autoclaved lab coy)\"])\n", "print(\"from carbs:\")\n", "print(BRD[\"FP_fromcarbs_standard\"])\n", "print(\"from prot:\")\n", "print(FPfromc_withprot-BRD[\"FP_fromcarbs_standard\"])\n", "print(\"from mucus:\")\n", "print(FPfromc_withmucus-BRD[\"FP_fromcarbs_standard\"])\n", "axs[0].set_xlabel(\"FP (mmol/day)\")\n", "axs[0].set_yticks([3,2,1])\n", "axs[0].set_yticklabels([\"from diet. carbohydrates\",\"from diet. proteins\",\"from mucus\"])#,rotation=50)\n", "#axs[0].set_ylim(0,280)\n", "\n", "fig.tight_layout()\n", "fig.savefig(\"plot_output_figure2/BRITISH_carbLI_ferm_comp_mucus_and_proteins_2.pdf\")\n", "\n", "\n", "############\n", "#other older plots\n", "###########\n", "fig, axs = plt.subplots(1,1, figsize=(2.3,3.5))\n", "axs=[axs]\n", "\n", "axs[0].barh([1,2,3,4],[BRD[\"FP_fromcarbs_standard\"],FPfromc_withprot,FPfromc_withmucus,FPfromc_withmucus_and_protein],color=4*[colorrefdiet],capsize=capsize)\n", "  \n", "#databox,labels=[\"bacteria\"],vert=True,showfliers=True, widths=[0.7]*1,patch_artist=True,)#,\"Mi<PERSON> (autoclaved lab coy)\"])\n", "\n", "axs[0].set_ylabel(\"FP (mmol/day)\")\n", "axs[0].set_xticks([1,2,3,4])\n", "axs[0].set_xticklabels([\"no prot & mucus\",\"w/ diet. prot.\",\"w/ mucus\",\"w/ mucus and protein\"],rotation=50)\n", "#axs[0].set_ylim(0,280)\n", "\n", "fig.tight_layout()\n", "fig.savefig(\"plot_output_figure2/BRITISH_carbLI_ferm_comp_mucus_and_proteins.pdf\")\n", "\n", "\n", "\n", "fig, axs = plt.subplots(1,1, figsize=(2.3,3.5))\n", "axs=[axs]\n", "\n", "axs[0].bar([1,2,3,4],[BRD[\"FP_fromcarbs_standard\"]/BRD[\"FP_fromcarbs_standard\"],FPfromc_withprot/BRD[\"FP_fromcarbs_standard\"],FPfromc_withmucus/BRD[\"FP_fromcarbs_standard\"],FPfromc_withmucus_and_protein/BRD[\"FP_fromcarbs_standard\"]],color=4*[colorrefdiet],capsize=capsize)\n", "  \n", "#databox,labels=[\"bacteria\"],vert=True,showfliers=True, widths=[0.7]*1,patch_artist=True,)#,\"Mi<PERSON> (autoclaved lab coy)\"])\n", "\n", "axs[0].set_ylabel(\"FP (relative scale)\")\n", "axs[0].set_xticks([1,2,3,4])\n", "axs[0].set_xticklabels([\"no prot & mucus\",\"w/ diet. prot.\",\"w/ mucus\",\"w/ mucus and protein\"],rotation=50)\n", "axs[0].set_ylim(0.9,1.3)\n", "\n", "fig.tight_layout()\n", "fig.savefig(\"plot_output_figure2/BRITISH_carbLI_ferm_comp_mucus_and_proteins_foldchange.pdf\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "## Fig. S2: Add figure how \"carbs available for microbiota\" changes with fiber digestion fraction and starch absorbance\n", "--> Standard: 0.5 fiber digestion, 0.1 starch passage #Englyst. Fiber unclear, but we show that it does not matter much for British ref diet\n", "e.g. panel: vary fiber digestion fraction on x-axis (show different lines for starch passage (0.05,0.1,0.15)\n", "possible: add other panel showing how total FPs change with fiber digestion fraction\n", "\n"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'energy': 2275.0, 'carbohydrates': 276.75, 'sugars': 59.0, 'proteins': 72.05000000000001, 'fat': 105.5, 'fiber': 19.9, 'fiber_low': 14.599999999999998, 'fiber_high': 25.2, 'carbLI_standard': 35.670500000000004, 'carbLI_higher': 44.6025, 'carbLI_lower': 25.755000000000003, 'carbLI_error': 9.423749999999998, 'bacwetweight': 117.72413793103448, 'fecalwetmass': 117.72413793103448, 'fecaldrymass': 29.58620689655172, 'fecaldrymassstd': 6.845624030794191, 'fecealfractionbac': 0.546888888888889, 'bacterialdrymass_feces': 16.180367816091955, 'energybacteria_fromfeces': np.float64(101.71752675768855), 'FP_fromfeces': np.float64(467.3659285620218), 'FP_fromfeces_g': [8.035607499560594, 2.7046488116402316, 6.192333401520376, 9.51608312429447, 1.6695716933854843, 4.741614607512687], 'FP_fromfeces_gsum': np.float64(32.859859137913844), 'energyfrac_fromfeces': np.float64(0.04471100077261035), 'energybacteria_fromfeces_error': np.float64(23.53528953407475), 'energyfrac_fromfeces_error': np.float64(0.01034518221278011), 'FP_fromfeces_error': np.float64(108.13861482566425), 'bacterialdrymass_feces_error': 3.743795719952113, 'energy_fromcarbs_standard': np.float64(98.74730465043015), 'FPlist_fromcarbs_standard': [129.903453754712, 29.799922782745863, 130.5998910474014, 102.55558301278339, 21.8796021223617, 38.98006759839011], 'FP_fromcarbs_standard': np.float64(453.7185203183944), 'FP_fromcarbs_g_standard': [7.8009622048779645, 2.6256711963877377, 6.011512984911887, 9.238206917791528, 1.6208190456224323, 4.603156182693888], 'FP_fromcarbs_gsum_standard': np.float64(31.900328532285435), 'ferm_fromcarbs_cc_standard': np.float64(1038.8323154868044), 'drymass_fromcarbs_standard': 15.707889889004568, 'energyfrac_fromcarbs_standard': np.float64(0.04340540863755171), 'energy_fromcarbs_lower': np.float64(71.29804267593188), 'FPlist_fromcarbs_lower': [93.7935675544948, 21.51629529357928, 94.29641283205514, 74.04771563320492, 15.797624161742212, 28.14459121673476], 'FP_fromcarbs_lower': np.float64(327.59620669181106), 'FP_fromcarbs_g_lower': [5.632491318782521, 1.8958007783172701, 4.340463882659498, 6.6702182242390995, 1.170272200277701, 3.323594776784208], 'FP_fromcarbs_gsum_lower': np.float64(23.0328411810603), 'ferm_fromcarbs_cc_lower': np.float64(750.0631133671424), 'drymass_fromcarbs_lower': 11.341492384219807, 'energy_fromcarbs_higher': np.float64(123.47392539131242), 'FPlist_fromcarbs_higher': [162.43166751502054, 37.261912670622, 163.3024947909819, 128.2358080578537, 27.358320002877377, 48.74079323410646], 'FP_fromcarbs_higher': np.float64(567.3309962714619), 'FP_fromcarbs_g_higher': [9.754346497612014, 3.2831471254085045, 7.516813835228898, 11.551481589851461, 2.026676987493153, 5.755800273015632], 'FP_fromcarbs_gsum_higher': np.float64(39.88826630860966), 'ferm_fromcarbs_cc_higher': np.float64(1298.95903762213), 'drymass_fromcarbs_higher': 19.641192547744666, 'energyfrac_fromcarbs_lower': np.float64(0.031339798978431595), 'energyfrac_fromcarbs_higher': np.float64(0.054274252919258206), 'FP_fromcarbs_standard_error': np.float64(119.86739478982543), 'FP_fromcarbs_gsum_standard_error': np.float64(8.427712563774682), 'drymass_fromcarbs_standard_error': 4.1498500817624295, 'energy_fromcarbs_standard_error': np.float64(26.087941357690273), 'energyfrac_fromcarbs_standard_error': np.float64(0.011467226970413306), 'FP_infeces': 9.041213793103447, 'FP_infeces_error': 9.41793103448276, 'proteins_LI': 7, 'mucus': 4.4, 'mucus_protein': 0.8800000000000001, 'mucus_CH': 3.5200000000000005}\n"]}], "source": ["print(BRD)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Plots total amount of fermentation products reaching large intestine\n", "\n", "For Fig. S\n", "add as a side point how little of those fermentation products are recovered in feces\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# main results British reference diet in one plot"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 400x300 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, axs = plt.subplots(3,2, figsize=(4,3))\n", "\n", "\n", "####from carb calculation\n", "\n", "#carbs\n", "axs[0,1].barh(1,BRD[\"carbLI_standard\"],height=0.7,xerr=BRD[\"carbLI_error\"],color=[colorrefdiet,colorrefdiet],capsize=capsize)\n", "axs[0,1].set_xlabel(\"microbiota digestible\\n carbs (g/day)\")\n", "#bacterial dry mass\n", "axs[1,1].barh(1,BRD[\"drymass_fromcarbs_standard\"],height=0.7,xerr=BRD[\"drymass_fromcarbs_standard_error\"],color=[colorrefdiet,colorrefdiet],capsize=capsize)\n", "axs[1,1].set_xlabel(\"bacterial dry \\n weight (g/day)\")\n", "#femrentation products\n", "axs[2,1].barh(1,BRD[\"FP_fromcarbs_standard\"],height=0.7,xerr=BRD[\"FP_fromcarbs_standard_error\"],color=[colorrefdiet,colorrefdiet],capsize=capsize)\n", "axs[2,1].set_xlabel(\"fermentation \\n products (mmol/day\")\n", "\n", "#colors=[colorrefdiet]*2\n", "#for patch, color in zip(bplot1['boxes'], colors):\n", "#        patch.set_facecolor(color)\n", "#axs[0].set_xlabel('bacterial dry mass (g/day)')\n", "#axs[0].set_xlim(0,30)\n", "\n", "####from feces calculation\n", "\n", "#########\n", "#feces\n", "#########\n", "\n", "axs[0,0].barh(1,BRD[\"fecaldrymass\"],height=0.7,xerr=BRD[\"fecaldrymassstd\"],color=[colorrefdiet,colorrefdiet],capsize=capsize)\n", "axs[0,0].set_xlabel(\"fecal dry weight (g/day)\")\n", "#bacterial dry mass\n", "axs[1,0].barh(1,BRD[\"bacterialdrymass_feces\"],height=0.7,xerr=BRD[\"bacterialdrymass_feces_error\"],color=[colorrefdiet,colorrefdiet],capsize=capsize)\n", "axs[1,0].set_xlabel(\"bacterial dry \\n weight (g/day)\")\n", "#femrentation products\n", "axs[2,0].barh(1,BRD[\"FP_fromfeces\"],height=0.7,xerr=BRD[\"FP_fromfeces_error\"],color=[colorrefdiet,colorrefdiet],capsize=capsize)\n", "axs[2,0].set_xlabel(\"fermentation \\n products (mmol/day\")\n", "\n", "axs[0,0].set_xlim(0,40)\n", "axs[0,1].set_xlim(0,60)\n", "axs[1,0].set_xlim(0,40)\n", "axs[1,1].set_xlim(0,35)\n", "axs[2,0].set_xlim(0,1100)\n", "axs[2,1].set_xlim(0,1100)\n", "\n", "\n", "axs[0,0].set_yticks([])\n", "axs[1,0].set_yticks([])\n", "axs[2,0].set_yticks([])\n", "axs[0,1].set_yticks([])\n", "axs[1,1].set_yticks([])\n", "axs[2,1].set_yticks([])\n", "\n", "axs[1,0].set_xticks([0,10,20,30])\n", "axs[1,1].set_xticks([0,10,20,30])\n", "\n", "ym=0.5\n", "ymax=1.5\n", "axs[0,0].set_ylim(ym,ymax)\n", "axs[1,0].set_ylim(ym,ymax)\n", "axs[2,0].set_ylim(ym,ymax)\n", "axs[0,1].set_ylim(ym,ymax)\n", "axs[1,1].set_ylim(ym,ymax)\n", "axs[2,1].set_ylim(ym,ymax)\n", "\n", "axs[0,0].spines[['right', 'top']].set_visible(False)\n", "axs[1,0].spines[['right', 'top']].set_visible(False)\n", "axs[2,0].spines[['right', 'top']].set_visible(False)\n", "axs[0,1].spines[['right', 'top']].set_visible(False)\n", "axs[1,1].spines[['right', 'top']].set_visible(False)\n", "axs[2,1].spines[['right', 'top']].set_visible(False)\n", "\n", "\n", "fig.tight_layout()\n", "fig.savefig(\"plot_output_figure2/BRITISH_mainnumbers.pdf\")\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Fermentation products in feces"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'energy': 2275.0, 'carbohydrates': 276.75, 'sugars': 59.0, 'proteins': 72.05000000000001, 'fat': 105.5, 'fiber': 19.9, 'fiber_low': 14.599999999999998, 'fiber_high': 25.2, 'carbLI_standard': 35.670500000000004, 'carbLI_higher': 44.6025, 'carbLI_lower': 25.755000000000003, 'carbLI_error': 9.423749999999998, 'bacwetweight': 117.72413793103448, 'fecalwetmass': 117.72413793103448, 'fecaldrymass': 29.58620689655172, 'fecaldrymassstd': 6.845624030794191, 'fecealfractionbac': 0.546888888888889, 'bacterialdrymass_feces': 16.180367816091955, 'energybacteria_fromfeces': np.float64(101.71752675768855), 'FP_fromfeces': np.float64(467.3659285620218), 'FP_fromfeces_g': [8.035607499560594, 2.7046488116402316, 6.192333401520376, 9.51608312429447, 1.6695716933854843, 4.741614607512687], 'FP_fromfeces_gsum': np.float64(32.859859137913844), 'energyfrac_fromfeces': np.float64(0.04471100077261035), 'energybacteria_fromfeces_error': np.float64(23.53528953407475), 'energyfrac_fromfeces_error': np.float64(0.01034518221278011), 'FP_fromfeces_error': np.float64(108.13861482566425), 'bacterialdrymass_feces_error': 3.743795719952113, 'energy_fromcarbs_standard': np.float64(98.74730465043015), 'FPlist_fromcarbs_standard': [129.903453754712, 29.799922782745863, 130.5998910474014, 102.55558301278339, 21.8796021223617, 38.98006759839011], 'FP_fromcarbs_standard': np.float64(453.7185203183944), 'FP_fromcarbs_g_standard': [7.8009622048779645, 2.6256711963877377, 6.011512984911887, 9.238206917791528, 1.6208190456224323, 4.603156182693888], 'FP_fromcarbs_gsum_standard': np.float64(31.900328532285435), 'ferm_fromcarbs_cc_standard': np.float64(1038.8323154868044), 'drymass_fromcarbs_standard': 15.707889889004568, 'energyfrac_fromcarbs_standard': np.float64(0.04340540863755171), 'energy_fromcarbs_lower': np.float64(71.29804267593188), 'FPlist_fromcarbs_lower': [93.7935675544948, 21.51629529357928, 94.29641283205514, 74.04771563320492, 15.797624161742212, 28.14459121673476], 'FP_fromcarbs_lower': np.float64(327.59620669181106), 'FP_fromcarbs_g_lower': [5.632491318782521, 1.8958007783172701, 4.340463882659498, 6.6702182242390995, 1.170272200277701, 3.323594776784208], 'FP_fromcarbs_gsum_lower': np.float64(23.0328411810603), 'ferm_fromcarbs_cc_lower': np.float64(750.0631133671424), 'drymass_fromcarbs_lower': 11.341492384219807, 'energy_fromcarbs_higher': np.float64(123.47392539131242), 'FPlist_fromcarbs_higher': [162.43166751502054, 37.261912670622, 163.3024947909819, 128.2358080578537, 27.358320002877377, 48.74079323410646], 'FP_fromcarbs_higher': np.float64(567.3309962714619), 'FP_fromcarbs_g_higher': [9.754346497612014, 3.2831471254085045, 7.516813835228898, 11.551481589851461, 2.026676987493153, 5.755800273015632], 'FP_fromcarbs_gsum_higher': np.float64(39.88826630860966), 'ferm_fromcarbs_cc_higher': np.float64(1298.95903762213), 'drymass_fromcarbs_higher': 19.641192547744666, 'energyfrac_fromcarbs_lower': np.float64(0.031339798978431595), 'energyfrac_fromcarbs_higher': np.float64(0.054274252919258206), 'FP_fromcarbs_standard_error': np.float64(119.86739478982543), 'FP_fromcarbs_gsum_standard_error': np.float64(8.427712563774682), 'drymass_fromcarbs_standard_error': 4.1498500817624295, 'energy_fromcarbs_standard_error': np.float64(26.087941357690273), 'energyfrac_fromcarbs_standard_error': np.float64(0.011467226970413306), 'FP_infeces': 9.041213793103447, 'FP_infeces_error': 9.41793103448276, 'proteins_LI': 7, 'mucus': 4.4, 'mucus_protein': 0.8800000000000001, 'mucus_CH': 3.5200000000000005}\n", "dict_items([('glucose', -10.858721180688017), ('acetate', 12.844363425100488), ('propionate', 2.3112700617108497), ('succinate', 1.983167993607639), ('lactate', 4.184815628937861), ('butyrate', 1.1709566518481773), ('formate', 7.269982549466864), ('maltose', -1.6243897980581266), ('uptake', 14.107500776804269), ('uptake_carbon', 84.6450046608256), ('total_secretion', 29.76455631067188), ('total_secretion_carbon', 62.7521949917264)])\n", "total secretion feces mmol/day:\n", "9.041213793103447\n", "different fp products (ace,but,for,lac,suc,prop, mmol/day):\n", "[3.062188506222137, 0.2791644772030259, 1.7332160626900643, 0.997690106973287, 0.4728014954844224, 0.5510233853952884]\n", "7.096084033968225\n", "C in different products mmol/day\n", "14.96057405759457\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 230x350 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#how much mmol of fermentation products in feces\n", "#typical value is 80mmol/l (or 76.8 mmol/l as reported in the following)\n", "#https://pubmed.ncbi.nlm.nih.gov/6740214/\n", "print(BRD)\n", "\n", "BRD[\"FP_infeces\"]=BRD[\"fecalwetmass\"]*76.8/1000.\n", "ferminfeces_upper=BRD[\"fecalwetmass\"]*190/1000.\n", "ferminfeces_lower=BRD[\"fecalwetmass\"]*30/1000.\n", "BRD[\"FP_infeces_error\"]=(ferminfeces_upper-ferminfeces_lower)/2.\n", "ferminfeces_g=BRD[\"FP_infeces\"]*0.08 #assume roughly average\n", "\n", "\n", "with open(os.path.join(\"data_analysisresults/average_excretion/av_YCA.json\")) as f:\n", "        yielddict2 = json.load(f)\n", "print(yielddict2.items())\n", "\n", "### read in json file with secretion data\n", "fpinfeces_list=[]\n", "sum_fp=0\n", "sum_c=0\n", "subc=-1\n", "ccount=[2,4,1,3,4,2]\n", "for sub in [\"acetate\",\"butyrate\",\"formate\",\"lactate\",\"succinate\",\"propionate\"]:\n", "    subc=subc+1\n", "    fpinfeces_list.append(yielddict2[sub]*BRD[\"FP_infeces\"]/yielddict[\"total_secretion\"])\n", "    sum_fp=sum_fp+fpinfeces_list[-1]\n", "    sum_c=sum_c+fpinfeces_list[-1]*ccount[subc]\n", "print(\"total secretion feces mmol/day:\")\n", "print(BRD[\"FP_infeces\"])\n", "print(\"different fp products (ace,but,for,lac,suc,prop, mmol/day):\")\n", "print(fpinfeces_list)\n", "print(sum_fp)\n", "print(\"C in different products mmol/day\")\n", "print(sum_c)\n", "\n", "\n", "\n", "###calculate carbon content of FP in feces\n", "###assume same consumption as secreted by bacteria\n", "\n", "#plot energy fraction for both types of measurements\n", "fig, axs = plt.subplots(1,1, figsize=(2.3,3.5))\n", "axs=[axs]\n", "axs[0].bar([1,2,3],[BRD[\"FP_fromfeces\"],BRD[\"FP_fromcarbs_standard\"],BRD[\"FP_infeces\"]],color=['k','k','brown'])\n", "        \n", "#    databox,labels=[\"bacteria\"],vert=True,showfliers=True, widths=[0.7]*1,patch_artist=True,)#,\"Mi<PERSON> (autoclaved lab coy)\"])\n", "axs[0].set_ylabel('ferment. products (mmol/day)')\n", "axs[0].set_xticks([1,2,3])\n", "axs[0].set_xticklabels([\"approx. via fecal mass\",\"approx. via carbs\",\"in feces\"],rotation=90)\n", "#axs[0].set_ylim(0,280)\n", "\n", "fig.tight_layout()\n", "fig.savefig(\"plot_output_figure2/BRITISH_ferm_includeinfeces.pdf\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Consideration of ATP"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'excretionBg' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[30], line 15\u001b[0m\n\u001b[1;32m      6\u001b[0m ATPgain\u001b[38;5;241m=\u001b[39m[\u001b[38;5;241m2\u001b[39m,\u001b[38;5;241m2\u001b[39m,\u001b[38;5;241m0\u001b[39m,\u001b[38;5;241m2\u001b[39m,\u001b[38;5;241m2\u001b[39m]\u001b[38;5;66;03m#ATP gain for the cell per excreted acetate, butyrate, lactate, propinonate, succinate\u001b[39;00m\n\u001b[1;32m      7\u001b[0m \u001b[38;5;66;03m#most of the pathways give 2ATP per secretion of a fermentation product, but lactate does not give any atp (but helps with the redox balance)\u001b[39;00m\n\u001b[1;32m      8\u001b[0m \n\u001b[1;32m      9\u001b[0m \u001b[38;5;66;03m#####\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     13\u001b[0m \u001b[38;5;66;03m#excretionBg fermentation products released (in mmol/g)\u001b[39;00m\n\u001b[1;32m     14\u001b[0m \u001b[38;5;66;03m#ATP release per fermentation\u001b[39;00m\n\u001b[0;32m---> 15\u001b[0m ATPyieldc\u001b[38;5;241m=\u001b[39mnp\u001b[38;5;241m.\u001b[39marray([np\u001b[38;5;241m.\u001b[39msum(ATPgain\u001b[38;5;241m*\u001b[39m\u001b[43mexcretionBg\u001b[49m),np\u001b[38;5;241m.\u001b[39msum(ATPgain\u001b[38;5;241m*\u001b[39mexcretionEg)])\u001b[38;5;66;03m#in mmol ATP/g\u001b[39;00m\n\u001b[1;32m     16\u001b[0m \u001b[38;5;66;03m#convert to g/mol ATP\u001b[39;00m\n\u001b[1;32m     17\u001b[0m ATPyieldc\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m1000.\u001b[39m\u001b[38;5;241m/\u001b[39mATPyieldc\n", "\u001b[0;31mNameError\u001b[0m: name 'excretionBg' is not defined"]}], "source": ["\n", "\n", "####\n", "#look into pathways, how many ATP are obtained per fermentation product. This varies with the metabolic pathways involves. And in principle it also depends on the type of sugar involved. But things simplify as glucose and other substrates are mostly utilized via glycolysis.\n", "#considering glycolysis, the first step is to go from 1 glucose \n", "####\n", "#how much ATP is released per fermentation pathway\n", "ATPgain=[2,2,0,2,2]#ATP gain for the cell per excreted acetate, butyrate, lactate, propinonate, succinate\n", "#most of the pathways give 2ATP per secretion of a fermentation product, but lactate does not give any atp (but helps with the redox balance)\n", "\n", "#####\n", "#use excretion numbers to estimate ATP\n", "#####\n", "\n", "#excretionBg fermentation products released (in mmol/g)\n", "#ATP release per fermentation\n", "ATPyieldc=np.array([np.sum(ATPgain*excretionBg),np.sum(ATPgain*excretionEg)])#in mmol ATP/g\n", "#convert to g/mol ATP\n", "ATPyieldc=1000./ATPyieldc\n", "print(\"ATP yield estimated via excretion of fermentation products (g/mol ATP)\")\n", "print(ATPyieldc)\n", "\n", "#B.theta and E.rectale yield in OD/mM\n", "yield_glucose=np.array([0.148, 0.094])\n", "yield_glucose_std=np.array([0.046,0.009])\n", "#conversion in g/mmol\n", "conversitionOD=np.array([0.507,0.5,0.494,0.522,0.516,0.513]).mean() #in g/(l*OD) #this is based on <PERSON><PERSON> et al 2015 (inflating cell size), for our spec\n", "yield_glucose=1000*yield_glucose*conversitionOD #in g/mol=OD l/mol *g/l/OD=g/mol; (1000 to coonvert mmol to mol)\n", "yield_glucose_std=1000*yield_glucose_std*conversitionOD\n", "print(\"measred yields in g/mol glucose for B. theta and E. rectale\")\n", "print(yield_glucose) #yield in g dryweight /mol glucose\n", "\n", "#remove the minimal amount which is needed for biomass accumulation\n", "#remove fractioon of glucose needed to make biomass (estimated via carbon requirement)\n", "#start with fraction of carbon in dry mass 0.47 g C/g cells -> 0.47/12=0.039 mol carb /g cells (12g/mol carb) -> 0.039/6=0.0065 mol glu/g cells (6 c per glucose) --> 25.6 g cells/mol glu\n", "print('yield corrected for biomass')\n", "yield_glucose_biomasscorrected=yield_glucose-25.6\n", "print(yield_glucose_biomasscorrected)\n", "print(\"Calculate ATP yield (g/mol ATP)\")\n", "#print(yield_glucose_biomasscorrected/ATPperGlu)\n", "\n", "####\n", "#compare with theoretical estimation of yield\n", "####\n", "#Stouthamer 1973 calculate as max yield: 28.5 g/mol ATP or 35 mmol ATP/g \n", "#other authors calculate lower yield. E.g. 14.7 g/mol ATP or 68 mmol ATP/g. <PERSON> et al. 2007\n", "#check also <PERSON><PERSON><PERSON> et al. 1990\n", "#some difference in treating the metabolic costs of NAD(H), NADP(H), and FAD(H2).\n", "\n", "print('theoretical yield estimated for E. coli in g/mol ATP')\n", "print(\"between 15 and 28 depending on authors\")\n", "\n", "####\n", "#compare with experimental values from E. coli\n", "####\n", "#see also very useful notes from <PERSON>'s group https://openwetware.org/wiki/Ecoli_ATP_requirement\n", "#experimenta yield\n", "print('experimental yield estimated for E. coli in g/mol ATP')\n", "print(10)\n", "#experimentally estimated for E. coli as YATP_max (no maintaneance energy) for growth in anaerobic conditions\n", "#approx 10.3 grams of cells per mole of ATP (<PERSON><PERSON><PERSON><PERSON> and <PERSON> 1975). Other reported values vary a bit, e.g. ~8g/mol ATP\n", "\n", "#####\n", "#use difference in ethnalpies\n", "#####\n", "\n", "#start with glucose: yield_glucose in g cells per mol glucose\n", "H_glu=0.6787763*1000# kcal/mol\n", "\n", "energy_consumed=H_glu/yield_glucose_biomasscorrected\n", "print(\"energy consumbed by different strains\")\n", "print(energy_consumed)\n", "#excretionBg fermentation products released (in mmol/g)\n", "#enthalpie in kcal per mmol\n", "energy_released=np.sum(excretionBg*H),np.sum(excretionEg*H) #energy released in kcal per g\n", "print(\"energy released by different strains\")\n", "print(energy_released)\n", "print(\"net energy needed for growth\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Save numbers to dict"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'energy': 2275.0, 'carbohydrates': 276.75, 'sugars': 59.0, 'proteins': 72.05000000000001, 'fat': 105.5, 'fiber': 19.9, 'fiber_low': 14.599999999999998, 'fiber_high': 25.2, 'carbLI_standard': 35.670500000000004, 'carbLI_higher': 44.6025, 'carbLI_lower': 25.755000000000003, 'carbLI_error': 9.423749999999998, 'bacwetweight': 117.72413793103448, 'fecalwetmass': 117.72413793103448, 'fecaldrymass': 29.58620689655172, 'fecaldrymassstd': 6.845624030794191, 'fecealfractionbac': 0.546888888888889, 'bacterialdrymass_feces': 16.180367816091955, 'energybacteria_fromfeces': np.float64(101.71752675768855), 'FP_fromfeces': np.float64(467.3659285620218), 'FP_fromfeces_g': [8.035607499560594, 2.7046488116402316, 6.192333401520376, 9.51608312429447, 1.6695716933854843, 4.741614607512687], 'FP_fromfeces_gsum': np.float64(32.859859137913844), 'energyfrac_fromfeces': np.float64(0.04471100077261035), 'energybacteria_fromfeces_error': np.float64(23.53528953407475), 'energyfrac_fromfeces_error': np.float64(0.01034518221278011), 'FP_fromfeces_error': np.float64(108.13861482566425), 'bacterialdrymass_feces_error': 3.743795719952113, 'energy_fromcarbs_standard': np.float64(98.74730465043015), 'FPlist_fromcarbs_standard': [129.903453754712, 29.799922782745863, 130.5998910474014, 102.55558301278339, 21.8796021223617, 38.98006759839011], 'FP_fromcarbs_standard': np.float64(453.7185203183944), 'FP_fromcarbs_g_standard': [7.8009622048779645, 2.6256711963877377, 6.011512984911887, 9.238206917791528, 1.6208190456224323, 4.603156182693888], 'FP_fromcarbs_gsum_standard': np.float64(31.900328532285435), 'ferm_fromcarbs_cc_standard': np.float64(1038.8323154868044), 'drymass_fromcarbs_standard': 15.707889889004568, 'energyfrac_fromcarbs_standard': np.float64(0.04340540863755171), 'energy_fromcarbs_lower': np.float64(71.29804267593188), 'FPlist_fromcarbs_lower': [93.7935675544948, 21.51629529357928, 94.29641283205514, 74.04771563320492, 15.797624161742212, 28.14459121673476], 'FP_fromcarbs_lower': np.float64(327.59620669181106), 'FP_fromcarbs_g_lower': [5.632491318782521, 1.8958007783172701, 4.340463882659498, 6.6702182242390995, 1.170272200277701, 3.323594776784208], 'FP_fromcarbs_gsum_lower': np.float64(23.0328411810603), 'ferm_fromcarbs_cc_lower': np.float64(750.0631133671424), 'drymass_fromcarbs_lower': 11.341492384219807, 'energy_fromcarbs_higher': np.float64(123.47392539131242), 'FPlist_fromcarbs_higher': [162.43166751502054, 37.261912670622, 163.3024947909819, 128.2358080578537, 27.358320002877377, 48.74079323410646], 'FP_fromcarbs_higher': np.float64(567.3309962714619), 'FP_fromcarbs_g_higher': [9.754346497612014, 3.2831471254085045, 7.516813835228898, 11.551481589851461, 2.026676987493153, 5.755800273015632], 'FP_fromcarbs_gsum_higher': np.float64(39.88826630860966), 'ferm_fromcarbs_cc_higher': np.float64(1298.95903762213), 'drymass_fromcarbs_higher': 19.641192547744666, 'energyfrac_fromcarbs_lower': np.float64(0.031339798978431595), 'energyfrac_fromcarbs_higher': np.float64(0.054274252919258206), 'FP_fromcarbs_standard_error': np.float64(119.86739478982543), 'FP_fromcarbs_gsum_standard_error': np.float64(8.427712563774682), 'drymass_fromcarbs_standard_error': 4.1498500817624295, 'energy_fromcarbs_standard_error': np.float64(26.087941357690273), 'energyfrac_fromcarbs_standard_error': np.float64(0.011467226970413306), 'FP_infeces': 9.041213793103447, 'FP_infeces_error': 9.41793103448276, 'proteins_LI': 7, 'mucus': 4.4, 'mucus_protein': 0.8800000000000001, 'mucus_CH': 3.5200000000000005}\n"]}], "source": ["print(BRD)\n", "with open('data_analysisresults/BRD_characteristics.json', 'w') as fp:\n", "    json.dump(BRD, fp)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}