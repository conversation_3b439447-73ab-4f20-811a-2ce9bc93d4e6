
# Analysis pipeline

We use a series of Python scripts to analyze the experimental findings of our cultivation studies. We also estimate fermentation product fluxes based on our experimental data, different dietary data, and key intestinal characteristics. The analysis is divided into different major steps with separate Jupyter scripts for each step. The scripts also contain code to generate the different plots shown in our paper. We recommend to run the scripts in sequential order. Further details and comments regarding the different analysis steps are provided in the corresponding Jupyter scripts.

- Step 1 - Analysis of experimental data.ipynb:
- Step 2 - Plot experimental results  (Fig. 1).ipynb
- Step 2a - distribution_abundance_coverage.ipynb
- Step 2b - Plot different media.ipynb
- Step 2c - Supplementary Cultivationmethod.ipynb
- Step 2d - strain tree.ipynb
- Step 2e - Plot pH variation.ipynb
- Step 3 - Calculations reference diet (Figure 2).ipynb
- Step 3a - Basic characteristics British diet.ipynb
- Step 3b - Fecal weight.ipynb
- Step 4a - Variation with microbiome - analyze variation.ipynb
- Step 4b - Variation with microbiome (Figure 3).ipynb
- Step 4c - Variation with microbiome - bioinformatics pathway analysis.ipynb
- Step 5 - Variation with diet - Figures 4 and 6.ipynb
- Step 5b - mouse data.ipynb
