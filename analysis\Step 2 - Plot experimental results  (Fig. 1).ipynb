{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Plotting of experimental data\n", "\n", "- 2a: plotting of fraction characterized strains represent\n", "- 2b: plotting results for different media\n", "- 2c: additional plots to illustrate method\n", "- 2d: plotting tree illustrating how strains are related\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 4}